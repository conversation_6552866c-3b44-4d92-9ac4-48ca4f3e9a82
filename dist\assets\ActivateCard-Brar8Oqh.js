import{a as D}from"./user-CM_uicz1.js";import{C as b,g as L}from"./constants-DT5j32Dw.js";import{_ as S,b as y,d as h,c as C,a,w as s,r,o as w,j as q,e as _,f as l,L as i,k as U,E as T}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const j={class:"activate-card"},I={key:0,class:"activate-result"},M={class:"card-types-info"},P={class:"card-type-desc"},Y={__name:"ActivateCard",setup($){const c=y(null),u=y(!1),o=h({keyCode:"",phone:""}),k={keyCode:[{required:!0,message:"请输入卡密",trigger:"blur"},{min:16,max:16,message:"卡密长度为16位",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},t=h({success:!1,message:"",cardType:0,activateTime:"",expireTime:""}),x=n=>L(b,n),V=n=>{switch(n){case 1:return"体验卡提供12小时的服务体验，适合新用户试用。";case 2:return"天卡提供1天的服务使用权限，适合短期使用。";case 3:return"周卡提供7天的服务使用权限，性价比较高。";case 4:return"月卡提供30天的服务使用权限，最受欢迎的选择。";case 5:return"季卡提供90天的服务使用权限，长期使用更划算。";case 6:return"年卡提供365天的服务使用权限，长期用户的理想选择。";case 7:return"永久卡提供永久的服务使用权限，一次购买终身使用。";default:return""}},E=async()=>{c.value&&await c.value.validate(async n=>{if(n){u.value=!0;try{const e=await D(o);if(e.code===200)t.success=!0,t.message="卡密激活成功",t.cardType=e.data.cardType||0,t.activateTime=e.data.activateTime||new Date().toLocaleString(),t.expireTime=e.data.expireTime||"未知",T.success("卡密激活成功");else throw new Error(e.message||"激活失败")}catch(e){console.error("激活卡密失败:",e),T.error(e.message||"激活卡密失败，请检查卡密是否正确或已被使用"),t.success=!1,t.message=""}finally{u.value=!1}}})},F=()=>{c.value&&c.value.resetFields(),t.success=!1,t.message=""};return(n,e)=>{const f=r("el-input"),m=r("el-form-item"),v=r("el-button"),N=r("el-form"),R=r("el-divider"),A=r("el-alert"),g=r("el-card"),p=r("el-table-column"),B=r("el-table");return w(),C("div",j,[a(g,{shadow:"hover"},{header:s(()=>e[2]||(e[2]=[l("div",{class:"card-header"},[l("span",null,"激活卡密")],-1)])),default:s(()=>[a(N,{ref_key:"activateFormRef",ref:c,model:o,rules:k,"label-width":"100px",class:"activate-form"},{default:s(()=>[a(m,{label:"卡密",prop:"keyCode"},{default:s(()=>[a(f,{modelValue:o.keyCode,"onUpdate:modelValue":e[0]||(e[0]=d=>o.keyCode=d),placeholder:"请输入卡密",maxlength:"16","show-word-limit":""},null,8,["modelValue"])]),_:1}),a(m,{label:"手机号码",prop:"phone"},{default:s(()=>[a(f,{modelValue:o.phone,"onUpdate:modelValue":e[1]||(e[1]=d=>o.phone=d),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1}),a(m,null,{default:s(()=>[a(v,{type:"primary",loading:u.value,onClick:E},{default:s(()=>e[3]||(e[3]=[_("激活")])),_:1,__:[3]},8,["loading"]),a(v,{onClick:F},{default:s(()=>e[4]||(e[4]=[_("重置")])),_:1,__:[4]})]),_:1})]),_:1},8,["model"]),t.success?(w(),C("div",I,[a(R,null,{default:s(()=>e[5]||(e[5]=[_("激活结果")])),_:1,__:[5]}),a(A,{title:t.message,type:"success",closable:!1,class:"mb-20"},{default:s(()=>[l("p",null,"手机号码: "+i(o.phone),1),l("p",null,"卡密类型: "+i(x(t.cardType)),1),l("p",null,"激活时间: "+i(t.activateTime),1),l("p",null,"到期时间: "+i(t.expireTime),1)]),_:1},8,["title"])])):q("",!0)]),_:1}),a(g,{shadow:"hover",class:"mt-20"},{header:s(()=>e[6]||(e[6]=[l("div",{class:"card-header"},[l("span",null,"卡密说明")],-1)])),default:s(()=>[l("div",M,[a(B,{data:U(b),border:""},{default:s(()=>[a(p,{prop:"label",label:"卡密类型",width:"120"}),a(p,{prop:"duration",label:"有效期",width:"120"}),a(p,{label:"说明"},{default:s(d=>[l("div",P,i(V(d.row.value)),1)]),_:1})]),_:1},8,["data"])])]),_:1})])}}},K=S(Y,[["__scopeId","data-v-6cd91bcb"]]);export{K as default};

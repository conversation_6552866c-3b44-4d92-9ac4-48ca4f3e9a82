import{L as P}from"./admin-CZaj3CWx.js";import{_ as j,b as f,d as w,O as M,c as O,a as e,w as t,r as s,E as Q,P as q,o as I,Q as G,f as g,e as r,i as H,L as i}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const J={class:"distribution-record"},K={class:"pagination-container"},W={class:"user-statistics"},X={__name:"DistributionRecord",setup(Y){const C=f([]),h=f(!1),V=f(!1),D=f(""),N=f(""),c=w({totalAmount:0,totalCount:0,level1Amount:0,level1Count:0,level2Amount:0,level2Count:0,level3Amount:0,level3Count:0}),o=w({userId:"",phone:"",level:"",keyType:""}),u=w({pageNum:1,pageSize:10,total:0}),b=async()=>{h.value=!0;try{const n={...o,pageNum:u.pageNum,pageSize:u.pageSize};Object.keys(n).forEach(_=>{(n[_]===""||n[_]===null||n[_]===void 0)&&delete n[_]});const a=await P(n);C.value=a.data.list||[],u.total=a.data.total||0}catch(n){console.error("获取分销记录列表失败:",n),Q.error("获取分销记录列表失败")}finally{h.value=!1}},k=()=>{u.pageNum=1,b()},z=()=>{o.userId="",o.phone="",o.level="",o.keyType="",u.pageNum=1,b()},S=n=>{u.pageSize=n,b()},U=n=>{u.pageNum=n,b()};return M(()=>{b()}),(n,a)=>{const _=s("el-input"),v=s("el-form-item"),p=s("el-option"),y=s("el-select"),x=s("el-button"),A=s("el-form"),d=s("el-table-column"),F=s("el-table"),T=s("el-pagination"),L=s("el-card"),m=s("el-descriptions-item"),B=s("el-descriptions"),E=s("el-dialog"),R=q("loading");return I(),O("div",J,[e(L,{shadow:"hover"},{header:t(()=>a[5]||(a[5]=[g("div",{class:"card-header"},[g("span",null,"分销记录管理")],-1)])),default:t(()=>[e(A,{inline:!0,model:o,class:"search-form"},{default:t(()=>[e(v,{label:"用户ID"},{default:t(()=>[e(_,{modelValue:o.userId,"onUpdate:modelValue":a[0]||(a[0]=l=>o.userId=l),placeholder:"分销用户ID"},null,8,["modelValue"])]),_:1}),e(v,{label:"手机号"},{default:t(()=>[e(_,{modelValue:o.phone,"onUpdate:modelValue":a[1]||(a[1]=l=>o.phone=l),placeholder:"分销用户手机号"},null,8,["modelValue"])]),_:1}),e(v,{label:"分销级别"},{default:t(()=>[e(y,{modelValue:o.level,"onUpdate:modelValue":a[2]||(a[2]=l=>o.level=l),placeholder:"全部",clearable:""},{default:t(()=>[e(p,{label:"一级分销",value:1}),e(p,{label:"二级分销",value:2}),e(p,{label:"三级分销",value:3})]),_:1},8,["modelValue"])]),_:1}),e(v,{label:"卡密类型"},{default:t(()=>[e(y,{modelValue:o.keyType,"onUpdate:modelValue":a[3]||(a[3]=l=>o.keyType=l),placeholder:"全部",clearable:""},{default:t(()=>[e(p,{label:"体验卡",value:1}),e(p,{label:"天卡",value:2}),e(p,{label:"周卡",value:3}),e(p,{label:"月卡",value:4}),e(p,{label:"季卡",value:5}),e(p,{label:"年卡",value:6}),e(p,{label:"永久卡",value:7})]),_:1},8,["modelValue"])]),_:1}),e(v,null,{default:t(()=>[e(x,{type:"primary",onClick:k},{default:t(()=>a[6]||(a[6]=[r("搜索")])),_:1,__:[6]}),e(x,{onClick:z},{default:t(()=>a[7]||(a[7]=[r("重置")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"]),G((I(),H(F,{data:C.value,style:{width:"100%"}},{default:t(()=>[e(d,{prop:"id",label:"ID",width:"80"}),e(d,{prop:"userId",label:"分销用户ID",width:"100"}),e(d,{prop:"phone",label:"分销用户手机号",width:"120"}),e(d,{prop:"activateUserId",label:"激活用户ID",width:"100"}),e(d,{prop:"activateUserPhone",label:"激活用户手机号",width:"120"}),e(d,{prop:"keyCode",label:"卡密码",width:"150"}),e(d,{prop:"keyTypeName",label:"卡密类型",width:"100"}),e(d,{label:"卡密价格",width:"100"},{default:t(l=>[r(i(l.row.price.toFixed(2)),1)]),_:1}),e(d,{prop:"levelName",label:"分销级别",width:"100"}),e(d,{label:"分销比例",width:"100"},{default:t(l=>[r(i((l.row.ratio*100).toFixed(2))+"% ",1)]),_:1}),e(d,{label:"分销金额",width:"100"},{default:t(l=>[r(i(l.row.amount.toFixed(2)),1)]),_:1}),e(d,{prop:"createTime",label:"创建时间",width:"180"}),e(d,{prop:"remark",label:"备注","min-width":"200"})]),_:1},8,["data"])),[[R,h.value]]),g("div",K,[e(T,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":u.pageNum,"page-sizes":[10,20,50,100],"page-size":u.pageSize,total:u.total,onSizeChange:S,onCurrentChange:U},null,8,["current-page","page-size","total"])])]),_:1}),e(E,{modelValue:V.value,"onUpdate:modelValue":a[4]||(a[4]=l=>V.value=l),title:"用户分销统计",width:"600px"},{default:t(()=>[g("div",W,[g("h3",null,"用户ID: "+i(D.value)+" | 手机号: "+i(N.value),1),e(B,{column:2,border:""},{default:t(()=>[e(m,{label:"总分销金额"},{default:t(()=>{var l;return[r(i(((l=c.totalAmount)==null?void 0:l.toFixed(2))||"0.00"),1)]}),_:1}),e(m,{label:"总分销次数"},{default:t(()=>[r(i(c.totalCount||"0"),1)]),_:1}),e(m,{label:"一级分销金额"},{default:t(()=>{var l;return[r(i(((l=c.level1Amount)==null?void 0:l.toFixed(2))||"0.00"),1)]}),_:1}),e(m,{label:"一级分销次数"},{default:t(()=>[r(i(c.level1Count||"0"),1)]),_:1}),e(m,{label:"二级分销金额"},{default:t(()=>{var l;return[r(i(((l=c.level2Amount)==null?void 0:l.toFixed(2))||"0.00"),1)]}),_:1}),e(m,{label:"二级分销次数"},{default:t(()=>[r(i(c.level2Count||"0"),1)]),_:1}),e(m,{label:"三级分销金额"},{default:t(()=>{var l;return[r(i(((l=c.level3Amount)==null?void 0:l.toFixed(2))||"0.00"),1)]}),_:1}),e(m,{label:"三级分销次数"},{default:t(()=>[r(i(c.level3Count||"0"),1)]),_:1})]),_:1})])]),_:1},8,["modelValue"])])}}},le=j(X,[["__scopeId","data-v-c43f2306"]]);export{le as default};

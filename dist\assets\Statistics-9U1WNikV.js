import{i as c}from"./index-Bb6yjXMn.js";import{n as M}from"./admin-CZaj3CWx.js";import{C as N}from"./constants-DT5j32Dw.js";import{_ as P,b as n,d as j,O as I,T as W,c as Y,a as s,w as a,r as u,E as $,o as q,f as r,L as R,e as E}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const G={class:"statistics"},H={class:"card-header"},J={class:"period-selector"},K={class:"data-value"},Q={class:"data-value"},X={class:"data-value"},Z={__name:"Statistics",setup(ee){const _=n("week"),o=j({newUsers:0,activatedCards:0,generatedCards:0,userTrend:[],cardUsage:[],cardTypeDistribution:[]}),m=n(null),C=n(null),w=n(null),b=n(null),x=n(null),S=n(null);let f=null,p=null,v=null,y=null,h=null,g=null;const D=()=>{k()},k=async()=>{try{const t=await M({period:_.value});Object.assign(o,t.data),T()}catch(t){console.error("获取统计数据失败:",t),$.error("获取统计数据失败"),o.newUsers=120,o.activatedCards=85,o.generatedCards=200,o.userTrend=[{date:"2023-06-01",count:10},{date:"2023-06-02",count:15},{date:"2023-06-03",count:12},{date:"2023-06-04",count:18},{date:"2023-06-05",count:20},{date:"2023-06-06",count:25},{date:"2023-06-07",count:22}],o.cardUsage=[{date:"2023-06-01",activated:5,generated:20},{date:"2023-06-02",activated:8,generated:15},{date:"2023-06-03",activated:12,generated:30},{date:"2023-06-04",activated:10,generated:25},{date:"2023-06-05",activated:15,generated:40},{date:"2023-06-06",activated:20,generated:35},{date:"2023-06-07",activated:15,generated:35}],o.cardTypeDistribution=[{type:1,count:20},{type:2,count:30},{type:3,count:50},{type:4,count:80},{type:5,count:40},{type:6,count:25},{type:7,count:15}],T()}},T=()=>{V(),z(),L(),B()},V=()=>{m.value&&(f=c(m.value),f.setOption({xAxis:{show:!1},yAxis:{show:!1},grid:{left:0,right:0,top:0,bottom:0},series:[{type:"line",data:o.userTrend.map(t=>t.count),smooth:!0,symbol:"none",lineStyle:{color:"#409EFF"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.7)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}}}]})),C.value&&(p=c(C.value),p.setOption({xAxis:{show:!1},yAxis:{show:!1},grid:{left:0,right:0,top:0,bottom:0},series:[{type:"line",data:o.cardUsage.map(t=>t.activated),smooth:!0,symbol:"none",lineStyle:{color:"#67C23A"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(103, 194, 58, 0.7)"},{offset:1,color:"rgba(103, 194, 58, 0.1)"}]}}}]})),w.value&&(v=c(w.value),v.setOption({xAxis:{show:!1},yAxis:{show:!1},grid:{left:0,right:0,top:0,bottom:0},series:[{type:"line",data:o.cardUsage.map(t=>t.generated),smooth:!0,symbol:"none",lineStyle:{color:"#E6A23C"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(230, 162, 60, 0.7)"},{offset:1,color:"rgba(230, 162, 60, 0.1)"}]}}}]}))},z=()=>{b.value&&(y=c(b.value),y.setOption({title:{text:"用户增长趋势"},tooltip:{trigger:"axis"},xAxis:{type:"category",data:o.userTrend.map(t=>t.date)},yAxis:{type:"value"},series:[{name:"新增用户",type:"line",data:o.userTrend.map(t=>t.count),smooth:!0,lineStyle:{color:"#409EFF"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.5)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}}}]}))},L=()=>{x.value&&(h=c(x.value),h.setOption({title:{text:"卡密使用情况"},tooltip:{trigger:"axis"},legend:{data:["激活卡密","生成卡密"]},xAxis:{type:"category",data:o.cardUsage.map(t=>t.date)},yAxis:{type:"value"},series:[{name:"激活卡密",type:"bar",data:o.cardUsage.map(t=>t.activated),itemStyle:{color:"#67C23A"}},{name:"生成卡密",type:"bar",data:o.cardUsage.map(t=>t.generated),itemStyle:{color:"#E6A23C"}}]}))},B=()=>{if(S.value){g=c(S.value);const t=o.cardTypeDistribution.map(e=>{const l=N.find(U=>U.value===e.type);return{name:l?l.label:`类型${e.type}`,value:e.count,itemStyle:{color:l?l.color:"#999"}}});g.setOption({title:{text:"卡密类型分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:t.map(e=>e.name)},series:[{name:"卡密类型",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:t}]})}},O=()=>{[f,p,v,y,h,g].forEach(e=>{e&&e.resize()})};return window.addEventListener("resize",O),I(()=>{k(),setTimeout(()=>{T()},100)}),W(()=>{window.removeEventListener("resize",O),[f,p,v,y,h,g].forEach(e=>{e&&e.dispose()})}),(t,e)=>{const l=u("el-radio-button"),U=u("el-radio-group"),d=u("el-card"),i=u("el-col"),A=u("el-row");return q(),Y("div",G,[s(d,{shadow:"hover"},{header:a(()=>[r("div",H,[e[4]||(e[4]=r("span",null,"统计分析",-1)),r("div",J,[s(U,{modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=F=>_.value=F),onChange:D},{default:a(()=>[s(l,{label:"day"},{default:a(()=>e[1]||(e[1]=[E("日")])),_:1,__:[1]}),s(l,{label:"week"},{default:a(()=>e[2]||(e[2]=[E("周")])),_:1,__:[2]}),s(l,{label:"month"},{default:a(()=>e[3]||(e[3]=[E("月")])),_:1,__:[3]})]),_:1},8,["modelValue"])])])]),default:a(()=>[s(A,{gutter:20,class:"data-overview"},{default:a(()=>[s(i,{span:8},{default:a(()=>[s(d,{shadow:"hover",class:"data-card"},{default:a(()=>[e[5]||(e[5]=r("div",{class:"data-title"},"新增用户",-1)),r("div",K,R(o.newUsers||0),1),r("div",{class:"data-chart",ref_key:"newUsersChartRef",ref:m},null,512)]),_:1,__:[5]})]),_:1}),s(i,{span:8},{default:a(()=>[s(d,{shadow:"hover",class:"data-card"},{default:a(()=>[e[6]||(e[6]=r("div",{class:"data-title"},"激活卡密",-1)),r("div",Q,R(o.activatedCards||0),1),r("div",{class:"data-chart",ref_key:"activatedCardsChartRef",ref:C},null,512)]),_:1,__:[6]})]),_:1}),s(i,{span:8},{default:a(()=>[s(d,{shadow:"hover",class:"data-card"},{default:a(()=>[e[7]||(e[7]=r("div",{class:"data-title"},"生成卡密",-1)),r("div",X,R(o.generatedCards||0),1),r("div",{class:"data-chart",ref_key:"generatedCardsChartRef",ref:w},null,512)]),_:1,__:[7]})]),_:1})]),_:1}),s(A,{gutter:20,class:"chart-row"},{default:a(()=>[s(i,{span:12},{default:a(()=>[s(d,{shadow:"hover",class:"chart-card"},{header:a(()=>e[8]||(e[8]=[r("div",{class:"card-header"},[r("span",null,"用户增长趋势")],-1)])),default:a(()=>[r("div",{class:"chart-container",ref_key:"userTrendChartRef",ref:b},null,512)]),_:1})]),_:1}),s(i,{span:12},{default:a(()=>[s(d,{shadow:"hover",class:"chart-card"},{header:a(()=>e[9]||(e[9]=[r("div",{class:"card-header"},[r("span",null,"卡密使用情况")],-1)])),default:a(()=>[r("div",{class:"chart-container",ref_key:"cardUsageChartRef",ref:x},null,512)]),_:1})]),_:1})]),_:1}),s(A,{gutter:20,class:"chart-row"},{default:a(()=>[s(i,{span:24},{default:a(()=>[s(d,{shadow:"hover",class:"chart-card"},{header:a(()=>e[10]||(e[10]=[r("div",{class:"card-header"},[r("span",null,"卡密类型分布")],-1)])),default:a(()=>[r("div",{class:"chart-container",ref_key:"cardTypeChartRef",ref:S},null,512)]),_:1})]),_:1})]),_:1})]),_:1})])}}},le=P(Z,[["__scopeId","data-v-69ad03e0"]]);export{le as default};

<template>
  <div class="distribution-config">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>分销配置管理</span>
        </div>
      </template>
      
      <el-table 
        :data="configList" 
        style="width: 100%" 
        v-loading="loading"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="level" label="级别" width="80" />
        <el-table-column prop="levelName" label="级别名称" min-width="120" />
        <el-table-column label="分销比例" width="120">
          <template #default="scope">
            {{ (scope.row.ratio * 100).toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="handleEditConfig(scope.row)"
            >编辑</el-button>
            <el-button 
              v-if="scope.row.status === 0"
              size="small" 
              type="success" 
              @click="handleEnableConfig(scope.row)"
            >启用</el-button>
            <el-button 
              v-if="scope.row.status === 1"
              size="small" 
              type="warning" 
              @click="handleDisableConfig(scope.row)"
            >禁用</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 编辑分销配置对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="'编辑分销配置 - ' + currentConfig.levelName"
      width="500px"
    >
      <el-form 
        :model="configForm" 
        :rules="configRules" 
        ref="configFormRef"
        label-width="100px"
      >
        <el-form-item label="分销比例" prop="ratio">
          <el-input-number 
            v-model="configForm.ratio" 
            :min="0" 
            :max="1" 
            :precision="2" 
            :step="0.01" 
            style="width: 200px;"
          />
          <span class="ratio-percent">{{ (configForm.ratio * 100).toFixed(2) }}%</span>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="configForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="configForm.remark" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitConfigForm" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getAllDistributionConfig,
  getDistributionConfigById,
  updateDistributionConfig,
  enableDistributionConfig,
  disableDistributionConfig
} from '../../api/admin'

// 分销配置列表
const configList = ref([])

// 加载状态
const loading = ref(false)

// 提交状态
const submitting = ref(false)

// 对话框显示状态
const dialogVisible = ref(false)

// 当前操作的分销配置
const currentConfig = reactive({
  id: null,
  level: 0,
  levelName: '',
  ratio: 0,
  status: 0,
  statusName: '',
  remark: '',
  createTime: '',
  updateTime: ''
})

// 分销配置表单
const configForm = reactive({
  ratio: 0,
  status: 0,
  remark: ''
})

// 分销配置表单引用
const configFormRef = ref(null)

// 分销配置表单验证规则
const configRules = {
  ratio: [
    { required: true, message: '请输入分销比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 1, message: '分销比例必须在0-1之间', trigger: 'blur' }
  ],
  remark: [
    { max: 200, message: '备注最多200个字符', trigger: 'blur' }
  ]
}

// 获取所有分销配置
const fetchConfigList = async () => {
  loading.value = true
  try {
    const res = await getAllDistributionConfig()
    configList.value = res.data || []
  } catch (error) {
    console.error('获取分销配置列表失败:', error)
    ElMessage.error('获取分销配置列表失败')
  } finally {
    loading.value = false
  }
}

// 编辑分销配置
const handleEditConfig = (row) => {
  Object.assign(currentConfig, row)
  configForm.ratio = row.ratio
  configForm.status = row.status
  configForm.remark = row.remark
  dialogVisible.value = true
}

// 启用分销配置
const handleEnableConfig = async (row) => {
  try {
    await enableDistributionConfig(row.id)
    ElMessage.success('启用分销配置成功')
    fetchConfigList()
  } catch (error) {
    console.error('启用分销配置失败:', error)
    ElMessage.error('启用分销配置失败')
  }
}

// 禁用分销配置
const handleDisableConfig = async (row) => {
  try {
    await disableDistributionConfig(row.id)
    ElMessage.success('禁用分销配置成功')
    fetchConfigList()
  } catch (error) {
    console.error('禁用分销配置失败:', error)
    ElMessage.error('禁用分销配置失败')
  }
}

// 提交分销配置表单
const submitConfigForm = async () => {
  if (!configFormRef.value) return
  
  await configFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        await updateDistributionConfig(currentConfig.id, configForm)
        ElMessage.success('更新分销配置成功')
        dialogVisible.value = false
        fetchConfigList()
      } catch (error) {
        console.error('提交分销配置失败:', error)
        ElMessage.error('提交分销配置失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

onMounted(() => {
  fetchConfigList()
})
</script>

<style scoped>
.distribution-config {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.ratio-percent {
  margin-left: 10px;
  color: #606266;
}
</style> 
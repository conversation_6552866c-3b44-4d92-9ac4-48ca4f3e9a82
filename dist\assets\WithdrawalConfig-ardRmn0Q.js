import{e as $,f as me,u as J,h as ge}from"./withdrawal-Ddpl8I2L.js";import{_ as _e,b as m,d as ye,g as W,W as ve,O as Te,E as r,c as v,a as t,w as o,r as s,P as be,o as p,Q as B,i as T,e as u,L as O,f as g,R as X,S as Y,j as Z,N as Ve}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const we={class:"withdrawal-config"},ke={class:"card-header"},Ce={class:"card-header"},Oe={key:0,class:"instruction-container"},Ie={class:"instruction-preview"},Ne={class:"preview-content"},Ue={key:1,class:"empty-instruction"},he={class:"dialog-footer"},Me={__name:"WithdrawalConfig",setup(Ae){const I=m("amount"),N=m(!1),U=m(!1),b=m(!1),h=m(!1),y=m(!1),f=m(!1),M=m(null),A=m([]),F=m([]),d=m(null),ee=[{value:"AMOUNT",label:"金额选项"},{value:"LIMIT",label:"限制设置"},{value:"INSTRUCTION",label:"提现说明"}],a=ye({id:null,configType:"",configKey:"",configValue:"",sortOrder:1,status:1,remark:""}),le={configValue:[{required:!0,message:"请输入配置值",trigger:"blur"}],sortOrder:[{required:!0,message:"请输入排序顺序",trigger:"blur"}]},D=W({get:()=>a.configType==="AMOUNT"&&parseFloat(a.configValue)||0,set:l=>{a.configValue=l.toString(),!f.value&&a.configType==="AMOUNT"&&(a.configKey=`AMOUNT_${l}`)}}),te=W(()=>!d.value||!d.value.configValue?[]:d.value.configValue.split(`
`)),ae=W(()=>{const l={AMOUNT:"金额选项",LIMIT:"限制设置",INSTRUCTION:"提现说明"}[a.configType]||"配置";return`${f.value?"编辑":"添加"}${l}`});ve(I,l=>{try{l==="amount"?w():l==="limit"?L():l==="instruction"&&k()}catch(e){console.error("切换标签页加载数据失败:",e),r.error("加载数据失败，请刷新页面重试")}});const w=async()=>{N.value=!0;try{const l=await $("AMOUNT");l.code===200?A.value=(l.data||[]).map(e=>{if(e.configValue)try{e.amount=parseFloat(e.configValue)}catch{e.amount=0}else e.amount=0;return e}):r.error(l.message||"获取提现金额选项失败")}catch(l){console.error("获取提现金额选项失败:",l),r.error("获取提现金额选项失败")}finally{N.value=!1}},L=async()=>{U.value=!0;try{const l=await $("LIMIT");l.code===200?F.value=l.data||[]:r.error(l.message||"获取提现限制设置失败")}catch(l){console.error("获取提现限制设置失败:",l),r.error("获取提现限制设置失败")}finally{U.value=!1}},k=async()=>{b.value=!0;try{const l=await $("INSTRUCTION");l.code===200?d.value=l.data&&l.data.length>0?l.data[0]:null:r.error(l.message||"获取提现说明失败")}catch(l){console.error("获取提现说明失败:",l),r.error("获取提现说明失败")}finally{b.value=!1}},oe=()=>{x(),a.configType="AMOUNT",f.value=!1,y.value=!0},ne=()=>{x(),a.configType="LIMIT",f.value=!1,y.value=!0},E=()=>{x(),a.configType="INSTRUCTION",a.configKey="WITHDRAWAL_INSTRUCTION",a.sortOrder=1,f.value=!1,y.value=!0},re=async()=>{if(!d.value){E();return}b.value=!0;try{const l=await J(d.value.id,{configType:"INSTRUCTION",configKey:d.value.configKey,configValue:d.value.configValue,sortOrder:d.value.sortOrder,status:d.value.status,remark:d.value.remark});l.code===200?(r.success("保存提现说明成功"),k()):r.error(l.message||"保存提现说明失败")}catch(l){console.error("保存提现说明失败:",l),r.error("保存提现说明失败")}finally{b.value=!1}},q=l=>{f.value=!0,a.id=l.id,a.configType=l.configType,a.configKey=l.configKey,a.configValue=l.configValue,a.sortOrder=l.sortOrder,a.status=l.status,a.remark=l.remark,y.value=!0},z=l=>{Ve.confirm(`确定要删除该${l.configType==="AMOUNT"?"金额选项":l.configType==="LIMIT"?"限制设置":"提现说明"}吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await me(l.id);e.code===200?(r.success("删除成功"),l.configType==="AMOUNT"?w():l.configType==="LIMIT"?L():l.configType==="INSTRUCTION"&&k()):r.error(e.message||"删除失败")}catch(e){console.error("删除失败:",e),r.error("删除失败")}}).catch(()=>{})},se=async()=>{M.value&&await M.value.validate(async l=>{if(l){h.value=!0;try{const e={configType:a.configType,configKey:a.configKey,configValue:a.configValue,sortOrder:a.sortOrder,status:a.status,remark:a.remark};let i;f.value?i=await J(a.id,e):i=await ge(e),i.code===200?(r.success(`${f.value?"更新":"添加"}成功`),y.value=!1,a.configType==="AMOUNT"?w():a.configType==="LIMIT"?L():a.configType==="INSTRUCTION"&&k()):r.error(i.message||`${f.value?"更新":"添加"}失败`)}catch(e){console.error(`${f.value?"更新":"添加"}失败:`,e),r.error(`${f.value?"更新":"添加"}失败`)}finally{h.value=!1}}})},x=()=>{a.id=null,a.configType="",a.configKey="",a.configValue="",a.sortOrder=1,a.status=1,a.remark=""};return Te(()=>{try{w()}catch(l){console.error("初始化提现配置组件失败:",l),r.error("加载提现配置失败，请刷新页面重试"),A.value=[]}}),(l,e)=>{const i=s("el-button"),c=s("el-table-column"),j=s("el-tag"),H=s("el-table"),K=s("el-card"),R=s("el-tab-pane"),C=s("el-input"),_=s("el-form-item"),P=s("el-form"),ie=s("el-empty"),ue=s("el-tabs"),de=s("el-option"),fe=s("el-select"),Q=s("el-input-number"),G=s("el-radio"),ce=s("el-radio-group"),pe=s("el-dialog"),S=be("loading");return p(),v("div",we,[t(ue,{modelValue:I.value,"onUpdate:modelValue":e[1]||(e[1]=n=>I.value=n),type:"card"},{default:o(()=>[t(R,{label:"提现金额选项",name:"amount"},{default:o(()=>[t(K,{shadow:"hover"},{header:o(()=>[g("div",ke,[e[12]||(e[12]=g("span",null,"提现金额选项管理",-1)),t(i,{type:"primary",size:"small",onClick:oe},{default:o(()=>e[11]||(e[11]=[u("添加金额选项")])),_:1,__:[11]})])]),default:o(()=>[B((p(),T(H,{data:A.value,border:"",style:{width:"100%"}},{default:o(()=>[t(c,{prop:"id",label:"ID",width:"80"}),t(c,{prop:"amount",label:"金额值",width:"150"},{default:o(n=>[u(O(n.row.amount!==void 0&&n.row.amount!==null?Number(n.row.amount).toFixed(2):"0.00")+" 元 ",1)]),_:1}),t(c,{prop:"sortOrder",label:"排序顺序",width:"120"}),t(c,{prop:"status",label:"状态",width:"120"},{default:o(n=>[t(j,{type:n.row.status===1?"success":"danger"},{default:o(()=>[u(O(n.row.statusName||(n.row.status===1?"启用":"禁用")),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"remark",label:"备注"}),t(c,{label:"操作",width:"200",fixed:"right"},{default:o(n=>[t(i,{type:"primary",link:"",onClick:V=>q(n.row)},{default:o(()=>e[13]||(e[13]=[u("编辑")])),_:2,__:[13]},1032,["onClick"]),t(i,{type:"danger",link:"",onClick:V=>z(n.row)},{default:o(()=>e[14]||(e[14]=[u("删除")])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[S,N.value]])]),_:1})]),_:1}),t(R,{label:"提现限制设置",name:"limit"},{default:o(()=>[t(K,{shadow:"hover"},{header:o(()=>[g("div",Ce,[e[16]||(e[16]=g("span",null,"提现限制设置",-1)),t(i,{type:"primary",size:"small",onClick:ne},{default:o(()=>e[15]||(e[15]=[u("添加限制设置")])),_:1,__:[15]})])]),default:o(()=>[B((p(),T(H,{data:F.value,border:"",style:{width:"100%"}},{default:o(()=>[t(c,{prop:"id",label:"ID",width:"80"}),t(c,{prop:"configKey",label:"配置键",width:"200"}),t(c,{prop:"configValue",label:"配置值",width:"150"}),t(c,{prop:"sortOrder",label:"排序顺序",width:"120"}),t(c,{prop:"status",label:"状态",width:"120"},{default:o(n=>[t(j,{type:n.row.status===1?"success":"danger"},{default:o(()=>[u(O(n.row.statusName||(n.row.status===1?"启用":"禁用")),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"remark",label:"备注"}),t(c,{label:"操作",width:"200",fixed:"right"},{default:o(n=>[t(i,{type:"primary",link:"",onClick:V=>q(n.row)},{default:o(()=>e[17]||(e[17]=[u("编辑")])),_:2,__:[17]},1032,["onClick"]),t(i,{type:"danger",link:"",onClick:V=>z(n.row)},{default:o(()=>e[18]||(e[18]=[u("删除")])),_:2,__:[18]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[S,U.value]])]),_:1})]),_:1}),t(R,{label:"提现说明",name:"instruction"},{default:o(()=>[t(K,{shadow:"hover"},{header:o(()=>e[19]||(e[19]=[g("div",{class:"card-header"},[g("span",null,"提现说明设置")],-1)])),default:o(()=>[B((p(),v("div",null,[d.value?(p(),v("div",Oe,[t(P,{"label-width":"100px"},{default:o(()=>[t(_,{label:"提现说明:"},{default:o(()=>[t(C,{modelValue:d.value.configValue,"onUpdate:modelValue":e[0]||(e[0]=n=>d.value.configValue=n),type:"textarea",rows:10,placeholder:"请输入提现说明"},null,8,["modelValue"])]),_:1}),t(_,null,{default:o(()=>[t(i,{type:"primary",onClick:re},{default:o(()=>e[20]||(e[20]=[u("保存提现说明")])),_:1,__:[20]})]),_:1})]),_:1}),g("div",Ie,[e[21]||(e[21]=g("h3",null,"提现说明预览",-1)),g("div",Ne,[(p(!0),v(X,null,Y(te.value,(n,V)=>(p(),v("p",{key:V},O(n),1))),128))])])])):(p(),v("div",Ue,[t(ie,{description:"暂无提现说明配置"},{default:o(()=>[t(i,{type:"primary",onClick:E},{default:o(()=>e[22]||(e[22]=[u("添加提现说明")])),_:1,__:[22]})]),_:1})]))])),[[S,b.value]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(pe,{modelValue:y.value,"onUpdate:modelValue":e[10]||(e[10]=n=>y.value=n),title:ae.value,width:"500px"},{footer:o(()=>[g("span",he,[t(i,{onClick:e[9]||(e[9]=n=>y.value=!1)},{default:o(()=>e[25]||(e[25]=[u("取消")])),_:1,__:[25]}),t(i,{type:"primary",onClick:se,loading:h.value},{default:o(()=>e[26]||(e[26]=[u("确认")])),_:1,__:[26]},8,["loading"])])]),default:o(()=>[t(P,{model:a,"label-width":"120px",rules:le,ref_key:"configFormRef",ref:M},{default:o(()=>[f.value?Z("",!0):(p(),T(_,{key:0,label:"配置类型"},{default:o(()=>[t(fe,{modelValue:a.configType,"onUpdate:modelValue":e[2]||(e[2]=n=>a.configType=n),placeholder:"请选择配置类型",disabled:""},{default:o(()=>[(p(),v(X,null,Y(ee,n=>t(de,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})),a.configType!=="AMOUNT"?(p(),T(_,{key:1,label:"配置键"},{default:o(()=>[t(C,{modelValue:a.configKey,"onUpdate:modelValue":e[3]||(e[3]=n=>a.configKey=n),placeholder:"请输入配置键",disabled:f.value},null,8,["modelValue","disabled"])]),_:1})):Z("",!0),a.configType==="AMOUNT"?(p(),T(_,{key:2,label:"金额值",prop:"configValue"},{default:o(()=>[t(Q,{modelValue:D.value,"onUpdate:modelValue":e[4]||(e[4]=n=>D.value=n),min:1,precision:2,step:10},null,8,["modelValue"])]),_:1})):(p(),T(_,{key:3,label:"配置值",prop:"configValue"},{default:o(()=>[t(C,{modelValue:a.configValue,"onUpdate:modelValue":e[5]||(e[5]=n=>a.configValue=n),placeholder:"请输入配置值"},null,8,["modelValue"])]),_:1})),t(_,{label:"排序顺序",prop:"sortOrder"},{default:o(()=>[t(Q,{modelValue:a.sortOrder,"onUpdate:modelValue":e[6]||(e[6]=n=>a.sortOrder=n),min:1,step:1},null,8,["modelValue"])]),_:1}),t(_,{label:"状态"},{default:o(()=>[t(ce,{modelValue:a.status,"onUpdate:modelValue":e[7]||(e[7]=n=>a.status=n)},{default:o(()=>[t(G,{label:1},{default:o(()=>e[23]||(e[23]=[u("启用")])),_:1,__:[23]}),t(G,{label:0},{default:o(()=>e[24]||(e[24]=[u("禁用")])),_:1,__:[24]})]),_:1},8,["modelValue"])]),_:1}),t(_,{label:"备注"},{default:o(()=>[t(C,{modelValue:a.remark,"onUpdate:modelValue":e[8]||(e[8]=n=>a.remark=n),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Re=_e(Me,[["__scopeId","data-v-36c039f1"]]);export{Re as default};

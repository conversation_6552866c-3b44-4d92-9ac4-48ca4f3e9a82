import{H as q,I as A,J as H,K as J}from"./admin-CZaj3CWx.js";import{_ as K,b as c,d as h,O as P,c as Q,a as t,w as l,r as i,E as n,P as S,o as f,Q as G,i as k,e as u,L as C,j as D,f as _}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const W={class:"distribution-config"},X={class:"ratio-percent"},Y={class:"dialog-footer"},Z={__name:"DistributionConfig",setup(ee){const x=c([]),g=c(!1),b=c(!1),d=c(!1),v=h({id:null,level:0,levelName:"",ratio:0,status:0,statusName:"",remark:"",createTime:"",updateTime:""}),r=h({ratio:0,status:0,remark:""}),w=c(null),N={ratio:[{required:!0,message:"请输入分销比例",trigger:"blur"},{type:"number",min:0,max:1,message:"分销比例必须在0-1之间",trigger:"blur"}],remark:[{max:200,message:"备注最多200个字符",trigger:"blur"}]},p=async()=>{g.value=!0;try{const o=await q();x.value=o.data||[]}catch(o){console.error("获取分销配置列表失败:",o),n.error("获取分销配置列表失败")}finally{g.value=!1}},F=o=>{Object.assign(v,o),r.ratio=o.ratio,r.status=o.status,r.remark=o.remark,d.value=!0},E=async o=>{try{await A(o.id),n.success("启用分销配置成功"),p()}catch(e){console.error("启用分销配置失败:",e),n.error("启用分销配置失败")}},T=async o=>{try{await H(o.id),n.success("禁用分销配置成功"),p()}catch(e){console.error("禁用分销配置失败:",e),n.error("禁用分销配置失败")}},B=async()=>{w.value&&await w.value.validate(async o=>{if(o){b.value=!0;try{await J(v.id,r),n.success("更新分销配置成功"),d.value=!1,p()}catch(e){console.error("提交分销配置失败:",e),n.error("提交分销配置失败")}finally{b.value=!1}}})};return P(()=>{p()}),(o,e)=>{const s=i("el-table-column"),U=i("el-tag"),m=i("el-button"),$=i("el-table"),z=i("el-card"),I=i("el-input-number"),y=i("el-form-item"),L=i("el-switch"),R=i("el-input"),j=i("el-form"),M=i("el-dialog"),O=S("loading");return f(),Q("div",W,[t(z,{shadow:"hover"},{header:l(()=>e[5]||(e[5]=[_("div",{class:"card-header"},[_("span",null,"分销配置管理")],-1)])),default:l(()=>[G((f(),k($,{data:x.value,style:{width:"100%"}},{default:l(()=>[t(s,{prop:"id",label:"ID",width:"80"}),t(s,{prop:"level",label:"级别",width:"80"}),t(s,{prop:"levelName",label:"级别名称","min-width":"120"}),t(s,{label:"分销比例",width:"120"},{default:l(a=>[u(C((a.row.ratio*100).toFixed(2))+"% ",1)]),_:1}),t(s,{label:"状态",width:"100"},{default:l(a=>[t(U,{type:a.row.status===1?"success":"info"},{default:l(()=>[u(C(a.row.statusName),1)]),_:2},1032,["type"])]),_:1}),t(s,{prop:"remark",label:"备注","min-width":"200"}),t(s,{prop:"createTime",label:"创建时间",width:"180"}),t(s,{prop:"updateTime",label:"更新时间",width:"180"}),t(s,{label:"操作",width:"200",fixed:"right"},{default:l(a=>[t(m,{size:"small",type:"primary",onClick:V=>F(a.row)},{default:l(()=>e[6]||(e[6]=[u("编辑")])),_:2,__:[6]},1032,["onClick"]),a.row.status===0?(f(),k(m,{key:0,size:"small",type:"success",onClick:V=>E(a.row)},{default:l(()=>e[7]||(e[7]=[u("启用")])),_:2,__:[7]},1032,["onClick"])):D("",!0),a.row.status===1?(f(),k(m,{key:1,size:"small",type:"warning",onClick:V=>T(a.row)},{default:l(()=>e[8]||(e[8]=[u("禁用")])),_:2,__:[8]},1032,["onClick"])):D("",!0)]),_:1})]),_:1},8,["data"])),[[O,g.value]])]),_:1}),t(M,{modelValue:d.value,"onUpdate:modelValue":e[4]||(e[4]=a=>d.value=a),title:"编辑分销配置 - "+v.levelName,width:"500px"},{footer:l(()=>[_("span",Y,[t(m,{onClick:e[3]||(e[3]=a=>d.value=!1)},{default:l(()=>e[9]||(e[9]=[u("取消")])),_:1,__:[9]}),t(m,{type:"primary",onClick:B,loading:b.value},{default:l(()=>e[10]||(e[10]=[u(" 确认 ")])),_:1,__:[10]},8,["loading"])])]),default:l(()=>[t(j,{model:r,rules:N,ref_key:"configFormRef",ref:w,"label-width":"100px"},{default:l(()=>[t(y,{label:"分销比例",prop:"ratio"},{default:l(()=>[t(I,{modelValue:r.ratio,"onUpdate:modelValue":e[0]||(e[0]=a=>r.ratio=a),min:0,max:1,precision:2,step:.01,style:{width:"200px"}},null,8,["modelValue"]),_("span",X,C((r.ratio*100).toFixed(2))+"%",1)]),_:1}),t(y,{label:"状态",prop:"status"},{default:l(()=>[t(L,{modelValue:r.status,"onUpdate:modelValue":e[1]||(e[1]=a=>r.status=a),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1}),t(y,{label:"备注",prop:"remark"},{default:l(()=>[t(R,{modelValue:r.remark,"onUpdate:modelValue":e[2]||(e[2]=a=>r.remark=a),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},oe=K(Z,[["__scopeId","data-v-dc447af7"]]);export{oe as default};

import{j as ee,k as ae,m as le}from"./admin-CZaj3CWx.js";import{C as D}from"./constants-DT5j32Dw.js";import{_ as te,d as v,b as z,O as oe,c as N,f as _,a as l,w as t,r as s,E as p,P as re,o as f,e as u,R as F,S as K,k as E,i as w,Q as ne,L as C,j as se,N as M}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const ie={class:"card-management-container"},ue={class:"header"},de={class:"header-actions"},pe={class:"pagination"},me={class:"dialog-footer"},ce={__name:"CardManagement",setup(ge){const i=v({keyCode:"",keyType:null,status:null}),n=v({pageNum:1,pageSize:10,total:0}),b=z([]),V=z(!1),c=v({visible:!1,loading:!1}),x=z(null),o=v({keyType:1,experienceHours:24,count:10,price:0,remark:""}),R={keyType:[{required:!0,message:"请选择卡密类型",trigger:"change"}],experienceHours:[{required:!0,message:"请输入体验时长",trigger:"change"}],count:[{required:!0,message:"请输入生成数量",trigger:"change"}],price:[{required:!0,message:"请输入卡密价格",trigger:"change"}]},y=async()=>{V.value=!0;try{const r={...i,pageNum:n.pageNum,pageSize:n.pageSize},e=await ee(r);e.code===200&&e.data?(b.value=e.data.list||[],n.total=e.data.total||0,n.pageNum=e.data.pageNum||1,n.pageSize=e.data.pageSize||10):(p.error(e.message||"获取卡密列表失败"),b.value=[],n.total=0)}catch(r){console.error("获取卡密列表失败:",r),p.error("获取卡密列表失败"),b.value=[],n.total=0}finally{V.value=!1}},j=()=>{n.pageNum=1,y()},q=()=>{Object.keys(i).forEach(r=>{i[r]=""}),i.keyType=null,i.status=null,n.pageNum=1,y()},L=r=>{n.pageSize=r,y()},$=r=>{n.pageNum=r,y()},P=()=>{o.keyType=1,o.experienceHours=24,o.count=10,o.price=0,o.remark="",c.visible=!0},G=async()=>{x.value&&await x.value.validate(async r=>{if(r){c.loading=!0;try{const e=await le(o);e.code===200?(p.success(`成功生成 ${o.count} 张卡密`),c.visible=!1,y()):p.error(e.message||"生成卡密失败")}catch(e){console.error("生成卡密失败:",e),p.error("生成卡密失败")}finally{c.loading=!1}}})},I=r=>{M.confirm("确定要删除该卡密吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await ae(r);e.code===200?(p.success("删除成功"),y()):p.error(e.message||"删除卡密失败")}catch(e){console.error("删除卡密失败:",e),p.error("删除卡密失败")}}).catch(()=>{})},O=r=>{navigator.clipboard.writeText(r).then(()=>{p.success("已复制到剪贴板")}).catch(e=>{console.error("复制失败:",e),p.error("复制失败")})},A=()=>{M.confirm("导出功能暂未实现，请联系管理员","提示",{confirmButtonText:"确定",type:"info",showCancelButton:!1})},Q=r=>{switch(r){case 1:return"info";case 2:return"warning";case 3:return"success";case 4:return"primary";case 5:return"";case 6:return"danger";case 7:return"danger";default:return"info"}};return oe(()=>{y()}),(r,e)=>{const g=s("el-button"),S=s("el-input"),m=s("el-form-item"),k=s("el-option"),T=s("el-select"),U=s("el-form"),B=s("el-card"),d=s("el-table-column"),H=s("el-tag"),Y=s("el-table"),J=s("el-pagination"),h=s("el-input-number"),W=s("el-dialog"),X=re("loading");return f(),N("div",ie,[_("div",ue,[e[14]||(e[14]=_("h2",null,"卡密列表",-1)),_("div",de,[l(g,{type:"primary",onClick:P},{default:t(()=>e[12]||(e[12]=[u("生成卡密")])),_:1,__:[12]}),l(g,{type:"success",onClick:A},{default:t(()=>e[13]||(e[13]=[u("导出卡密")])),_:1,__:[13]})])]),l(B,{class:"search-card"},{default:t(()=>[l(U,{model:i,inline:""},{default:t(()=>[l(m,{label:"卡密码"},{default:t(()=>[l(S,{modelValue:i.keyCode,"onUpdate:modelValue":e[0]||(e[0]=a=>i.keyCode=a),placeholder:"输入卡密码",clearable:""},null,8,["modelValue"])]),_:1}),l(m,{label:"卡密类型"},{default:t(()=>[l(T,{modelValue:i.keyType,"onUpdate:modelValue":e[1]||(e[1]=a=>i.keyType=a),placeholder:"选择卡密类型",clearable:""},{default:t(()=>[(f(!0),N(F,null,K(E(D),a=>(f(),w(k,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"卡密状态"},{default:t(()=>[l(T,{modelValue:i.status,"onUpdate:modelValue":e[2]||(e[2]=a=>i.status=a),placeholder:"选择卡密状态",clearable:""},{default:t(()=>[l(k,{label:"未激活",value:0}),l(k,{label:"已激活",value:1})]),_:1},8,["modelValue"])]),_:1}),l(m,null,{default:t(()=>[l(g,{type:"primary",onClick:j},{default:t(()=>e[15]||(e[15]=[u("搜索")])),_:1,__:[15]}),l(g,{onClick:q},{default:t(()=>e[16]||(e[16]=[u("重置")])),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),_:1}),l(B,{class:"card-list"},{default:t(()=>[ne((f(),w(Y,{data:b.value,border:"",style:{width:"100%"}},{default:t(()=>[l(d,{prop:"id",label:"ID",width:"80"}),l(d,{prop:"keyCode",label:"卡密码",width:"220"}),l(d,{label:"类型",width:"120"},{default:t(a=>[l(H,{type:Q(a.row.keyType)},{default:t(()=>[u(C(a.row.keyTypeName),1)]),_:2},1032,["type"])]),_:1}),l(d,{label:"状态",width:"120"},{default:t(a=>[l(H,{type:a.row.status===0?"success":"warning"},{default:t(()=>[u(C(a.row.statusName),1)]),_:2},1032,["type"])]),_:1}),l(d,{prop:"experienceHours",label:"体验时长(小时)",width:"120"},{default:t(a=>[u(C(a.row.keyType===1?a.row.experienceHours:"-"),1)]),_:1}),l(d,{label:"价格",width:"100"},{default:t(a=>[u(C(a.row.price?a.row.price.toFixed(2):"-"),1)]),_:1}),l(d,{prop:"activateTime",label:"激活时间",width:"180"}),l(d,{prop:"expireTime",label:"到期时间",width:"180"}),l(d,{prop:"createTime",label:"创建时间",width:"180"}),l(d,{prop:"remark",label:"备注",width:"120"}),l(d,{label:"操作",fixed:"right",width:"150"},{default:t(a=>[l(g,{type:"danger",size:"small",onClick:Z=>I(a.row.id),disabled:a.row.status!==0},{default:t(()=>e[17]||(e[17]=[u(" 删除 ")])),_:2,__:[17]},1032,["onClick","disabled"]),l(g,{type:"primary",size:"small",onClick:Z=>O(a.row.keyCode)},{default:t(()=>e[18]||(e[18]=[u(" 复制 ")])),_:2,__:[18]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,V.value]]),_("div",pe,[l(J,{"current-page":n.pageNum,"onUpdate:currentPage":e[3]||(e[3]=a=>n.pageNum=a),"page-size":n.pageSize,"onUpdate:pageSize":e[4]||(e[4]=a=>n.pageSize=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:n.total,onSizeChange:L,onCurrentChange:$},null,8,["current-page","page-size","total"])])]),_:1}),l(W,{modelValue:c.visible,"onUpdate:modelValue":e[11]||(e[11]=a=>c.visible=a),title:"生成卡密",width:"500px"},{footer:t(()=>[_("div",me,[l(g,{onClick:e[10]||(e[10]=a=>c.visible=!1)},{default:t(()=>e[19]||(e[19]=[u("取消")])),_:1,__:[19]}),l(g,{type:"primary",onClick:G,loading:c.loading},{default:t(()=>e[20]||(e[20]=[u(" 生成 ")])),_:1,__:[20]},8,["loading"])])]),default:t(()=>[l(U,{model:o,rules:R,ref_key:"generateFormRef",ref:x,"label-width":"100px"},{default:t(()=>[l(m,{label:"卡密类型",prop:"keyType"},{default:t(()=>[l(T,{modelValue:o.keyType,"onUpdate:modelValue":e[5]||(e[5]=a=>o.keyType=a),placeholder:"选择卡密类型",style:{width:"100%"}},{default:t(()=>[(f(!0),N(F,null,K(E(D),a=>(f(),w(k,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o.keyType===1?(f(),w(m,{key:0,label:"体验时长",prop:"experienceHours"},{default:t(()=>[l(h,{modelValue:o.experienceHours,"onUpdate:modelValue":e[6]||(e[6]=a=>o.experienceHours=a),min:1,max:720,style:{width:"100%"}},null,8,["modelValue"])]),_:1})):se("",!0),l(m,{label:"生成数量",prop:"count"},{default:t(()=>[l(h,{modelValue:o.count,"onUpdate:modelValue":e[7]||(e[7]=a=>o.count=a),min:1,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(m,{label:"卡密价格",prop:"price"},{default:t(()=>[l(h,{modelValue:o.price,"onUpdate:modelValue":e[8]||(e[8]=a=>o.price=a),min:0,max:1e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(m,{label:"备注",prop:"remark"},{default:t(()=>[l(S,{modelValue:o.remark,"onUpdate:modelValue":e[9]||(e[9]=a=>o.remark=a),type:"textarea",placeholder:"可选备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},ke=te(ce,[["__scopeId","data-v-24556e80"]]);export{ke as default};

import{g as I,u as h}from"./user-CM_uicz1.js";import{_ as x,b as _,d as C,O as E,c as F,a as r,w as s,r as n,E as p,o as N,e as f,f as c}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const T={class:"profile-container"},B={__name:"Profile",setup(R){const i=_(null),u=_(!1),m=localStorage.getItem("userId"),o=C({id:m,username:"",email:"",phone:"",createTime:"",oldPassword:"",newPassword:"",confirmPassword:""}),g={email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],oldPassword:[{min:6,message:"密码长度不能小于6个字符",trigger:"blur"}],newPassword:[{validator:(d,e,l)=>{e===""?l():e.length<6?l(new Error("密码长度不能小于6个字符")):(o.confirmPassword!==""&&i.value.validateField("confirmPassword"),l())},trigger:"blur"}],confirmPassword:[{validator:(d,e,l)=>{o.newPassword===""?l():e===""?l(new Error("请再次输入密码")):e!==o.newPassword?l(new Error("两次输入密码不一致")):l()},trigger:"blur"}]},w=async()=>{if(m)try{const d=await I(m),{password:e,...l}=d.data;Object.assign(o,l)}catch(d){console.error("获取用户信息失败:",d),p.error("获取用户信息失败")}},V=async()=>{i.value&&await i.value.validate(async d=>{if(d){u.value=!0;try{const e={id:o.id,email:o.email,phone:o.phone};o.oldPassword&&o.newPassword&&(e.oldPassword=o.oldPassword,e.password=o.newPassword),await h(e),p.success("个人资料更新成功"),o.oldPassword="",o.newPassword="",o.confirmPassword=""}catch(e){console.error("更新用户信息失败:",e),p.error("更新用户信息失败，请检查原密码是否正确")}finally{u.value=!1}}})},v=()=>{o.oldPassword="",o.newPassword="",o.confirmPassword="",w()};return E(()=>{w()}),(d,e)=>{const l=n("el-input"),t=n("el-form-item"),b=n("el-divider"),P=n("el-button"),y=n("el-form"),U=n("el-card");return N(),F("div",T,[r(U,{shadow:"hover"},{header:s(()=>e[7]||(e[7]=[c("div",{class:"card-header"},[c("span",null,"个人资料")],-1)])),default:s(()=>[r(y,{ref_key:"profileFormRef",ref:i,model:o,rules:g,"label-width":"100px",class:"profile-form"},{default:s(()=>[r(t,{label:"用户名"},{default:s(()=>[r(l,{modelValue:o.username,"onUpdate:modelValue":e[0]||(e[0]=a=>o.username=a),disabled:""},null,8,["modelValue"])]),_:1}),r(t,{label:"邮箱",prop:"email"},{default:s(()=>[r(l,{modelValue:o.email,"onUpdate:modelValue":e[1]||(e[1]=a=>o.email=a),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),r(t,{label:"手机号码",prop:"phone"},{default:s(()=>[r(l,{modelValue:o.phone,"onUpdate:modelValue":e[2]||(e[2]=a=>o.phone=a),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1}),r(t,{label:"注册时间"},{default:s(()=>[r(l,{modelValue:o.createTime,"onUpdate:modelValue":e[3]||(e[3]=a=>o.createTime=a),disabled:""},null,8,["modelValue"])]),_:1}),r(b,null,{default:s(()=>e[8]||(e[8]=[f("修改密码")])),_:1,__:[8]}),r(t,{label:"原密码",prop:"oldPassword"},{default:s(()=>[r(l,{modelValue:o.oldPassword,"onUpdate:modelValue":e[4]||(e[4]=a=>o.oldPassword=a),type:"password",placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])]),_:1}),r(t,{label:"新密码",prop:"newPassword"},{default:s(()=>[r(l,{modelValue:o.newPassword,"onUpdate:modelValue":e[5]||(e[5]=a=>o.newPassword=a),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),r(t,{label:"确认密码",prop:"confirmPassword"},{default:s(()=>[r(l,{modelValue:o.confirmPassword,"onUpdate:modelValue":e[6]||(e[6]=a=>o.confirmPassword=a),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),r(t,null,{default:s(()=>[r(P,{type:"primary",loading:u.value,onClick:V},{default:s(()=>e[9]||(e[9]=[f("保存修改")])),_:1,__:[9]},8,["loading"]),r(P,{onClick:v},{default:s(()=>e[10]||(e[10]=[f("重置")])),_:1,__:[10]})]),_:1})]),_:1},8,["model"])]),_:1})])}}},S=x(B,[["__scopeId","data-v-69430cfa"]]);export{S as default};

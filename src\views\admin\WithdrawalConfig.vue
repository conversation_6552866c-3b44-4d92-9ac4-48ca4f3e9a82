<template>
  <div class="withdrawal-config">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="提现金额选项" name="amount">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>提现金额选项管理</span>
              <el-button type="primary" size="small" @click="handleAddAmount">添加金额选项</el-button>
            </div>
          </template>
          
          <el-table
            v-loading="amountLoading"
            :data="amountOptions"
            border
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="amount" label="金额值" width="150">
              <template #default="scope">
                {{ (scope.row.amount !== undefined && scope.row.amount !== null) ? Number(scope.row.amount).toFixed(2) : '0.00' }} 元
              </template>
            </el-table-column>
            <el-table-column prop="sortOrder" label="排序顺序" width="120" />
            <el-table-column prop="status" label="状态" width="120">
              <template #default="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                  {{ scope.row.statusName || (scope.row.status === 1 ? '启用' : '禁用') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="handleEditConfig(scope.row)">编辑</el-button>
                <el-button type="danger" link @click="handleDeleteConfig(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="提现限制设置" name="limit">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>提现限制设置</span>
              <el-button type="primary" size="small" @click="handleAddLimit">添加限制设置</el-button>
            </div>
          </template>
          
          <el-table
            v-loading="limitLoading"
            :data="limitConfigs"
            border
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="configKey" label="配置键" width="200" />
            <el-table-column prop="configValue" label="配置值" width="150" />
            <el-table-column prop="sortOrder" label="排序顺序" width="120" />
            <el-table-column prop="status" label="状态" width="120">
              <template #default="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                  {{ scope.row.statusName || (scope.row.status === 1 ? '启用' : '禁用') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="handleEditConfig(scope.row)">编辑</el-button>
                <el-button type="danger" link @click="handleDeleteConfig(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="提现说明" name="instruction">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>提现说明设置</span>
            </div>
          </template>
          
          <div v-loading="instructionLoading">
            <div v-if="instructionConfig" class="instruction-container">
              <el-form label-width="100px">
                <el-form-item label="提现说明:">
                  <el-input
                    v-model="instructionConfig.configValue"
                    type="textarea"
                    :rows="10"
                    placeholder="请输入提现说明"
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="handleSaveInstruction">保存提现说明</el-button>
                </el-form-item>
              </el-form>
              
              <div class="instruction-preview">
                <h3>提现说明预览</h3>
                <div class="preview-content">
                  <p v-for="(line, index) in instructionLines" :key="index">{{ line }}</p>
                </div>
              </div>
            </div>
            
            <div v-else class="empty-instruction">
              <el-empty description="暂无提现说明配置">
                <el-button type="primary" @click="handleAddInstruction">添加提现说明</el-button>
              </el-empty>
            </div>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 配置表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
    >
      <el-form :model="configForm" label-width="120px" :rules="configRules" ref="configFormRef">
        <el-form-item label="配置类型" v-if="!isEdit">
          <el-select v-model="configForm.configType" placeholder="请选择配置类型" disabled>
            <el-option
              v-for="item in configTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="配置键" v-if="configForm.configType !== 'AMOUNT'">
          <el-input v-model="configForm.configKey" placeholder="请输入配置键" :disabled="isEdit" />
        </el-form-item>
        
        <el-form-item label="金额值" v-if="configForm.configType === 'AMOUNT'" prop="configValue">
          <el-input-number v-model="amountValue" :min="1" :precision="2" :step="10" />
        </el-form-item>
        
        <el-form-item label="配置值" v-else prop="configValue">
          <el-input v-model="configForm.configValue" placeholder="请输入配置值" />
        </el-form-item>
        
        <el-form-item label="排序顺序" prop="sortOrder">
          <el-input-number v-model="configForm.sortOrder" :min="1" :step="1" />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-radio-group v-model="configForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input v-model="configForm.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitConfig" :loading="submitLoading">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getAllWithdrawalConfigs, 
  getWithdrawalConfigsByType, 
  addWithdrawalConfig, 
  updateWithdrawalConfig, 
  deleteWithdrawalConfig 
} from '../../api/withdrawal'

const activeTab = ref('amount')
const amountLoading = ref(false)
const limitLoading = ref(false)
const instructionLoading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const configFormRef = ref(null)

// 提现金额选项列表
const amountOptions = ref([])
// 提现限制设置列表
const limitConfigs = ref([])
// 提现说明配置
const instructionConfig = ref(null)

// 配置类型选项
const configTypeOptions = [
  { value: 'AMOUNT', label: '金额选项' },
  { value: 'LIMIT', label: '限制设置' },
  { value: 'INSTRUCTION', label: '提现说明' }
]

// 配置表单
const configForm = reactive({
  id: null,
  configType: '',
  configKey: '',
  configValue: '',
  sortOrder: 1,
  status: 1,
  remark: ''
})

// 表单校验规则
const configRules = {
  configValue: [
    { required: true, message: '请输入配置值', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序顺序', trigger: 'blur' }
  ]
}

// 金额值（用于金额选项的输入）
const amountValue = computed({
  get: () => {
    return configForm.configType === 'AMOUNT' ? parseFloat(configForm.configValue) || 0 : 0
  },
  set: (val) => {
    configForm.configValue = val.toString()
    // 如果是新增金额选项，自动生成配置键
    if (!isEdit.value && configForm.configType === 'AMOUNT') {
      configForm.configKey = `AMOUNT_${val}`
    }
  }
})

// 提现说明预览（按行分割）
const instructionLines = computed(() => {
  if (!instructionConfig.value || !instructionConfig.value.configValue) {
    return []
  }
  return instructionConfig.value.configValue.split('\n')
})

// 对话框标题
const dialogTitle = computed(() => {
  const typeText = {
    'AMOUNT': '金额选项',
    'LIMIT': '限制设置',
    'INSTRUCTION': '提现说明'
  }[configForm.configType] || '配置'
  
  return `${isEdit.value ? '编辑' : '添加'}${typeText}`
})

// 监听标签页变化，加载对应数据
watch(activeTab, (newVal) => {
  try {
    if (newVal === 'amount') {
      fetchAmountOptions()
    } else if (newVal === 'limit') {
      fetchLimitConfigs()
    } else if (newVal === 'instruction') {
      fetchInstructionConfig()
    }
  } catch (error) {
    console.error('切换标签页加载数据失败:', error)
    ElMessage.error('加载数据失败，请刷新页面重试')
  }
})

// 获取提现金额选项列表
const fetchAmountOptions = async () => {
  amountLoading.value = true
  try {
    const res = await getWithdrawalConfigsByType('AMOUNT')
    if (res.code === 200) {
      // 确保数据格式正确，处理可能的数据异常
      amountOptions.value = (res.data || []).map(item => {
        // 确保amount字段存在且为数字类型
        if (item.configValue) {
          try {
            item.amount = parseFloat(item.configValue);
          } catch (e) {
            item.amount = 0;
          }
        } else {
          item.amount = 0;
        }
        return item;
      });
    } else {
      ElMessage.error(res.message || '获取提现金额选项失败')
    }
  } catch (error) {
    console.error('获取提现金额选项失败:', error)
    ElMessage.error('获取提现金额选项失败')
  } finally {
    amountLoading.value = false
  }
}

// 获取提现限制设置列表
const fetchLimitConfigs = async () => {
  limitLoading.value = true
  try {
    const res = await getWithdrawalConfigsByType('LIMIT')
    if (res.code === 200) {
      limitConfigs.value = res.data || []
    } else {
      ElMessage.error(res.message || '获取提现限制设置失败')
    }
  } catch (error) {
    console.error('获取提现限制设置失败:', error)
    ElMessage.error('获取提现限制设置失败')
  } finally {
    limitLoading.value = false
  }
}

// 获取提现说明配置
const fetchInstructionConfig = async () => {
  instructionLoading.value = true
  try {
    const res = await getWithdrawalConfigsByType('INSTRUCTION')
    if (res.code === 200) {
      instructionConfig.value = (res.data && res.data.length > 0) ? res.data[0] : null
    } else {
      ElMessage.error(res.message || '获取提现说明失败')
    }
  } catch (error) {
    console.error('获取提现说明失败:', error)
    ElMessage.error('获取提现说明失败')
  } finally {
    instructionLoading.value = false
  }
}

// 添加金额选项
const handleAddAmount = () => {
  resetConfigForm()
  configForm.configType = 'AMOUNT'
  isEdit.value = false
  dialogVisible.value = true
}

// 添加限制设置
const handleAddLimit = () => {
  resetConfigForm()
  configForm.configType = 'LIMIT'
  isEdit.value = false
  dialogVisible.value = true
}

// 添加提现说明
const handleAddInstruction = () => {
  resetConfigForm()
  configForm.configType = 'INSTRUCTION'
  configForm.configKey = 'WITHDRAWAL_INSTRUCTION'
  configForm.sortOrder = 1
  isEdit.value = false
  dialogVisible.value = true
}

// 保存提现说明
const handleSaveInstruction = async () => {
  if (!instructionConfig.value) {
    handleAddInstruction()
    return
  }
  
  instructionLoading.value = true
  try {
    const res = await updateWithdrawalConfig(instructionConfig.value.id, {
      configType: 'INSTRUCTION',
      configKey: instructionConfig.value.configKey,
      configValue: instructionConfig.value.configValue,
      sortOrder: instructionConfig.value.sortOrder,
      status: instructionConfig.value.status,
      remark: instructionConfig.value.remark
    })
    
    if (res.code === 200) {
      ElMessage.success('保存提现说明成功')
      fetchInstructionConfig()
    } else {
      ElMessage.error(res.message || '保存提现说明失败')
    }
  } catch (error) {
    console.error('保存提现说明失败:', error)
    ElMessage.error('保存提现说明失败')
  } finally {
    instructionLoading.value = false
  }
}

// 编辑配置
const handleEditConfig = (row) => {
  isEdit.value = true
  
  configForm.id = row.id
  configForm.configType = row.configType
  configForm.configKey = row.configKey
  configForm.configValue = row.configValue
  configForm.sortOrder = row.sortOrder
  configForm.status = row.status
  configForm.remark = row.remark
  
  dialogVisible.value = true
}

// 删除配置
const handleDeleteConfig = (row) => {
  ElMessageBox.confirm(`确定要删除该${row.configType === 'AMOUNT' ? '金额选项' : row.configType === 'LIMIT' ? '限制设置' : '提现说明'}吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteWithdrawalConfig(row.id)
      
      if (res.code === 200) {
        ElMessage.success('删除成功')
        
        // 刷新对应的数据列表
        if (row.configType === 'AMOUNT') {
          fetchAmountOptions()
        } else if (row.configType === 'LIMIT') {
          fetchLimitConfigs()
        } else if (row.configType === 'INSTRUCTION') {
          fetchInstructionConfig()
        }
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 提交配置表单
const handleSubmitConfig = async () => {
  if (!configFormRef.value) return
  
  await configFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitLoading.value = true
    try {
      const data = {
        configType: configForm.configType,
        configKey: configForm.configKey,
        configValue: configForm.configValue,
        sortOrder: configForm.sortOrder,
        status: configForm.status,
        remark: configForm.remark
      }
      
      let res
      
      if (isEdit.value) {
        res = await updateWithdrawalConfig(configForm.id, data)
      } else {
        res = await addWithdrawalConfig(data)
      }
      
      if (res.code === 200) {
        ElMessage.success(`${isEdit.value ? '更新' : '添加'}成功`)
        dialogVisible.value = false
        
        // 刷新对应的数据列表
        if (configForm.configType === 'AMOUNT') {
          fetchAmountOptions()
        } else if (configForm.configType === 'LIMIT') {
          fetchLimitConfigs()
        } else if (configForm.configType === 'INSTRUCTION') {
          fetchInstructionConfig()
        }
      } else {
        ElMessage.error(res.message || `${isEdit.value ? '更新' : '添加'}失败`)
      }
    } catch (error) {
      console.error(`${isEdit.value ? '更新' : '添加'}失败:`, error)
      ElMessage.error(`${isEdit.value ? '更新' : '添加'}失败`)
    } finally {
      submitLoading.value = false
    }
  })
}

// 重置配置表单
const resetConfigForm = () => {
  configForm.id = null
  configForm.configType = ''
  configForm.configKey = ''
  configForm.configValue = ''
  configForm.sortOrder = 1
  configForm.status = 1
  configForm.remark = ''
}

// 初始化
onMounted(() => {
  try {
    fetchAmountOptions()
  } catch (error) {
    console.error('初始化提现配置组件失败:', error)
    ElMessage.error('加载提现配置失败，请刷新页面重试')
    amountOptions.value = [] // 确保即使加载失败也有一个空数组
  }
})
</script>

<style scoped>
.withdrawal-config {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.instruction-container {
  display: flex;
  gap: 20px;
}

.instruction-container .el-form {
  flex: 1;
}

.instruction-preview {
  flex: 1;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.instruction-preview h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #606266;
}

.preview-content {
  white-space: pre-wrap;
}

.preview-content p {
  margin: 8px 0;
  line-height: 1.5;
}

.empty-instruction {
  padding: 40px 0;
  text-align: center;
}
</style> 
import{s as w}from"./request-YZgFExiB.js";import{_ as N,c as L,f as m,Q as R,a as l,w as n,r as s,P as j,i as q,b as f,d as M,O as A,E as c,N as I,V as Q,o as P,e as _,L as h,j as G,R as H}from"./index-DKz5EXvV.js";const J={name:"AboutUs",setup(){const x=f([]),t=f(!1),k=f(1),e=f(10),O=f(0),b=f(null),u=f(!1),d=f(!1),r=M({id:null,title:"",content:"",sortOrder:0,status:1}),z={title:[{required:!0,message:"请输入标题",trigger:"blur"},{max:100,message:"标题最多100个字符",trigger:"blur"}],content:[{required:!0,message:"请输入内容",trigger:"blur"}],sortOrder:[{required:!0,message:"请输入排序值",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},V=f(!1),C=f(null),g=async()=>{t.value=!0;try{const o=await w("/admin/about-us");o.code===200?(x.value=o.data,O.value=o.data.length):c.error(o.message||"获取数据失败")}catch(o){console.error("获取关于我们列表出错:",o),c.error("获取数据失败")}finally{t.value=!1}},v=o=>{o?Object.keys(r).forEach(i=>{r[i]=o[i]}):Object.keys(r).forEach(i=>{r[i]=i==="sortOrder"?0:i==="status"?1:null}),u.value=!0,Q(()=>{b.value&&b.value.clearValidate()})},S=()=>{u.value=!1},y=async()=>{if(b.value)try{await b.value.validate(),d.value=!0;let o;r.id?o=await w(`/admin/about-us/${r.id}`,{method:"PUT",data:{title:r.title,content:r.content,sortOrder:r.sortOrder,status:r.status}}):o=await w("/admin/about-us",{method:"POST",data:{title:r.title,content:r.content,sortOrder:r.sortOrder,status:r.status}}),o.code===200?(c.success("保存成功"),u.value=!1,g()):c.error(o.message||"保存失败")}catch(o){console.error("保存关于我们信息出错:",o),c.error("保存失败")}finally{d.value=!1}},U=o=>{I.confirm("确定要删除此信息吗？删除后不可恢复。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const i=await w(`/admin/about-us/${o}`,{method:"DELETE"});i.code===200?(c.success("删除成功"),g()):c.error(i.message||"删除失败")}catch(i){console.error("删除关于我们信息出错:",i),c.error("删除失败")}}).catch(()=>{})},T=async(o,i)=>{try{const F=o===1?`/admin/about-us/${i.id}/enable`:`/admin/about-us/${i.id}/disable`,B=await w(F,{method:"PUT"});B.code===200?c.success(`${o===1?"启用":"禁用"}成功`):(i.status=o===1?0:1,c.error(B.message||`${o===1?"启用":"禁用"}失败`))}catch(F){i.status=o===1?0:1,console.error(`${o===1?"启用":"禁用"}关于我们信息出错:`,F),c.error(`${o===1?"启用":"禁用"}失败`)}},D=o=>{C.value=o,V.value=!0},E=o=>{e.value=o,g()},a=o=>{k.value=o,g()},p=o=>o?o.length>50?o.substring(0,50)+"...":o:"";return A(()=>{g()}),{list:x,loading:t,currentPage:k,pageSize:e,total:O,formRef:b,form:r,formRules:z,formDialogVisible:u,submitLoading:d,previewDialogVisible:V,previewData:C,openFormDialog:v,closeFormDialog:S,handleSubmitForm:y,handleDelete:U,handleStatusChange:T,handlePreview:D,handleSizeChange:E,handleCurrentChange:a,truncateContent:p}}},K={class:"about-us-container"},W={class:"actions"},X={class:"pagination-container"},Y={class:"dialog-footer"},Z={class:"preview-title"},$={class:"preview-content"},ee={class:"preview-info"};function te(x,t,k,e,O,b){const u=s("el-button"),d=s("el-table-column"),r=s("el-tooltip"),z=s("el-switch"),V=s("el-table"),C=s("el-pagination"),g=s("el-input"),v=s("el-form-item"),S=s("el-input-number"),y=s("el-radio"),U=s("el-radio-group"),T=s("el-form"),D=s("el-dialog"),E=j("loading");return P(),L("div",K,[t[16]||(t[16]=m("h2",null,"关于我们管理",-1)),m("div",W,[l(u,{type:"primary",onClick:t[0]||(t[0]=a=>e.openFormDialog(null))},{default:n(()=>t[7]||(t[7]=[m("i",{class:"el-icon-plus"},null,-1),_(" 新增信息 ")])),_:1,__:[7]})]),R((P(),q(V,{data:e.list,border:"",style:{width:"100%","margin-top":"15px"}},{default:n(()=>[l(d,{prop:"id",label:"ID",width:"80"}),l(d,{prop:"title",label:"标题","min-width":"120"},{default:n(a=>[l(r,{content:a.row.title,placement:"top","hide-after":0},{default:n(()=>[m("span",null,h(a.row.title),1)]),_:2},1032,["content"])]),_:1}),l(d,{prop:"content",label:"内容","min-width":"200"},{default:n(a=>[l(r,{content:a.row.content,placement:"top","hide-after":0},{default:n(()=>[m("span",null,h(e.truncateContent(a.row.content)),1)]),_:2},1032,["content"])]),_:1}),l(d,{prop:"sortOrder",label:"排序",width:"80"}),l(d,{label:"状态",width:"100"},{default:n(a=>[l(z,{modelValue:a.row.status,"onUpdate:modelValue":p=>a.row.status=p,"active-value":1,"inactive-value":0,onChange:p=>e.handleStatusChange(p,a.row),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(d,{prop:"createTime",label:"创建时间",width:"160"}),l(d,{prop:"updateTime",label:"更新时间",width:"160"}),l(d,{label:"操作",width:"200"},{default:n(a=>[l(u,{type:"primary",size:"small",onClick:p=>e.handlePreview(a.row),icon:"el-icon-view"},{default:n(()=>t[8]||(t[8]=[_(" 预览 ")])),_:2,__:[8]},1032,["onClick"]),l(u,{type:"primary",size:"small",onClick:p=>e.openFormDialog(a.row),icon:"el-icon-edit"},{default:n(()=>t[9]||(t[9]=[_(" 编辑 ")])),_:2,__:[9]},1032,["onClick"]),l(u,{type:"danger",size:"small",onClick:p=>e.handleDelete(a.row.id),icon:"el-icon-delete"},{default:n(()=>t[10]||(t[10]=[_(" 删除 ")])),_:2,__:[10]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[E,e.loading]]),m("div",X,[l(C,{"current-page":e.currentPage,"page-sizes":[10,20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])]),l(D,{title:e.form.id?"编辑信息":"新增信息",modelValue:e.formDialogVisible,"onUpdate:modelValue":t[5]||(t[5]=a=>e.formDialogVisible=a),width:"700px","before-close":e.closeFormDialog},{footer:n(()=>[m("span",Y,[l(u,{onClick:e.closeFormDialog},{default:n(()=>t[14]||(t[14]=[_("取消")])),_:1,__:[14]},8,["onClick"]),l(u,{type:"primary",onClick:e.handleSubmitForm,loading:e.submitLoading},{default:n(()=>t[15]||(t[15]=[_("确定")])),_:1,__:[15]},8,["onClick","loading"])])]),default:n(()=>[l(T,{ref:"formRef",model:e.form,rules:e.formRules,"label-width":"80px"},{default:n(()=>[l(v,{label:"标题",prop:"title"},{default:n(()=>[l(g,{modelValue:e.form.title,"onUpdate:modelValue":t[1]||(t[1]=a=>e.form.title=a),placeholder:"请输入标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(v,{label:"内容",prop:"content"},{default:n(()=>[l(g,{modelValue:e.form.content,"onUpdate:modelValue":t[2]||(t[2]=a=>e.form.content=a),type:"textarea",rows:8,placeholder:"请输入内容",maxlength:"10000","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(v,{label:"排序",prop:"sortOrder"},{default:n(()=>[l(S,{modelValue:e.form.sortOrder,"onUpdate:modelValue":t[3]||(t[3]=a=>e.form.sortOrder=a),min:0,max:9999,style:{width:"200px"}},null,8,["modelValue"]),t[11]||(t[11]=m("span",{class:"form-tip"},"数值越小排序越靠前",-1))]),_:1,__:[11]}),l(v,{label:"状态",prop:"status"},{default:n(()=>[l(U,{modelValue:e.form.status,"onUpdate:modelValue":t[4]||(t[4]=a=>e.form.status=a)},{default:n(()=>[l(y,{label:1},{default:n(()=>t[12]||(t[12]=[_("启用")])),_:1,__:[12]}),l(y,{label:0},{default:n(()=>t[13]||(t[13]=[_("禁用")])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue","before-close"]),l(D,{title:"内容预览",modelValue:e.previewDialogVisible,"onUpdate:modelValue":t[6]||(t[6]=a=>e.previewDialogVisible=a),width:"700px"},{default:n(()=>[e.previewData?(P(),L(H,{key:0},[m("h3",Z,h(e.previewData.title),1),m("div",$,h(e.previewData.content),1),m("div",ee,"更新时间: "+h(e.previewData.updateTime),1)],64)):G("",!0)]),_:1},8,["modelValue"])])}const ae=N(J,[["render",te],["__scopeId","data-v-3113af06"]]);export{ae as default};

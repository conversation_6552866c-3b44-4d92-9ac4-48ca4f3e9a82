<template>
  <div class="distribution-record">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>分销记录管理</span>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户ID">
          <el-input v-model="searchForm.userId" placeholder="分销用户ID" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="分销用户手机号" />
        </el-form-item>
        <el-form-item label="分销级别">
          <el-select v-model="searchForm.level" placeholder="全部" clearable>
            <el-option label="一级分销" :value="1" />
            <el-option label="二级分销" :value="2" />
            <el-option label="三级分销" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="卡密类型">
          <el-select v-model="searchForm.keyType" placeholder="全部" clearable>
            <el-option label="体验卡" :value="1" />
            <el-option label="天卡" :value="2" />
            <el-option label="周卡" :value="3" />
            <el-option label="月卡" :value="4" />
            <el-option label="季卡" :value="5" />
            <el-option label="年卡" :value="6" />
            <el-option label="永久卡" :value="7" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 分销记录表格 -->
      <el-table 
        :data="recordList" 
        style="width: 100%" 
        v-loading="loading"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="userId" label="分销用户ID" width="100" />
        <el-table-column prop="phone" label="分销用户手机号" width="120" />
        <el-table-column prop="activateUserId" label="激活用户ID" width="100" />
        <el-table-column prop="activateUserPhone" label="激活用户手机号" width="120" />
        <el-table-column prop="keyCode" label="卡密码" width="150" />
        <el-table-column prop="keyTypeName" label="卡密类型" width="100" />
        <el-table-column label="卡密价格" width="100">
          <template #default="scope">
            {{ scope.row.price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="levelName" label="分销级别" width="100" />
        <el-table-column label="分销比例" width="100">
          <template #default="scope">
            {{ (scope.row.ratio * 100).toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column label="分销金额" width="100">
          <template #default="scope">
            {{ scope.row.amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="remark" label="备注" min-width="200" />
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户分销统计对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="用户分销统计"
      width="600px"
    >
      <div class="user-statistics">
        <h3>用户ID: {{ currentUserId }} | 手机号: {{ currentUserPhone }}</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="总分销金额">{{ userStatistics.totalAmount?.toFixed(2) || '0.00' }}</el-descriptions-item>
          <el-descriptions-item label="总分销次数">{{ userStatistics.totalCount || '0' }}</el-descriptions-item>
          <el-descriptions-item label="一级分销金额">{{ userStatistics.level1Amount?.toFixed(2) || '0.00' }}</el-descriptions-item>
          <el-descriptions-item label="一级分销次数">{{ userStatistics.level1Count || '0' }}</el-descriptions-item>
          <el-descriptions-item label="二级分销金额">{{ userStatistics.level2Amount?.toFixed(2) || '0.00' }}</el-descriptions-item>
          <el-descriptions-item label="二级分销次数">{{ userStatistics.level2Count || '0' }}</el-descriptions-item>
          <el-descriptions-item label="三级分销金额">{{ userStatistics.level3Amount?.toFixed(2) || '0.00' }}</el-descriptions-item>
          <el-descriptions-item label="三级分销次数">{{ userStatistics.level3Count || '0' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getDistributionRecordList,
  getUserDistributionStatistics
} from '../../api/admin'

// 分销记录列表
const recordList = ref([])

// 加载状态
const loading = ref(false)

// 对话框显示状态
const dialogVisible = ref(false)

// 当前查看的用户ID和手机号
const currentUserId = ref('')
const currentUserPhone = ref('')

// 用户分销统计数据
const userStatistics = reactive({
  totalAmount: 0,
  totalCount: 0,
  level1Amount: 0,
  level1Count: 0,
  level2Amount: 0,
  level2Count: 0,
  level3Amount: 0,
  level3Count: 0
})

// 搜索表单
const searchForm = reactive({
  userId: '',
  phone: '',
  level: '',
  keyType: ''
})

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 获取分销记录列表
const fetchRecordList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const res = await getDistributionRecordList(params)
    recordList.value = res.data.list || []
    pagination.total = res.data.total || 0
  } catch (error) {
    console.error('获取分销记录列表失败:', error)
    ElMessage.error('获取分销记录列表失败')
  } finally {
    loading.value = false
  }
}

// 查看用户分销统计
const viewUserStatistics = async (userId, phone) => {
  currentUserId.value = userId
  currentUserPhone.value = phone
  
  try {
    const res = await getUserDistributionStatistics(userId)
    Object.assign(userStatistics, res.data)
    dialogVisible.value = true
  } catch (error) {
    console.error('获取用户分销统计失败:', error)
    ElMessage.error('获取用户分销统计失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  fetchRecordList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.userId = ''
  searchForm.phone = ''
  searchForm.level = ''
  searchForm.keyType = ''
  pagination.pageNum = 1
  fetchRecordList()
}

// 改变每页显示数量
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchRecordList()
}

// 改变页码
const handleCurrentChange = (page) => {
  pagination.pageNum = page
  fetchRecordList()
}

// 添加行点击事件，查看用户分销统计
const handleRowClick = (row) => {
  viewUserStatistics(row.userId, row.phone)
}

onMounted(() => {
  fetchRecordList()
})
</script>

<style scoped>
.distribution-record {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.user-statistics {
  padding: 10px;
}

.user-statistics h3 {
  margin-bottom: 20px;
}
</style> 
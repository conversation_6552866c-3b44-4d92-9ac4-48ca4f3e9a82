<template>
  <div class="card-key-management">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>卡密管理</span>
          <el-button type="primary" @click="handleCreateBatch">批量生成卡密</el-button>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="卡密">
          <el-input v-model="searchForm.cardKey" placeholder="卡密" clearable />
        </el-form-item>
        
        <el-form-item label="卡密类型">
          <el-select v-model="searchForm.cardType" placeholder="全部类型" clearable>
            <el-option 
              v-for="item in CARD_TYPES" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="卡密状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option 
              v-for="item in CARD_STATUS" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 卡密表格 -->
      <el-table
        v-loading="loading"
        :data="cardKeyList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="cardKey" label="卡密" width="220" />
        <el-table-column prop="cardType" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getCardTypeTag(scope.row.cardType)">
              {{ getCardTypeName(scope.row.cardType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getCardStatusTag(scope.row.status)">
              {{ getCardStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="使用用户" width="150" />
        <el-table-column prop="activateTime" label="激活时间" width="180" />
        <el-table-column prop="expireTime" label="到期时间" width="180" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button 
              type="danger" 
              link 
              :disabled="scope.row.status !== 0"
              @click="handleDeleteCardKey(scope.row)"
            >
              删除
            </el-button>
            <el-button 
              type="primary" 
              link 
              @click="handleCopyCardKey(scope.row.cardKey)"
            >
              复制卡密
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 批量生成卡密对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="批量生成卡密"
      width="500px"
    >
      <el-form :model="cardKeyForm" label-width="100px" :rules="rules" ref="cardKeyFormRef">
        <el-form-item label="卡密类型" prop="cardType">
          <el-select v-model="cardKeyForm.cardType" placeholder="请选择卡密类型" style="width: 100%">
            <el-option 
              v-for="item in CARD_TYPES" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="生成数量" prop="count">
          <el-input-number v-model="cardKeyForm.count" :min="1" :max="100" />
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input v-model="cardKeyForm.remark" type="textarea" :rows="3" placeholder="可选" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCardKeyForm" :loading="submitting">
            生成
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCardKeyList, generateCardKeys, deleteCardKey } from '../../api/admin'
import { CARD_TYPES, CARD_STATUS, getLabelByValue } from '../../utils/constants'

const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const cardKeyFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  cardKey: '',
  cardType: '',
  status: ''
})

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 卡密列表
const cardKeyList = ref([])

// 卡密表单
const cardKeyForm = reactive({
  cardType: '',
  count: 10,
  remark: ''
})

// 表单验证规则
const rules = {
  cardType: [
    { required: true, message: '请选择卡密类型', trigger: 'change' }
  ],
  count: [
    { required: true, message: '请输入生成数量', trigger: 'blur' }
  ]
}

// 获取卡密类型名称
const getCardTypeName = (type) => {
  return getLabelByValue(CARD_TYPES, type)
}

// 获取卡密类型标签类型
const getCardTypeTag = (type) => {
  switch (type) {
    case 1: return 'info'
    case 2: return 'warning'
    case 3: return 'success'
    case 4: return 'primary'
    case 5: return ''
    case 6: return 'danger'
    case 7: return 'danger'
    default: return 'info'
  }
}

// 获取卡密状态名称
const getCardStatusName = (status) => {
  return getLabelByValue(CARD_STATUS, status)
}

// 获取卡密状态标签类型
const getCardStatusTag = (status) => {
  switch (status) {
    case 0: return 'info'      // 未使用
    case 1: return 'success'   // 已使用
    case 2: return 'warning'   // 已过期
    case 3: return 'danger'    // 已禁用
    default: return 'info'
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  fetchCardKeyList()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchCardKeyList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.pageNum = val
  fetchCardKeyList()
}

// 获取卡密列表
const fetchCardKeyList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    const res = await getCardKeyList(params)
    cardKeyList.value = res.data || []
    pagination.total = res.total || 0
  } catch (error) {
    console.error('获取卡密列表失败:', error)
    ElMessage.error('获取卡密列表失败')
    
    // 使用模拟数据
    cardKeyList.value = [
      {
        id: 1,
        cardKey: 'ABCD-EFGH-IJKL-MNOP',
        cardType: 1,
        status: 0,
        username: '',
        activateTime: '',
        expireTime: '',
        createTime: '2023-06-01 12:00:00'
      },
      {
        id: 2,
        cardKey: 'QRST-UVWX-YZ12-3456',
        cardType: 2,
        status: 1,
        username: 'user1',
        activateTime: '2023-06-02 12:00:00',
        expireTime: '2023-06-09 12:00:00',
        createTime: '2023-06-01 12:00:00'
      },
      {
        id: 3,
        cardKey: '7890-ABCD-EFGH-IJKL',
        cardType: 3,
        status: 2,
        username: 'user2',
        activateTime: '2023-05-01 12:00:00',
        expireTime: '2023-06-01 12:00:00',
        createTime: '2023-05-01 12:00:00'
      }
    ]
    pagination.total = 3
  } finally {
    loading.value = false
  }
}

// 打开批量生成卡密对话框
const handleCreateBatch = () => {
  // 重置表单
  Object.assign(cardKeyForm, {
    cardType: '',
    count: 10,
    remark: ''
  })
  
  dialogVisible.value = true
}

// 提交生成卡密表单
const submitCardKeyForm = async () => {
  if (!cardKeyFormRef.value) return
  
  await cardKeyFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 调用API生成卡密
        const res = await generateCardKeys(cardKeyForm)
        
        ElMessage.success(`成功生成${cardKeyForm.count}个卡密`)
        dialogVisible.value = false
        
        // 刷新列表
        fetchCardKeyList()
      } catch (error) {
        console.error('生成卡密失败:', error)
        ElMessage.error('生成卡密失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 删除卡密
const handleDeleteCardKey = (cardKey) => {
  ElMessageBox.confirm(`确定要删除卡密 ${cardKey.cardKey} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteCardKey(cardKey.id)
      ElMessage.success('删除卡密成功')
      
      // 刷新列表
      fetchCardKeyList()
    } catch (error) {
      console.error('删除卡密失败:', error)
      ElMessage.error('删除卡密失败')
    }
  }).catch(() => {})
}

// 复制卡密
const handleCopyCardKey = (cardKey) => {
  // 创建一个临时的textarea元素
  const textarea = document.createElement('textarea')
  textarea.value = cardKey
  document.body.appendChild(textarea)
  
  // 选择并复制文本
  textarea.select()
  document.execCommand('copy')
  
  // 移除临时元素
  document.body.removeChild(textarea)
  
  ElMessage.success('卡密已复制到剪贴板')
}

onMounted(() => {
  fetchCardKeyList()
})
</script>

<style scoped>
.card-key-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
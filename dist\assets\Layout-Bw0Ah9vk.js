import{_ as j,g as d,h as q,b as i,c as A,a as e,w as o,r as t,u as G,o as r,f as l,i as m,L as H,e as J,N as K,E as O}from"./index-DKz5EXvV.js";const P={class:"user-layout"},Q={class:"header-left"},U={class:"header-right"},W={class:"avatar-container"},X={class:"username"},Y={__name:"Layout",setup(Z){const _=q(),p=d(()=>_.matched.filter(n=>n.meta&&n.meta.title).map(n=>n.meta.title)),f={setup(){return{breadcrumbs:p}},template:`
    <el-breadcrumb separator="/">
      <el-breadcrumb-item v-for="(title, index) in breadcrumbs" :key="index">
        {{ title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  `},v=G(),s=i(!1),g=i(localStorage.getItem("username")||"用户"),b=d(()=>_.path),x=()=>{s.value=!s.value},w=()=>{K.confirm("确定要退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{localStorage.removeItem("token"),localStorage.removeItem("role"),localStorage.removeItem("userId"),localStorage.removeItem("username"),v.push("/login"),O({type:"success",message:"已退出登录"})}).catch(()=>{})};return(h,n)=>{const k=t("el-icon-house"),a=t("el-icon"),c=t("el-menu-item"),y=t("el-icon-check"),B=t("el-icon-user"),I=t("el-icon-data-analysis"),C=t("el-menu"),S=t("el-aside"),E=t("el-icon-fold"),L=t("el-icon-expand"),N=t("el-avatar"),M=t("el-icon-arrow-down"),T=t("el-dropdown-item"),V=t("el-dropdown-menu"),F=t("el-dropdown"),R=t("el-header"),z=t("router-view"),D=t("el-main"),u=t("el-container");return r(),A("div",P,[e(u,{class:"layout-container"},{default:o(()=>[e(S,{width:"220px",class:"aside"},{default:o(()=>[n[4]||(n[4]=l("div",{class:"logo"},[l("h2",null,"用户中心")],-1)),e(C,{"default-active":b.value,class:"el-menu-vertical","background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF",collapse:s.value,router:""},{default:o(()=>[e(c,{index:"/user"},{default:o(()=>[e(a,null,{default:o(()=>[e(k)]),_:1}),n[0]||(n[0]=l("span",null,"用户中心",-1))]),_:1,__:[0]}),e(c,{index:"/user/activate"},{default:o(()=>[e(a,null,{default:o(()=>[e(y)]),_:1}),n[1]||(n[1]=l("span",null,"激活卡密",-1))]),_:1,__:[1]}),e(c,{index:"/user/profile"},{default:o(()=>[e(a,null,{default:o(()=>[e(B)]),_:1}),n[2]||(n[2]=l("span",null,"个人资料",-1))]),_:1,__:[2]}),e(c,{index:"/user/promotion/rank"},{default:o(()=>[e(a,null,{default:o(()=>[e(I)]),_:1}),n[3]||(n[3]=l("span",null,"推广排名",-1))]),_:1,__:[3]})]),_:1},8,["default-active","collapse"])]),_:1,__:[4]}),e(u,null,{default:o(()=>[e(R,{class:"header"},{default:o(()=>[l("div",Q,[e(a,{class:"collapse-btn",onClick:x},{default:o(()=>[s.value?(r(),m(L,{key:1})):(r(),m(E,{key:0}))]),_:1}),e(f)]),l("div",U,[e(F,{trigger:"click"},{dropdown:o(()=>[e(V,null,{default:o(()=>[e(T,{onClick:w},{default:o(()=>n[5]||(n[5]=[J("退出登录")])),_:1,__:[5]})]),_:1})]),default:o(()=>[l("div",W,[e(N,{size:30,icon:"el-icon-user"}),l("span",X,H(g.value),1),e(a,null,{default:o(()=>[e(M)]),_:1})])]),_:1})])]),_:1}),e(D,{class:"main"},{default:o(()=>[e(z)]),_:1})]),_:1})]),_:1})])}}},ee=j(Y,[["__scopeId","data-v-b46cf854"]]);export{ee as default};

<template>
  <div class="user-management">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="手机号" clearable />
        </el-form-item>
        
        <el-form-item label="推荐人ID">
          <el-input v-model="searchForm.referrerId" placeholder="推荐人ID" clearable />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option label="正常" :value="0" />
            <el-option label="禁用" :value="1" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 用户表格 -->
      <el-table
        v-loading="loading"
        :data="userList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="phone" label="手机号" width="150" />
        <el-table-column prop="invitationCode" label="邀请码" width="150" />
        <el-table-column prop="referrerId" label="推荐人ID" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
              {{ scope.row.status === 0 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="180" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="320">
          <template #default="scope">
            <el-button 
              :type="scope.row.status === 0 ? 'danger' : 'success'" 
              link 
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status === 0 ? '禁用' : '启用' }}
            </el-button>
            <el-button 
              type="primary" 
              link 
              @click="handleViewDetails(scope.row)"
            >
              查看详情
            </el-button>
            <el-button 
              type="danger" 
              link 
              @click="handleDeleteUser(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="用户详情"
      width="500px"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="ID">{{ currentUser.id }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ currentUser.phone }}</el-descriptions-item>
        <el-descriptions-item label="邀请码">{{ currentUser.invitationCode }}</el-descriptions-item>
        <el-descriptions-item label="推荐人ID">{{ currentUser.referrerId }}</el-descriptions-item>
        <el-descriptions-item label="推荐人手机号">{{ currentUser.referrerPhone }}</el-descriptions-item>
        <el-descriptions-item label="已推荐人数">{{ currentUser.referredCount }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="currentUser.status === 0 ? 'success' : 'danger'">
            {{ currentUser.status === 0 ? '正常' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="注册时间">{{ currentUser.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ currentUser.updateTime }}</el-descriptions-item>
        
        <!-- 卡密信息 -->
        <el-descriptions-item label="卡密信息" v-if="currentUser.cardKeys && currentUser.cardKeys.length > 0">
          <div class="card-info" v-for="(card, index) in currentUser.cardKeys" :key="card.id">
            <div class="card-header">
              <strong>卡密 #{{ index + 1 }}</strong>
              <el-divider v-if="index > 0" />
            </div>
            <div><strong>卡密ID:</strong> {{ card.id }}</div>
            <div><strong>卡密码:</strong> {{ card.keyCode }}</div>
            <div><strong>卡密类型:</strong> {{ card.keyTypeName || getCardTypeName(card.keyType) }}</div>
            <div><strong>体验时长:</strong> {{ card.experienceHours }} 小时</div>
            <div><strong>激活时间:</strong> {{ card.activateTime }}</div>
            <div><strong>到期时间:</strong> {{ card.expireTime }}</div>
            <div>
              <strong>状态:</strong> 
              <el-tag :type="getCardStatusTypeByName(card.statusName || card.status)">
                {{ card.statusName || getCardStatusName(card.status) }}
              </el-tag>
            </div>
            <div v-if="card.remark"><strong>备注:</strong> {{ card.remark }}</div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="卡密信息" v-else>
          <el-empty description="暂无卡密信息" :image-size="60"></el-empty>
        </el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button 
            :type="currentUser.status === 0 ? 'danger' : 'success'" 
            @click="handleToggleStatus(currentUser, true)"
          >
            {{ currentUser.status === 0 ? '禁用用户' : '启用用户' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserList, getUserDetail, deleteUser, enableUser, disableUser } from '../../api/admin'
import { CARD_TYPES, CARD_STATUS_OPTIONS } from '../../utils/constants'

const loading = ref(false)
const dialogVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  phone: '',
  referrerId: '',
  status: null
})

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 用户列表
const userList = ref([])

// 当前选中的用户
const currentUser = reactive({
  id: '',
  phone: '',
  invitationCode: '',
  referrerId: '',
  referrerPhone: '',
  referredCount: 0,
  status: 0,
  createTime: '',
  updateTime: ''
})

// 获取卡密类型名称
const getCardTypeName = (type) => {
  const cardType = CARD_TYPES.find(item => item.value === type)
  return cardType ? cardType.label : `未知类型(${type})`
}

// 获取卡密状态名称
const getCardStatusName = (status) => {
  const statusOption = CARD_STATUS_OPTIONS.find(item => item.value === status)
  return statusOption ? statusOption.label : `未知状态(${status})`
}

// 获取卡密状态对应的标签类型
const getCardStatusType = (status) => {
  switch (status) {
    case 0: return 'success' // 未使用
    case 1: return 'primary' // 已使用
    case 2: return 'info'    // 已过期
    default: return 'info'
  }
}

// 获取卡密状态对应的标签类型
const getCardStatusTypeByName = (statusOrName) => {
  // 如果是字符串，则认为是状态名称
  if (typeof statusOrName === 'string') {
    if (statusOrName === '未使用') return 'success'
    if (statusOrName === '已激活' || statusOrName === '已使用') return 'primary'
    if (statusOrName === '已过期') return 'info'
    return 'info'
  }
  
  // 如果是数字，则认为是状态值
  switch (statusOrName) {
    case 0: return 'success' // 未使用
    case 1: return 'primary' // 已使用/已激活
    case 2: return 'info'    // 已过期
    default: return 'info'
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  fetchUserList()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  searchForm.status = null
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchUserList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.pageNum = val
  fetchUserList()
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    const res = await getUserList(params)
    if (res.code === 200 && res.data) {
      userList.value = res.data.list || []
      pagination.total = res.data.total || 0
      pagination.pageNum = res.data.pageNum || 1
      pagination.pageSize = res.data.pageSize || 10
    } else {
      ElMessage.error(res.message || '获取用户列表失败')
      userList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
    userList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 查看用户详情
const handleViewDetails = async (user) => {
  try {
    const res = await getUserDetail(user.id)
    if (res.code === 200 && res.data) {
      Object.assign(currentUser, res.data)
      dialogVisible.value = true
    } else {
      ElMessage.error(res.message || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}

// 切换用户状态
const handleToggleStatus = (user, isFromDialog = false) => {
  const isDisable = user.status === 0
  const statusText = isDisable ? '禁用' : '启用'
  
  ElMessageBox.confirm(`确定要${statusText}用户 ${user.phone || user.id} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const api = isDisable ? disableUser : enableUser
      const res = await api(user.id)
      
      if (res.code === 200) {
        // 更新本地数据
        user.status = isDisable ? 1 : 0
        
        // 如果是从对话框触发，更新列表中对应的用户
        if (isFromDialog) {
          const userInList = userList.value.find(item => item.id === user.id)
          if (userInList) {
            userInList.status = user.status
          }
        }
        
        ElMessage.success(`${statusText}用户成功`)
        
        // 如果是从对话框触发，关闭对话框
        if (isFromDialog) {
          dialogVisible.value = false
        }
      } else {
        ElMessage.error(res.message || `${statusText}用户失败`)
      }
    } catch (error) {
      console.error(`${statusText}用户失败:`, error)
      ElMessage.error(`${statusText}用户失败`)
    }
  }).catch(() => {})
}

// 删除用户
const handleDeleteUser = (user) => {
  ElMessageBox.confirm(`确定要删除用户 ${user.phone || user.id} 吗? 此操作不可恢复!`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteUser(user.id)
      
      if (res.code === 200) {
        ElMessage.success('删除用户成功')
        fetchUserList() // 重新加载用户列表
      } else {
        ElMessage.error(res.message || '删除用户失败')
    }
    } catch (error) {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }).catch(() => {})
}

onMounted(() => {
  fetchUserList()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 10px;
}

.card-info div {
  line-height: 1.5;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  font-size: 16px;
  color: #409EFF;
}

.card-info .el-divider {
  margin: 10px 0;
}
</style> 
<template>
  <div class="about-us-container">
    <h2>关于我们管理</h2>
    
    <div class="actions">
      <el-button type="primary" @click="openFormDialog(null)">
        <i class="el-icon-plus"></i> 新增信息
      </el-button>
    </div>
    
    <el-table
      :data="list"
      border
      v-loading="loading"
      style="width: 100%; margin-top: 15px;"
    >
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      
      <el-table-column prop="title" label="标题" min-width="120">
        <template #default="scope">
          <el-tooltip :content="scope.row.title" placement="top" :hide-after="0">
            <span>{{ scope.row.title }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column prop="content" label="内容" min-width="200">
        <template #default="scope">
          <el-tooltip :content="scope.row.content" placement="top" :hide-after="0">
            <span>{{ truncateContent(scope.row.content) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column prop="sortOrder" label="排序" width="80"></el-table-column>
      
      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="(val) => handleStatusChange(val, scope.row)"
            active-text="启用"
            inactive-text="禁用"
          ></el-switch>
        </template>
      </el-table-column>
      
      <el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="160"></el-table-column>
      
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click="handlePreview(scope.row)"
            icon="el-icon-view"
          >
            预览
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="openFormDialog(scope.row)"
            icon="el-icon-edit"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(scope.row.id)"
            icon="el-icon-delete"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
    
    <!-- 表单弹窗 -->
    <el-dialog
      :title="form.id ? '编辑信息' : '新增信息'"
      v-model="formDialogVisible"
      width="700px"
      :before-close="closeFormDialog"
    >
      <el-form 
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" maxlength="100" show-word-limit></el-input>
        </el-form-item>
        
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="8"
            placeholder="请输入内容"
            maxlength="10000"
            show-word-limit
          ></el-input>
        </el-form-item>
        
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number 
            v-model="form.sortOrder" 
            :min="0" 
            :max="9999"
            style="width: 200px;"
          ></el-input-number>
          <span class="form-tip">数值越小排序越靠前</span>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeFormDialog">取消</el-button>
          <el-button type="primary" @click="handleSubmitForm" :loading="submitLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 预览弹窗 -->
    <el-dialog
      title="内容预览"
      v-model="previewDialogVisible"
      width="700px"
    >
      <template v-if="previewData">
        <h3 class="preview-title">{{ previewData.title }}</h3>
        <div class="preview-content">{{ previewData.content }}</div>
        <div class="preview-info">更新时间: {{ previewData.updateTime }}</div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'

export default {
  name: 'AboutUs',
  setup() {
    // 列表数据
    const list = ref([])
    const loading = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    
    // 表单数据
    const formRef = ref(null)
    const formDialogVisible = ref(false)
    const submitLoading = ref(false)
    const form = reactive({
      id: null,
      title: '',
      content: '',
      sortOrder: 0,
      status: 1
    })
    
    // 表单验证规则
    const formRules = {
      title: [
        { required: true, message: '请输入标题', trigger: 'blur' },
        { max: 100, message: '标题最多100个字符', trigger: 'blur' }
      ],
      content: [
        { required: true, message: '请输入内容', trigger: 'blur' }
      ],
      sortOrder: [
        { required: true, message: '请输入排序值', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }
    
    // 预览数据
    const previewDialogVisible = ref(false)
    const previewData = ref(null)
    
    // 获取列表数据
    const fetchList = async () => {
      loading.value = true
      try {
        const res = await request('/admin/about-us')
        if (res.code === 200) {
          list.value = res.data
          total.value = res.data.length
        } else {
          ElMessage.error(res.message || '获取数据失败')
        }
      } catch (error) {
        console.error('获取关于我们列表出错:', error)
        ElMessage.error('获取数据失败')
      } finally {
        loading.value = false
      }
    }
    
    // 打开表单弹窗
    const openFormDialog = (row) => {
      if (row) {
        Object.keys(form).forEach(key => {
          form[key] = row[key]
        })
      } else {
        Object.keys(form).forEach(key => {
          form[key] = key === 'sortOrder' ? 0 : key === 'status' ? 1 : null
        })
      }
      
      formDialogVisible.value = true
      nextTick(() => {
        if (formRef.value) {
          formRef.value.clearValidate()
        }
      })
    }
    
    // 关闭表单弹窗
    const closeFormDialog = () => {
      formDialogVisible.value = false
    }
    
    // 提交表单
    const handleSubmitForm = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        
        submitLoading.value = true
        let res
        
        if (form.id) {
          // 编辑
          res = await request(`/admin/about-us/${form.id}`, {
            method: 'PUT',
            data: {
              title: form.title,
              content: form.content,
              sortOrder: form.sortOrder,
              status: form.status
            }
          })
        } else {
          // 新增
          res = await request('/admin/about-us', {
            method: 'POST',
            data: {
              title: form.title,
              content: form.content,
              sortOrder: form.sortOrder,
              status: form.status
            }
          })
        }
        
        if (res.code === 200) {
          ElMessage.success('保存成功')
          formDialogVisible.value = false
          fetchList()
        } else {
          ElMessage.error(res.message || '保存失败')
        }
      } catch (error) {
        console.error('保存关于我们信息出错:', error)
        ElMessage.error('保存失败')
      } finally {
        submitLoading.value = false
      }
    }
    
    // 删除
    const handleDelete = (id) => {
      ElMessageBox.confirm('确定要删除此信息吗？删除后不可恢复。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await request(`/admin/about-us/${id}`, {
            method: 'DELETE'
          })
          if (res.code === 200) {
            ElMessage.success('删除成功')
            fetchList()
          } else {
            ElMessage.error(res.message || '删除失败')
          }
        } catch (error) {
          console.error('删除关于我们信息出错:', error)
          ElMessage.error('删除失败')
        }
      }).catch(() => {})
    }
    
    // 状态变更
    const handleStatusChange = async (value, row) => {
      try {
        const url = value === 1 
          ? `/admin/about-us/${row.id}/enable` 
          : `/admin/about-us/${row.id}/disable`
        
        const res = await request(url, {
          method: 'PUT'
        })
        if (res.code === 200) {
          ElMessage.success(`${value === 1 ? '启用' : '禁用'}成功`)
        } else {
          // 回滚UI状态
          row.status = value === 1 ? 0 : 1
          ElMessage.error(res.message || `${value === 1 ? '启用' : '禁用'}失败`)
        }
      } catch (error) {
        // 回滚UI状态
        row.status = value === 1 ? 0 : 1
        console.error(`${value === 1 ? '启用' : '禁用'}关于我们信息出错:`, error)
        ElMessage.error(`${value === 1 ? '启用' : '禁用'}失败`)
      }
    }
    
    // 预览
    const handlePreview = (row) => {
      previewData.value = row
      previewDialogVisible.value = true
    }
    
    // 分页处理
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchList()
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchList()
    }
    
    // 内容截断
    const truncateContent = (content) => {
      if (!content) return ''
      return content.length > 50 ? content.substring(0, 50) + '...' : content
    }
    
    onMounted(() => {
      fetchList()
    })
    
    return {
      list,
      loading,
      currentPage,
      pageSize,
      total,
      formRef,
      form,
      formRules,
      formDialogVisible,
      submitLoading,
      previewDialogVisible,
      previewData,
      openFormDialog,
      closeFormDialog,
      handleSubmitForm,
      handleDelete,
      handleStatusChange,
      handlePreview,
      handleSizeChange,
      handleCurrentChange,
      truncateContent
    }
  }
}
</script>

<style scoped>
.about-us-container {
  padding: 20px;
}

.actions {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.preview-title {
  text-align: center;
  margin-bottom: 20px;
}

.preview-content {
  white-space: pre-wrap;
  line-height: 1.6;
  margin-bottom: 20px;
}

.preview-info {
  text-align: right;
  color: #999;
  font-size: 12px;
}

.form-tip {
  margin-left: 10px;
  color: #999;
  font-size: 12px;
}
</style> 
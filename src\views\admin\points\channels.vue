<template>
  <div class="channels-container">
    <div class="page-header">
      <h2>积分渠道管理</h2>
      <el-button type="primary" @click="handleAddChannel">添加渠道</el-button>
    </div>

    <el-card class="table-card">
      <el-table
        :data="channelsList"
        style="width: 100%"
        border
        v-loading="loading"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="channelName" label="渠道名称" width="150" />
        <el-table-column prop="channelCode" label="渠道代码" width="150" />
        <el-table-column prop="isFixed" label="是否固定" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isFixed === 1 ? 'warning' : 'info'">
              {{ scope.row.isFixed === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="contributeToReferrer" label="贡献给推荐人" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.contributeToReferrer === 1 ? 'success' : 'info'">
              {{ scope.row.contributeToReferrer === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="referrerRatio" label="推荐人比例" width="120">
          <template #default="scope">
            {{ scope.row.contributeToReferrer === 1 ? (scope.row.referrerRatio * 100).toFixed(0) + '%' : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="渠道描述" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click="handleEditChannel(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="handleDeleteChannel(scope.row)"
            >
              删除
            </el-button>
            <el-button 
              :type="scope.row.status === 1 ? 'warning' : 'success'" 
              size="small" 
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑渠道对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑渠道' : '添加渠道'"
      width="500px"
    >
      <el-form
        ref="channelFormRef"
        :model="channelForm"
        :rules="channelRules"
        label-width="100px"
      >
        <el-form-item label="渠道名称" prop="channelName">
          <el-input v-model="channelForm.channelName" placeholder="请输入渠道名称" />
        </el-form-item>
        <el-form-item label="渠道代码" prop="channelCode">
          <el-input 
            v-model="channelForm.channelCode" 
            placeholder="请输入渠道代码" 
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="渠道描述" prop="remark">
          <el-input
            v-model="channelForm.remark"
            type="textarea"
            placeholder="请输入渠道描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="channelForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item label="是否固定" prop="isFixed">
          <el-switch
            v-model="channelForm.isFixed"
            :active-value="1"
            :inactive-value="0"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
        <el-form-item label="贡献给推荐人" prop="contributeToReferrer">
          <el-switch
            v-model="channelForm.contributeToReferrer"
            :active-value="1"
            :inactive-value="0"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
        <el-form-item 
          label="推荐人比例" 
          prop="referrerRatio"
          v-if="channelForm.contributeToReferrer === 1"
        >
          <el-input-number 
            v-model="channelForm.referrerRatio" 
            :min="0" 
            :max="1" 
            :step="0.01" 
            :precision="2"
            style="width: 180px"
          />
          <span class="ratio-hint">（0-1之间的小数，如0.1表示10%）</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitChannelForm" :loading="submitLoading">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getChannelsList, 
  addChannel,
  updateChannel,
  deleteChannel
} from '@/api/points'

// 渠道列表数据
const channelsList = ref([])
const loading = ref(false)
const submitLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 对话框控制
const dialogVisible = ref(false)
const isEdit = ref(false)
const channelFormRef = ref(null)
const channelForm = ref({
  id: '',
  channelName: '',
  channelCode: '',
  isFixed: 0,
  contributeToReferrer: 0,
  referrerRatio: 0,
  status: 1,
  remark: ''
})

// 定义表单验证规则
const channelRules = {
  channelName: [
    { required: true, message: '请输入渠道名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  channelCode: [
    { required: true, message: '请输入渠道代码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  referrerRatio: [
    { 
      required: true, 
      message: '请输入推荐人比例', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (channelForm.value.contributeToReferrer === 1 && (value === null || value === undefined)) {
          callback(new Error('当贡献给推荐人时，必须设置推荐人比例'));
        } else {
          callback();
        }
      }
    }
  ],
  remark: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取渠道列表
const fetchChannels = async () => {
  loading.value = true
  try {
    const response = await getChannelsList({
      page: currentPage.value,
      limit: pageSize.value
    })
    
    if (response.code === 0 || response.code === 200) {
      channelsList.value = Array.isArray(response.data) ? response.data : []
      total.value = Array.isArray(response.data) ? response.data.length : 0
    } else {
      ElMessage.error(response.message || '获取渠道列表失败')
    }
  } catch (error) {
    console.error('获取渠道列表出错:', error)
    ElMessage.error('获取渠道列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchChannels()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchChannels()
}

// 初始化表单
const initChannelForm = () => {
  channelForm.value = {
    id: '',
    channelName: '',
    channelCode: '',
    isFixed: 0,
    contributeToReferrer: 0,
    referrerRatio: 0,
    status: 1,
    remark: ''
  }
}

// 添加渠道
const handleAddChannel = () => {
  isEdit.value = false
  initChannelForm()
  dialogVisible.value = true
}

// 编辑渠道
const handleEditChannel = (row) => {
  isEdit.value = true
  channelForm.value = {
    id: row.id,
    channelName: row.channelName,
    channelCode: row.channelCode,
    isFixed: row.isFixed || 0,
    contributeToReferrer: row.contributeToReferrer || 0,
    referrerRatio: row.referrerRatio || 0,
    status: row.status,
    remark: row.remark
  }
  dialogVisible.value = true
}

// 删除渠道
const handleDeleteChannel = (row) => {
  ElMessageBox.confirm(
    `确定要删除渠道 "${row.channelName}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        const response = await deleteChannel(row.id)
        
        if (response.code === 0 || response.code === 200) {
          ElMessage.success('删除成功')
          fetchChannels()
        } else {
          ElMessage.error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('删除渠道出错:', error)
        ElMessage.error('删除失败，请稍后重试')
      }
    })
    .catch(() => {
      // 用户取消删除操作
    })
}

// 切换渠道状态
const handleToggleStatus = async (row) => {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'
  
  try {
    // 使用updateChannel接口更新状态
    const response = await updateChannel({
      id: row.id,
      channelName: row.channelName,
      channelCode: row.channelCode,
      contributeToReferrer: row.contributeToReferrer,
      referrerRatio: row.referrerRatio,
      status: newStatus,
      remark: row.remark
    })
    
    if (response.code === 0 || response.code === 200) {
      ElMessage.success(`${statusText}成功`)
      row.status = newStatus
    } else {
      ElMessage.error(response.message || `${statusText}失败`)
    }
  } catch (error) {
    console.error(`${statusText}渠道出错:`, error)
    ElMessage.error(`${statusText}失败，请稍后重试`)
  }
}

// 提交渠道表单
const submitChannelForm = async () => {
  if (!channelFormRef.value) return
  
  await channelFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitLoading.value = true
    try {
      let response;
      
      if (isEdit.value) {
        // 更新渠道
        response = await updateChannel(channelForm.value);
      } else {
        // 添加渠道
        response = await addChannel(channelForm.value);
      }
      
      if (response.code === 0 || response.code === 200) {
        ElMessage.success(isEdit.value ? '更新渠道成功' : '添加渠道成功')
        dialogVisible.value = false
        fetchChannels()
      } else {
        ElMessage.error(response.message || (isEdit.value ? '更新渠道失败' : '添加渠道失败'))
      }
    } catch (error) {
      console.error(isEdit.value ? '更新渠道出错:' : '添加渠道出错:', error)
      ElMessage.error(isEdit.value ? '更新渠道失败，请稍后重试' : '添加渠道失败，请稍后重试')
    } finally {
      submitLoading.value = false
    }
  })
}

// 页面加载时获取渠道列表
onMounted(() => {
  fetchChannels()
})
</script>

<style scoped>
.channels-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.table-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.ratio-hint {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style> 
<template>
  <div class="profile-container">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>个人资料</span>
        </div>
      </template>
      
      <el-form
        ref="profileFormRef"
        :model="profileForm"
        :rules="profileRules"
        label-width="100px"
        class="profile-form"
      >
        <el-form-item label="用户名">
          <el-input v-model="profileForm.username" disabled />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="profileForm.phone" placeholder="请输入手机号码" />
        </el-form-item>
        
        <el-form-item label="注册时间">
          <el-input v-model="profileForm.createTime" disabled />
        </el-form-item>
        
        <el-divider>修改密码</el-divider>
        
        <el-form-item label="原密码" prop="oldPassword">
          <el-input 
            v-model="profileForm.oldPassword" 
            type="password" 
            placeholder="请输入原密码" 
            show-password 
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="profileForm.newPassword" 
            type="password" 
            placeholder="请输入新密码" 
            show-password 
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="profileForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入新密码" 
            show-password 
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleUpdate">保存修改</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getUserInfo, updateUserInfo } from '../../api/user'

const profileFormRef = ref(null)
const loading = ref(false)
const userId = localStorage.getItem('userId')

// 个人资料表单
const profileForm = reactive({
  id: userId,
  username: '',
  email: '',
  phone: '',
  createTime: '',
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 验证新密码
const validateNewPass = (rule, value, callback) => {
  if (value === '') {
    callback()
  } else if (value.length < 6) {
    callback(new Error('密码长度不能小于6个字符'))
  } else {
    if (profileForm.confirmPassword !== '') {
      profileFormRef.value.validateField('confirmPassword')
    }
    callback()
  }
}

// 验证确认密码
const validateConfirmPass = (rule, value, callback) => {
  if (profileForm.newPassword === '') {
    callback()
  } else if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== profileForm.newPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const profileRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  oldPassword: [
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  newPassword: [
    { validator: validateNewPass, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPass, trigger: 'blur' }
  ]
}

// 获取用户信息
const fetchUserInfo = async () => {
  if (!userId) return
  
  try {
    const res = await getUserInfo(userId)
    const { password, ...userInfo } = res.data
    Object.assign(profileForm, userInfo)
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 更新用户信息
const handleUpdate = async () => {
  if (!profileFormRef.value) return
  
  await profileFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // 构建更新数据
        const updateData = {
          id: profileForm.id,
          email: profileForm.email,
          phone: profileForm.phone
        }
        
        // 如果有输入密码，则添加密码字段
        if (profileForm.oldPassword && profileForm.newPassword) {
          updateData.oldPassword = profileForm.oldPassword
          updateData.password = profileForm.newPassword
        }
        
        await updateUserInfo(updateData)
        
        ElMessage.success('个人资料更新成功')
        
        // 清空密码字段
        profileForm.oldPassword = ''
        profileForm.newPassword = ''
        profileForm.confirmPassword = ''
      } catch (error) {
        console.error('更新用户信息失败:', error)
        ElMessage.error('更新用户信息失败，请检查原密码是否正确')
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  profileForm.oldPassword = ''
  profileForm.newPassword = ''
  profileForm.confirmPassword = ''
  fetchUserInfo()
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.profile-form {
  max-width: 600px;
  margin: 0 auto;
}
</style> 
import{s as r}from"./request-YZgFExiB.js";function u(t){return r({url:`/user/${t}`,method:"get"})}function a(t){return r({url:"/user",method:"put",data:t})}function n(t){return r({url:"/cardkey/activate",method:"post",data:t})}function o(t){return r({url:"/user/card/status",method:"get",params:{userId:t}})}function s(t){return r({url:"/promotion/rank",method:"get",params:t})}export{n as a,o as b,s as c,u as g,a as u};

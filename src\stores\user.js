import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    id: localStorage.getItem('userId') || null,
    username: localStorage.getItem('username') || '',
    role: localStorage.getItem('role') || '',
    token: localStorage.getItem('token') || '',
    invitationCode: localStorage.getItem('invitationCode') || ''
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.role === 'admin',
    isUser: (state) => state.role === 'user'
  },
  
  actions: {
    setUserInfo(userInfo) {
      if (userInfo.id) {
        this.id = userInfo.id
        localStorage.setItem('userId', userInfo.id)
      }
      
      if (userInfo.username) {
        this.username = userInfo.username
        localStorage.setItem('username', userInfo.username)
      }
      
      if (userInfo.role) {
        this.role = userInfo.role
        localStorage.setItem('role', userInfo.role)
      }
      
      if (userInfo.token) {
        this.token = userInfo.token
        localStorage.setItem('token', userInfo.token)
      }
      
      if (userInfo.invitationCode) {
        this.invitationCode = userInfo.invitationCode
        localStorage.setItem('invitationCode', userInfo.invitationCode)
      }
    },
    
    clearUserInfo() {
      this.id = null
      this.username = ''
      this.role = ''
      this.token = ''
      this.invitationCode = ''
      
      localStorage.removeItem('userId')
      localStorage.removeItem('username')
      localStorage.removeItem('role')
      localStorage.removeItem('token')
      localStorage.removeItem('invitationCode')
    }
  }
}) 
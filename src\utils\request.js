import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'

// 创建axios实例
const service = axios.create({
  baseURL: '/api', // API基础URL
  timeout: 15000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 调试输出
    console.log('发送请求:', config.method.toUpperCase(), config.baseURL + config.url)
    
    // 从本地存储获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 调试输出
    console.log('收到响应:', response.config.url, response.data)
    
    const res = response.data
    
    // 调试输出响应结构
    console.log('响应结构:', res)
    
    // 如果返回的状态码不是200，则判断为错误
    if (res.code !== undefined && res.code !== 200) {
      ElMessage({
        message: res.message || '请求失败',
        type: 'error',
        duration: 5 * 1000
      })
      
      // 401: 未授权（未登录或token过期）
      if (res.code === 401) {
        // 清除本地存储的token
        localStorage.removeItem('token')
        localStorage.removeItem('role')
        localStorage.removeItem('username')
        
        // 跳转到登录页
        router.replace('/login')
      }
      
      return Promise.reject(new Error(res.message || '请求失败'))
    } else {
      // 返回响应数据
      return res
    }
  },
  error => {
    console.error('响应错误:', error)
    
    // 处理HTTP错误状态
    let message = '网络错误，请稍后重试'
    if (error.response) {
      switch (error.response.status) {
        case 401:
          message = '未授权，请重新登录'
          // 清除本地存储的token
          localStorage.removeItem('token')
          localStorage.removeItem('role')
          localStorage.removeItem('username')
          
          // 跳转到登录页
          router.replace('/login')
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `请求失败: ${error.response.status}`
      }
    }
    
    ElMessage({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    
    return Promise.reject(error)
  }
)

export default service 
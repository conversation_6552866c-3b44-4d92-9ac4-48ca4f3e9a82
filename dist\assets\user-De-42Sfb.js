import{X as t}from"./index-DKz5EXvV.js";const i=t("user",{state:()=>({id:localStorage.getItem("userId")||null,username:localStorage.getItem("username")||"",role:localStorage.getItem("role")||"",token:localStorage.getItem("token")||"",invitationCode:localStorage.getItem("invitationCode")||""}),getters:{isLoggedIn:e=>!!e.token,isAdmin:e=>e.role==="admin",isUser:e=>e.role==="user"},actions:{setUserInfo(e){e.id&&(this.id=e.id,localStorage.setItem("userId",e.id)),e.username&&(this.username=e.username,localStorage.setItem("username",e.username)),e.role&&(this.role=e.role,localStorage.setItem("role",e.role)),e.token&&(this.token=e.token,localStorage.setItem("token",e.token)),e.invitationCode&&(this.invitationCode=e.invitationCode,localStorage.setItem("invitationCode",e.invitationCode))},clearUserInfo(){this.id=null,this.username="",this.role="",this.token="",this.invitationCode="",localStorage.removeItem("userId"),localStorage.removeItem("username"),localStorage.removeItem("role"),localStorage.removeItem("token"),localStorage.removeItem("invitationCode")}}});export{i as u};

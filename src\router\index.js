import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('../views/Login.vue')
    },
    {
      path: '/admin',
      name: 'AdminLayout',
      component: () => import('../views/admin/Layout.vue'),
      meta: { requiresAuth: true, role: 'admin' },
      children: [
        {
          path: '',
          name: 'AdminDashboard',
          component: () => import('../views/admin/Dashboard.vue'),
          meta: { title: '仪表盘' }
        },
        {
          path: 'users',
          name: 'UserManagement',
          component: () => import('../views/admin/UserManagement.vue'),
          meta: { title: '用户管理' }
        },
        {
          path: 'admins',
          name: 'AdminManagement',
          component: () => import('../views/admin/AdminManagement.vue'),
          meta: { title: '管理员管理', superAdminOnly: true }
        },
        {
          path: 'cards',
          name: 'CardManagement',
          component: () => import('../views/admin/CardManagement.vue'),
          meta: { title: '卡密管理' }
        },
        {
          path: 'activate',
          name: 'AdminActivateCard',
          component: () => import('../views/admin/ActivateCard.vue'),
          meta: { title: '激活卡密' }
        },
        {
          path: 'statistics',
          name: 'Statistics',
          component: () => import('../views/admin/Statistics.vue'),
          meta: { title: '统计分析' }
        },
        {
          path: 'logs',
          name: 'OperationLogs',
          component: () => import('../views/admin/OperationLogs.vue'),
          meta: { title: '操作日志' }
        },
        {
          path: 'announcements',
          name: 'Announcements',
          component: () => import('../views/admin/Announcement.vue'),
          meta: { title: '公告管理' }
        },
        {
          path: 'promotions',
          name: 'Promotions',
          component: () => import('../views/admin/Promotion.vue'),
          meta: { title: '推广内容管理' }
        },
        {
          path: 'promotion/rank',
          name: 'PromotionRankConfig',
          component: () => import('../views/admin/PromotionRankConfig.vue'),
          meta: { title: '推广排名配置' }
        },
        {
          path: 'distribution/config',
          name: 'DistributionConfig',
          component: () => import('../views/admin/DistributionConfig.vue'),
          meta: { title: '分销配置管理' }
        },
        {
          path: 'distribution/record',
          name: 'DistributionRecord',
          component: () => import('../views/admin/DistributionRecord.vue'),
          meta: { title: '分销记录管理' }
        },
        {
          path: 'points/users',
          name: 'UserPointsList',
          component: () => import('../views/admin/points/users.vue'),
          meta: { title: '用户积分管理' }
        },
        {
          path: 'points/configs',
          name: 'PointsConfigList',
          component: () => import('../views/admin/points/configs.vue'),
          meta: { title: '积分配置管理' }
        },
        {
          path: 'points/channels',
          name: 'PointsChannelsList',
          component: () => import('../views/admin/points/channels.vue'),
          meta: { title: '积分渠道管理' }
        },
        // 添加系统配置管理路由
        {
          path: 'system/config',
          name: 'SystemConfig',
          component: () => import('../views/admin/SystemConfig.vue'),
          meta: { title: '系统配置管理' }
        },
        // 添加修改密码路由
        {
          path: 'password',
          name: 'ChangePassword',
          component: () => import('../views/admin/ChangePassword.vue'),
          meta: { title: '修改密码' }
        },
        // 添加提现相关路由
        {
          path: 'withdrawal/requests',
          name: 'WithdrawalManagement',
          component: () => import('../views/admin/WithdrawalManagement.vue'),
          meta: { title: '提现申请管理' }
        },
        {
          path: 'withdrawal/records',
          name: 'WithdrawalRecords',
          component: () => import('../views/admin/WithdrawalRecords.vue'),
          meta: { title: '提现记录管理' }
        },
        {
          path: 'withdrawal/amounts',
          name: 'WithdrawalAmounts',
          component: () => import('../views/admin/WithdrawalAmounts.vue'),
          meta: { title: '可提现金额管理' }
        },
        {
          path: 'withdrawal/config',
          name: 'WithdrawalConfig',
          component: () => import('../views/admin/WithdrawalConfig.vue'),
          meta: { title: '提现配置管理' }
        },
        // 添加关于我们管理路由
        {
          path: 'about-us',
          name: 'AboutUsManagement',
          component: () => import('../views/admin/AboutUs.vue'),
          meta: { title: '关于我们管理' }
        }
      ]
    },
    {
      path: '/user',
      name: 'UserLayout',
      component: () => import('../views/user/Layout.vue'),
      meta: { requiresAuth: true, role: 'user' },
      children: [
        {
          path: '',
          name: 'UserDashboard',
          component: () => import('../views/user/Dashboard.vue'),
          meta: { title: '用户中心' }
        },
        {
          path: 'activate',
          name: 'UserActivateCard',
          component: () => import('../views/user/ActivateCard.vue'),
          meta: { title: '激活卡密' }
        },
        {
          path: 'profile',
          name: 'UserProfile',
          component: () => import('../views/user/Profile.vue'),
          meta: { title: '个人资料' }
        },
        {
          path: 'promotion/rank',
          name: 'UserPromotionRank',
          component: () => import('../views/user/PromotionRank.vue'),
          meta: { title: '推广排名' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('../views/NotFound.vue')
    }
  ]
})

// 全局错误处理
const originalPush = router.push
router.push = function push(location) {
  return originalPush.call(this, location).catch(err => {
    if (err.name !== 'NavigationDuplicated') {
      console.error('路由导航错误:', err)
    }
    return Promise.resolve(false)
  })
}

// 导航守卫
router.beforeEach((to, from, next) => {
  console.log('路由导航守卫 - 目标路由:', to.path)
  console.log('认证状态:', !!localStorage.getItem('token'))
  console.log('用户角色:', localStorage.getItem('role'))
  
  const isAuthenticated = !!localStorage.getItem('token')
  const userRole = localStorage.getItem('role')

  // 需要认证的路由
  if (to.meta.requiresAuth) {
    console.log('该路由需要认证')
    if (!isAuthenticated) {
      console.log('用户未认证，重定向到登录页')
      next('/login')
    } else if (to.meta.role && to.meta.role !== userRole) {
      // 角色不匹配
      console.log('角色不匹配，重定向到对应首页')
      next('/admin')
    } else if (to.meta.superAdminOnly && localStorage.getItem('adminRole') !== '2') {
      // 仅超级管理员可访问
      console.log('非超级管理员，无法访问该页面')
      next('/admin')
    } else {
      console.log('认证成功，允许访问')
      next()
    }
  } else {
    // 已登录用户访问登录页，重定向到对应的首页
    if (isAuthenticated && to.path === '/login') {
      console.log('已登录用户访问登录页，重定向到对应首页')
      next('/admin')
    } else {
      console.log('无需认证，允许访问')
      next()
    }
  }
})

export default router 
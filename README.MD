# 卡密系统

这是一个基于Spring Boot + Vue + Element Plus的卡密管理系统，支持多种卡密类型，包括体验卡（12小时）、天卡、周卡、月卡、季卡、年卡和永久卡。

## 技术栈

### 后端
- Spring Boot 3.5.0
- MyBatis
- MySQL
- JWT认证

### 前端
- Vue.js
- Element Plus

## 项目结构

```
cardkey
├── src/main/java/com/example/cardkey
│   ├── common          // 通用类
│   ├── controller      // 控制器
│   ├── entity          // 实体类
│   ├── mapper          // MyBatis映射器
│   ├── service         // 服务接口
│   │   └── impl        // 服务实现
│   └── util            // 工具类
├── src/main/resources
│   ├── application.properties  // 应用配置
│   ├── db                      // 数据库脚本
│   └── mapper                  // MyBatis XML映射文件
└── pom.xml                     // Maven配置
```

## 数据库设计

系统包含以下数据表：
1. `user` - 用户表
2. `admin` - 管理员表
3. `card_key` - 卡密表
4. `user_card` - 用户卡密关联表
5. `card_log` - 卡密使用日志表

详细的表结构请参见 `src/main/resources/db/schema.sql`。

## API接口文档

### 基本信息

#### 服务器地址
- 基础URL：`http://127.0.0.1:8527/api`

#### 认证方式
- 使用JWT令牌认证
- 登录后获取token，在后续请求中通过请求头Authorization传递：`Authorization: Bearer {token}`

#### 响应格式
所有接口返回JSON格式数据，基本结构如下：
```json
{
  "code": 200,       // 状态码，200表示成功
  "message": "操作成功", // 提示信息
  "data": {}         // 返回的数据，可能是对象、数组或null
}
```

### 用户接口

#### 1. 用户注册
- **URL**: `/user/register`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "username": "用户名",
    "password": "密码",
    "email": "邮箱地址",
    "phone": "手机号码"
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "注册成功",
    "data": {
      "id": 1,
      "username": "用户名",
      "email": "邮箱地址",
      "phone": "手机号码",
      "status": 1,
      "createTime": "2023-06-01 12:00:00",
      "updateTime": "2023-06-01 12:00:00"
    }
  }
  ```

#### 2. 用户登录
- **URL**: `/user/login`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "username": "用户名",
    "password": "密码"
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "userId": 1,
      "username": "用户名",
      "expireTime": "2023-06-02 12:00:00"
    }
  }
  ```

#### 3. 获取用户信息
- **URL**: `/user/{id}`
- **方法**: GET
- **请求参数**: 路径参数id
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "username": "用户名",
      "email": "邮箱地址",
      "phone": "手机号码",
      "status": 1,
      "createTime": "2023-06-01 12:00:00",
      "updateTime": "2023-06-01 12:00:00"
    }
  }
  ```

#### 4. 更新用户信息
- **URL**: `/user`
- **方法**: PUT
- **请求参数**:
  ```json
  {
    "id": 1,
    "email": "新邮箱地址",
    "phone": "新手机号码",
    "password": "新密码"  // 可选
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": null
  }
  ```

#### 5. 激活卡密
- **URL**: `/user/card/activate`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "cardCode": "卡密码",
    "userId": 1
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "激活成功",
    "data": {
      "success": true,
      "message": "激活成功",
      "cardType": 4,
      "typeName": "月卡",
      "activateTime": "2023-06-01 12:00:00",
      "expireTime": "2023-07-01 12:00:00"
    }
  }
  ```

#### 6. 获取用户卡密状态
- **URL**: `/user/card/status`
- **方法**: GET
- **请求参数**: `userId=1`
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "hasActiveCard": true,
      "cardInfo": {
        "cardType": 4,
        "typeName": "月卡",
        "activateTime": "2023-06-01 12:00:00",
        "expireTime": "2023-07-01 12:00:00",
        "remainDays": 29
      }
    }
  }
  ```

#### 7. 获取用户列表（管理员接口）
- **URL**: `/user/list`
- **方法**: GET
- **请求参数**:
  - `username`: 用户名（可选）
  - `status`: 状态（可选，0-禁用，1-启用）
  - `pageNum`: 页码（默认1）
  - `pageSize`: 每页条数（默认10）
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "username": "用户名",
        "email": "邮箱地址",
        "phone": "手机号码",
        "status": 1,
        "createTime": "2023-06-01 12:00:00",
        "updateTime": "2023-06-01 12:00:00"
      }
    ]
  }
  ```

### 管理员接口

#### 1. 管理员登录
- **URL**: `/admin/login`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "username": "admin",
    "password": "123456"
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "adminId": 1,
      "username": "admin",
      "role": 2,
      "roleName": "超级管理员"
    }
  }
  ```

#### 2. 添加管理员
- **URL**: `/admin`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "username": "管理员用户名",
    "password": "密码",
    "realName": "真实姓名",
    "role": 1  // 1-普通管理员，2-超级管理员
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "添加成功",
    "data": {
      "id": 2,
      "username": "管理员用户名",
      "realName": "真实姓名",
      "role": 1,
      "roleName": "普通管理员",
      "status": 1,
      "createTime": "2023-06-01 12:00:00",
      "updateTime": "2023-06-01 12:00:00"
    }
  }
  ```

#### 3. 获取管理员信息
- **URL**: `/admin/{id}`
- **方法**: GET
- **请求参数**: 路径参数id
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "username": "admin",
      "realName": "系统管理员",
      "role": 2,
      "roleName": "超级管理员",
      "status": 1,
      "createTime": "2023-06-01 12:00:00",
      "updateTime": "2023-06-01 12:00:00"
    }
  }
  ```

#### 4. 更新管理员信息
- **URL**: `/admin`
- **方法**: PUT
- **请求参数**:
  ```json
  {
    "id": 2,
    "realName": "新名字",
    "password": "新密码",  // 可选
    "role": 1
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": null
  }
  ```

#### 5. 更新管理员状态
- **URL**: `/admin/status`
- **方法**: PUT
- **请求参数**:
  ```json
  {
    "adminId": 2,
    "status": 0  // 0-禁用，1-启用
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": null
  }
  ```

#### 6. 获取管理员列表
- **URL**: `/admin/list`
- **方法**: GET
- **请求参数**:
  - `username`: 用户名（可选）
  - `role`: 角色（可选，1-普通管理员，2-超级管理员）
  - `status`: 状态（可选，0-禁用，1-启用）
  - `pageNum`: 页码（默认1）
  - `pageSize`: 每页条数（默认10）
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "username": "admin",
        "realName": "系统管理员",
        "role": 2,
        "roleName": "超级管理员",
        "status": 1,
        "createTime": "2023-06-01 12:00:00",
        "updateTime": "2023-06-01 12:00:00"
      }
    ]
  }
  ```

#### 7. 生成卡密
- **URL**: `/admin/card/generate`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "type": 4,       // 卡密类型，见卡密类型说明
    "count": 10,     // 生成数量
    "batchName": "6月批次", // 批次名称
    "creatorId": 1   // 创建者ID
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "生成成功",
    "data": {
      "batchId": "BATCH20230601120000",
      "count": 10,
      "cardType": 4,
      "cardList": [
        "AB12CD34EF56GH78",
        "IJ90KL12MN34OP56",
        // ...更多卡密码
      ]
    }
  }
  ```

#### 8. 获取卡密列表
- **URL**: `/admin/card/list`
- **方法**: GET
- **请求参数**:
  - `type`: 卡密类型（可选）
  - `status`: 状态（可选，0-未使用，1-已使用，2-已过期）
  - `batchId`: 批次ID（可选）
  - `pageNum`: 页码（默认1）
  - `pageSize`: 每页条数（默认10）
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "cardCode": "AB12CD34EF56GH78",
        "type": 4,
        "typeName": "月卡",
        "validDays": 30,
        "validHours": 0,
        "status": 0,
        "statusName": "未使用",
        "createTime": "2023-06-01 12:00:00",
        "useTime": null,
        "expireTime": null,
        "batchId": "BATCH20230601120000",
        "creatorId": 1
      }
    ]
  }
  ```

#### 9. 更新卡密状态
- **URL**: `/admin/card/status`
- **方法**: PUT
- **请求参数**:
  ```json
  {
    "cardId": 1,
    "status": 2  // 0-未使用，1-已使用，2-已过期
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": null
  }
  ```

#### 10. 获取统计数据
- **URL**: `/admin/statistics`
- **方法**: GET
- **请求参数**:
  - `startDate`: 开始日期（可选，格式：yyyy-MM-dd）
  - `endDate`: 结束日期（可选，格式：yyyy-MM-dd）
  - `period`: 周期（可选，默认day，可选值：day/week/month）
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "cardStatistics": {
        "totalGenerated": 100,
        "totalUsed": 50,
        "totalExpired": 10,
        "typeDistribution": [
          {"type": 1, "count": 10},
          {"type": 2, "count": 20},
          {"type": 3, "count": 15},
          {"type": 4, "count": 30},
          {"type": 5, "count": 10},
          {"type": 6, "count": 10},
          {"type": 7, "count": 5}
        ]
      },
      "userStatistics": {
        "activeUsers": 80,
        "newUsers": 20
      }
    }
  }
  ```

### 卡密类型说明

| 类型值 | 类型名称 | 有效期 |
|--------|----------|--------|
| 1      | 体验卡   | 12小时 |
| 2      | 天卡     | 1天    |
| 3      | 周卡     | 7天    |
| 4      | 月卡     | 30天   |
| 5      | 季卡     | 90天   |
| 6      | 年卡     | 365天  |
| 7      | 永久卡   | 永久   |

### 状态码说明

| 状态码 | 说明                 |
|--------|----------------------|
| 200    | 成功                 |
| 401    | 未授权（未登录或token过期）|
| 403    | 权限不足             |
| 500    | 服务器内部错误       |

## 使用说明

### 环境要求
- JDK 21
- Maven 3.6+
- MySQL 8.0+

### 数据库配置
1. 创建名为 `cardkey_db` 的数据库
2. 导入 `src/main/resources/db/schema.sql` 脚本创建表结构

### 应用配置
在 `application.properties` 中配置数据库连接信息：
```properties
spring.datasource.url=************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
```

### 运行应用
```bash
mvn spring-boot:run
```

### 默认管理员账号
- 用户名：admin
- 密码：123456

## 待完成功能
1. 完善前端界面
2. 添加更多统计分析功能
3. 实现卡密批量导出功能
4. 添加用户权限管理
5. 增加系统日志功能 
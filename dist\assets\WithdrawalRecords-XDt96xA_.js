import{c as O}from"./withdrawal-Ddpl8I2L.js";import{_ as P,b as h,d as y,O as j,c as z,a,w as r,r as p,E as N,P as q,o as f,Q as A,f as b,R as Q,S as G,e as c,i as H,L as C}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const J={class:"withdrawal-records"},K={class:"pagination-container"},X={__name:"WithdrawalRecords",setup(Z){const m=h(!1),_=h([]),v=[{value:1,label:"已通过"},{value:2,label:"已拒绝"},{value:3,label:"已打款"}],n=y({phone:"",status:null,startTime:"",endTime:""}),o=y({pageNum:1,pageSize:10,total:0}),d=h([]),T=l=>{const e=v.find(g=>g.value===l);return e?e.label:"未知状态"},V=l=>{switch(l){case 1:return"success";case 2:return"danger";case 3:return"primary";default:return"info"}},k=l=>{l?(n.startTime=l[0],n.endTime=l[1]):(n.startTime="",n.endTime="")},w=()=>{o.pageNum=1,i()},D=()=>{n.phone="",n.status=null,n.startTime="",n.endTime="",_.value=[],w()},x=l=>{o.pageSize=l,i()},I=l=>{o.pageNum=l,i()},i=async()=>{m.value=!0;try{const l={...n,pageNum:o.pageNum,pageSize:o.pageSize},e=await O(l);e.code===200&&e.data?(d.value=e.data.list||[],o.total=e.data.total||0,o.pageNum=e.data.pageNum||1,o.pageSize=e.data.pageSize||10):(N.error(e.message||"获取提现记录列表失败"),d.value=[],o.total=0)}catch(l){console.error("获取提现记录列表失败:",l),N.error("获取提现记录列表失败"),d.value=[],o.total=0}finally{m.value=!1}};return j(()=>{i()}),(l,e)=>{const g=p("el-input"),u=p("el-form-item"),R=p("el-option"),U=p("el-select"),B=p("el-date-picker"),S=p("el-button"),L=p("el-form"),s=p("el-table-column"),M=p("el-tag"),Y=p("el-table"),E=p("el-pagination"),F=p("el-card"),W=q("loading");return f(),z("div",J,[a(F,{shadow:"hover"},{header:r(()=>e[5]||(e[5]=[b("div",{class:"card-header"},[b("span",null,"提现记录管理")],-1)])),default:r(()=>[a(L,{inline:!0,model:n,class:"search-form"},{default:r(()=>[a(u,{label:"手机号"},{default:r(()=>[a(g,{modelValue:n.phone,"onUpdate:modelValue":e[0]||(e[0]=t=>n.phone=t),placeholder:"手机号",clearable:""},null,8,["modelValue"])]),_:1}),a(u,{label:"状态"},{default:r(()=>[a(U,{modelValue:n.status,"onUpdate:modelValue":e[1]||(e[1]=t=>n.status=t),placeholder:"全部状态",clearable:""},{default:r(()=>[(f(),z(Q,null,G(v,t=>a(R,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"操作时间"},{default:r(()=>[a(B,{modelValue:_.value,"onUpdate:modelValue":e[2]||(e[2]=t=>_.value=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:k},null,8,["modelValue"])]),_:1}),a(u,null,{default:r(()=>[a(S,{type:"primary",onClick:w},{default:r(()=>e[6]||(e[6]=[c("搜索")])),_:1,__:[6]}),a(S,{onClick:D},{default:r(()=>e[7]||(e[7]=[c("重置")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"]),A((f(),H(Y,{data:d.value,border:"",style:{width:"100%"}},{default:r(()=>[a(s,{prop:"id",label:"记录ID",width:"80"}),a(s,{prop:"requestId",label:"申请ID",width:"80"}),a(s,{prop:"userId",label:"用户ID",width:"100"}),a(s,{prop:"phone",label:"手机号",width:"150"}),a(s,{prop:"amount",label:"提现金额",width:"120"},{default:r(t=>[c(C(t.row.amount.toFixed(2))+" 元 ",1)]),_:1}),a(s,{prop:"alipayAccount",label:"支付宝账号",width:"180"}),a(s,{prop:"alipayName",label:"支付宝实名",width:"120"}),a(s,{prop:"status",label:"状态",width:"120"},{default:r(t=>[a(M,{type:V(t.row.status)},{default:r(()=>[c(C(T(t.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(s,{prop:"adminRemark",label:"管理员备注",width:"180"}),a(s,{prop:"transactionId",label:"交易单号",width:"180"}),a(s,{prop:"operationTime",label:"操作时间",width:"180"}),a(s,{prop:"createTime",label:"创建时间",width:"180"})]),_:1},8,["data"])),[[W,m.value]]),b("div",K,[a(E,{"current-page":o.pageNum,"onUpdate:currentPage":e[3]||(e[3]=t=>o.pageNum=t),"page-size":o.pageSize,"onUpdate:pageSize":e[4]||(e[4]=t=>o.pageSize=t),"page-sizes":[10,20,50,100],total:o.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:x,onCurrentChange:I},null,8,["current-page","page-size","total"])])]),_:1})])}}},te=P(X,[["__scopeId","data-v-7a741236"]]);export{te as default};

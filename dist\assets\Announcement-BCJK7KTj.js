import{r as P,s as S,t as H,v as J,w as K,x as W}from"./admin-CZaj3CWx.js";import{_ as X,b as w,d as x,O as Y,c as Z,a as t,w as l,r as u,E as i,P as ee,o as V,Q as te,i as C,e as r,L as f,j as B,f as s,N as ne}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const le={class:"announcement-management"},oe={class:"card-header"},ae={class:"announcement-detail"},se={class:"announcement-content"},re={class:"announcement-info"},ue={class:"dialog-footer"},ie={__name:"Announcement",setup(de){const Q=w([]),A=w(!1),k=w(!1),d=x({view:!1,edit:!1}),c=x({id:null,title:"",content:"",qqGroup:"",status:1,createTime:"",updateTime:""}),o=x({id:null,title:"",content:"",qqGroup:"",status:1}),_=w(null),g=w(!0),D={title:[{required:!0,message:"请输入公告标题",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],content:[{required:!0,message:"请输入公告内容",trigger:"blur"}]},v=async()=>{A.value=!0;try{const a=await P();Q.value=a.data||[]}catch(a){console.error("获取公告列表失败:",a),i.error("获取公告列表失败")}finally{A.value=!1}},E=a=>{Object.assign(c,a),d.view=!0},F=()=>{g.value=!0,T(),d.edit=!0},U=a=>{g.value=!1,T(),Object.assign(o,a),d.edit=!0},$=async a=>{try{await S(a.id),i.success("启用公告成功"),v()}catch(e){console.error("启用公告失败:",e),i.error("启用公告失败")}},h=async a=>{try{await H(a.id),i.success("禁用公告成功"),v()}catch(e){console.error("禁用公告失败:",e),i.error("禁用公告失败")}},z=a=>{ne.confirm("确定要删除该公告吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await J(a.id),i.success("删除公告成功"),v()}catch(e){console.error("删除公告失败:",e),i.error("删除公告失败")}}).catch(()=>{})},T=()=>{_.value&&_.value.resetFields(),o.id=null,o.title="",o.content="",o.qqGroup="",o.status=1},N=async()=>{_.value&&await _.value.validate(async a=>{if(a){k.value=!0;try{g.value?(await K(o),i.success("创建公告成功")):(await W(o),i.success("更新公告成功")),d.edit=!1,v()}catch(e){console.error("提交公告失败:",e),i.error("提交公告失败")}finally{k.value=!1}}})};return Y(()=>{v()}),(a,e)=>{const m=u("el-button"),p=u("el-table-column"),L=u("el-tag"),j=u("el-table"),M=u("el-card"),G=u("el-dialog"),q=u("el-input"),b=u("el-form-item"),O=u("el-switch"),R=u("el-form"),I=ee("loading");return V(),Z("div",le,[t(M,{shadow:"hover"},{header:l(()=>[s("div",oe,[e[8]||(e[8]=s("span",null,"公告管理",-1)),t(m,{type:"primary",onClick:F},{default:l(()=>e[7]||(e[7]=[r("添加公告")])),_:1,__:[7]})])]),default:l(()=>[te((V(),C(j,{data:Q.value,style:{width:"100%"}},{default:l(()=>[t(p,{prop:"id",label:"ID",width:"80"}),t(p,{prop:"title",label:"标题","min-width":"200"}),t(p,{prop:"qqGroup",label:"QQ群链接","min-width":"150"}),t(p,{label:"状态",width:"100"},{default:l(n=>[t(L,{type:n.row.status===1?"success":"info"},{default:l(()=>[r(f(n.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(p,{prop:"createTime",label:"创建时间",width:"180"}),t(p,{prop:"updateTime",label:"更新时间",width:"180"}),t(p,{label:"操作",width:"250",fixed:"right"},{default:l(n=>[t(m,{size:"small",onClick:y=>E(n.row)},{default:l(()=>e[9]||(e[9]=[r("查看")])),_:2,__:[9]},1032,["onClick"]),t(m,{size:"small",type:"primary",onClick:y=>U(n.row)},{default:l(()=>e[10]||(e[10]=[r("编辑")])),_:2,__:[10]},1032,["onClick"]),n.row.status===0?(V(),C(m,{key:0,size:"small",type:"success",onClick:y=>$(n.row)},{default:l(()=>e[11]||(e[11]=[r("启用")])),_:2,__:[11]},1032,["onClick"])):B("",!0),n.row.status===1?(V(),C(m,{key:1,size:"small",type:"warning",onClick:y=>h(n.row)},{default:l(()=>e[12]||(e[12]=[r("禁用")])),_:2,__:[12]},1032,["onClick"])):B("",!0),t(m,{size:"small",type:"danger",onClick:y=>z(n.row)},{default:l(()=>e[13]||(e[13]=[r("删除")])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[I,A.value]])]),_:1}),t(G,{modelValue:d.view,"onUpdate:modelValue":e[0]||(e[0]=n=>d.view=n),title:"公告详情",width:"600px"},{default:l(()=>[s("div",ae,[s("h2",null,f(c.title),1),s("div",se,f(c.content),1),s("div",re,[s("p",null,[e[14]||(e[14]=s("strong",null,"QQ群链接:",-1)),r(" "+f(c.qqGroup),1)]),s("p",null,[e[15]||(e[15]=s("strong",null,"状态:",-1)),r(" "+f(c.status===1?"启用":"禁用"),1)]),s("p",null,[e[16]||(e[16]=s("strong",null,"创建时间:",-1)),r(" "+f(c.createTime),1)]),s("p",null,[e[17]||(e[17]=s("strong",null,"更新时间:",-1)),r(" "+f(c.updateTime),1)])])])]),_:1},8,["modelValue"]),t(G,{modelValue:d.edit,"onUpdate:modelValue":e[6]||(e[6]=n=>d.edit=n),title:g.value?"添加公告":"编辑公告",width:"600px"},{footer:l(()=>[s("span",ue,[t(m,{onClick:e[5]||(e[5]=n=>d.edit=!1)},{default:l(()=>e[18]||(e[18]=[r("取消")])),_:1,__:[18]}),t(m,{type:"primary",onClick:N,loading:k.value},{default:l(()=>e[19]||(e[19]=[r(" 确认 ")])),_:1,__:[19]},8,["loading"])])]),default:l(()=>[t(R,{model:o,rules:D,ref_key:"announcementFormRef",ref:_,"label-width":"100px"},{default:l(()=>[t(b,{label:"标题",prop:"title"},{default:l(()=>[t(q,{modelValue:o.title,"onUpdate:modelValue":e[1]||(e[1]=n=>o.title=n),placeholder:"请输入公告标题"},null,8,["modelValue"])]),_:1}),t(b,{label:"内容",prop:"content"},{default:l(()=>[t(q,{modelValue:o.content,"onUpdate:modelValue":e[2]||(e[2]=n=>o.content=n),type:"textarea",rows:6,placeholder:"请输入公告内容"},null,8,["modelValue"])]),_:1}),t(b,{label:"QQ群链接",prop:"qqGroup"},{default:l(()=>[t(q,{modelValue:o.qqGroup,"onUpdate:modelValue":e[3]||(e[3]=n=>o.qqGroup=n),placeholder:"请输入QQ群链接"},null,8,["modelValue"])]),_:1}),t(b,{label:"状态",prop:"status"},{default:l(()=>[t(O,{modelValue:o.status,"onUpdate:modelValue":e[4]||(e[4]=n=>o.status=n),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},fe=X(ie,[["__scopeId","data-v-a95f1e0d"]]);export{fe as default};

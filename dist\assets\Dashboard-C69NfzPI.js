import{_ as q,d as I,b as F,O as G,c as h,a as t,w as s,r as n,E as x,u as H,o as y,f as a,e as i,L as d,U as J,i as K,j as Y}from"./index-DKz5EXvV.js";import{g as Q,b as W}from"./user-CM_uicz1.js";import{C as D}from"./constants-DT5j32Dw.js";import"./request-YZgFExiB.js";const X={class:"user-dashboard"},Z={class:"card-header"},$={key:0,class:"card-info"},ee={class:"card-type"},ae={class:"card-details"},te={class:"detail-item"},se={class:"detail-item"},re={class:"detail-item"},ne={key:1,class:"no-card"},oe={class:"card-header"},le={class:"user-info-content"},de={class:"user-avatar"},ie={class:"user-details"},ce={class:"detail-item"},ue={class:"detail-item"},pe={class:"detail-item"},_e={class:"detail-item"},me={class:"nav-buttons"},fe={__name:"Dashboard",setup(ye){const v=H(),m=localStorage.getItem("userId"),c=I({username:"",email:"",phone:"",createTime:""}),r=I({hasActiveCard:!1,cardInfo:{cardType:0,typeName:"",activateTime:"",expireTime:"",remainDays:0}}),A=F([{cardType:4,cardCode:"AB12CD34EF56GH78",activateTime:"2023-06-01 12:00:00",expireTime:"2023-07-01 12:00:00",status:1},{cardType:3,cardCode:"IJ90KL12MN34OP56",activateTime:"2023-05-01 12:00:00",expireTime:"2023-05-08 12:00:00",status:0}]),C=o=>{const e=D.find(l=>l.value===o);return e?e.label:"未知"},k=o=>{switch(o){case 1:return"info";case 2:return"warning";case 3:return"success";case 4:return"primary";case 5:return"";case 6:return"danger";case 7:return"danger";default:return"info"}},N=()=>{if(!r.hasActiveCard)return 0;if(r.cardInfo.cardType===7)return 100;if(!D.find(u=>u.value===r.cardInfo.cardType))return 0;let e=0;switch(r.cardInfo.cardType){case 1:e=.5;break;case 2:e=1;break;case 3:e=7;break;case 4:e=30;break;case 5:e=90;break;case 6:e=365;break;default:e=0}if(e===0)return 0;const l=r.cardInfo.remainDays/e*100;return Math.min(Math.max(l,0),100)},P=()=>r.hasActiveCard?r.cardInfo.cardType===7?"success":r.cardInfo.remainDays<=0?"exception":r.cardInfo.remainDays<7?"warning":"success":"",T=()=>{v.push("/user/activate")},w=()=>{v.push("/user/profile")},S=()=>{v.push("/user/promotion/rank")},B=async()=>{if(m)try{const o=await Q(m);Object.assign(c,o.data)}catch(o){console.error("获取用户信息失败:",o),x.error("获取用户信息失败")}},E=async()=>{if(m)try{const o=await W(m);Object.assign(r,o.data)}catch(o){console.error("获取卡密状态失败:",o),x.error("获取卡密状态失败"),r.hasActiveCard=!0,r.cardInfo={cardType:4,typeName:"月卡",activateTime:"2023-06-01 12:00:00",expireTime:"2023-07-01 12:00:00",remainDays:25}}};return G(()=>{B(),E()}),(o,e)=>{const l=n("el-button"),u=n("el-tag"),M=n("el-progress"),O=n("el-empty"),f=n("el-card"),g=n("el-col"),R=n("el-avatar"),V=n("el-icon-check"),b=n("el-icon"),j=n("el-icon-user"),z=n("el-icon-data-analysis"),U=n("el-row"),p=n("el-table-column"),L=n("el-table");return y(),h("div",X,[t(U,{gutter:20},{default:s(()=>[t(g,{span:16},{default:s(()=>[t(f,{shadow:"hover",class:"card-status"},{header:s(()=>[a("div",Z,[e[1]||(e[1]=a("span",null,"卡密状态",-1)),r.hasActiveCard?Y("",!0):(y(),K(l,{key:0,type:"primary",onClick:T},{default:s(()=>e[0]||(e[0]=[i(" 激活卡密 ")])),_:1,__:[0]}))])]),default:s(()=>[r.hasActiveCard?(y(),h("div",$,[a("div",ee,[t(u,{type:k(r.cardInfo.cardType),size:"large"},{default:s(()=>[i(d(C(r.cardInfo.cardType)),1)]),_:1},8,["type"])]),a("div",ae,[a("div",te,[e[2]||(e[2]=a("span",{class:"detail-label"},"激活时间：",-1)),a("span",null,d(r.cardInfo.activateTime),1)]),a("div",se,[e[3]||(e[3]=a("span",{class:"detail-label"},"到期时间：",-1)),a("span",null,d(r.cardInfo.expireTime),1)]),a("div",re,[e[4]||(e[4]=a("span",{class:"detail-label"},"剩余天数：",-1)),a("span",{class:J({"text-danger":r.cardInfo.remainDays<7})},d(r.cardInfo.remainDays)+"天 ",3)]),t(M,{percentage:N(),status:P(),"stroke-width":15,class:"card-progress"},null,8,["percentage","status"])])])):(y(),h("div",ne,[t(O,{description:"您还没有激活任何卡密"},{default:s(()=>[t(l,{type:"primary",onClick:T},{default:s(()=>e[5]||(e[5]=[i("立即激活")])),_:1,__:[5]})]),_:1})]))]),_:1})]),_:1}),t(g,{span:8},{default:s(()=>[t(f,{shadow:"hover",class:"user-info"},{header:s(()=>[a("div",oe,[e[7]||(e[7]=a("span",null,"用户信息",-1)),t(l,{type:"primary",link:"",onClick:w},{default:s(()=>e[6]||(e[6]=[i("编辑资料")])),_:1,__:[6]})])]),default:s(()=>[a("div",le,[a("div",de,[t(R,{size:80,icon:"el-icon-user"})]),a("div",ie,[a("div",ce,[e[8]||(e[8]=a("span",{class:"detail-label"},"用户名：",-1)),a("span",null,d(c.username),1)]),a("div",ue,[e[9]||(e[9]=a("span",{class:"detail-label"},"邮箱：",-1)),a("span",null,d(c.email),1)]),a("div",pe,[e[10]||(e[10]=a("span",{class:"detail-label"},"手机：",-1)),a("span",null,d(c.phone),1)]),a("div",_e,[e[11]||(e[11]=a("span",{class:"detail-label"},"注册时间：",-1)),a("span",null,d(c.createTime),1)])])])]),_:1})]),_:1}),t(g,{span:24},{default:s(()=>[t(f,{shadow:"hover",class:"quick-nav"},{header:s(()=>e[12]||(e[12]=[a("div",{class:"card-header"},[a("span",null,"快速导航")],-1)])),default:s(()=>[a("div",me,[t(l,{type:"primary",onClick:T},{default:s(()=>[t(b,null,{default:s(()=>[t(V)]),_:1}),e[13]||(e[13]=i(" 激活卡密 "))]),_:1,__:[13]}),t(l,{type:"success",onClick:w},{default:s(()=>[t(b,null,{default:s(()=>[t(j)]),_:1}),e[14]||(e[14]=i(" 个人资料 "))]),_:1,__:[14]}),t(l,{type:"warning",onClick:S},{default:s(()=>[t(b,null,{default:s(()=>[t(z)]),_:1}),e[15]||(e[15]=i(" 推广排名 "))]),_:1,__:[15]})])]),_:1})]),_:1})]),_:1}),t(f,{shadow:"hover",class:"mt-20"},{header:s(()=>e[16]||(e[16]=[a("div",{class:"card-header"},[a("span",null,"使用记录")],-1)])),default:s(()=>[t(L,{data:A.value,stripe:"",style:{width:"100%"}},{default:s(()=>[t(p,{prop:"cardType",label:"卡密类型",width:"120"},{default:s(_=>[t(u,{type:k(_.row.cardType)},{default:s(()=>[i(d(C(_.row.cardType)),1)]),_:2},1032,["type"])]),_:1}),t(p,{prop:"cardCode",label:"卡密",width:"180"}),t(p,{prop:"activateTime",label:"激活时间",width:"180"}),t(p,{prop:"expireTime",label:"到期时间",width:"180"}),t(p,{prop:"status",label:"状态"},{default:s(_=>[t(u,{type:_.row.status===1?"success":"info"},{default:s(()=>[i(d(_.row.status===1?"使用中":"已过期"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])]),_:1})])}}},he=q(fe,[["__scopeId","data-v-f8462d56"]]);export{he as default};

<template>
  <div class="users-container">
    <div class="page-header">
      <h2>用户积分管理</h2>
    </div>

    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-card">
      <el-table
        :data="userPointsList"
        style="width: 100%"
        border
        v-loading="loading"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="userId" label="用户ID" width="120" />
        <el-table-column prop="phone" label="用户手机号" width="150" />
        <el-table-column prop="points" label="积分余额" width="120" />
        <el-table-column prop="totalEarnedPoints" label="累计获得" width="120" />
        <el-table-column prop="totalConsumedPoints" label="累计消费" width="120" />
        <el-table-column prop="updateTime" label="最后更新时间" width="180" />
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click="handleViewHistory(scope.row.userId)"
            >
              查看记录
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="handleAddPoints(scope.row)"
            >
              添加积分
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="handleDeductPoints(scope.row)"
            >
              扣减积分
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加积分对话框 -->
    <el-dialog
      v-model="addPointsDialogVisible"
      title="添加积分"
      width="500px"
    >
      <el-form
        ref="addPointsFormRef"
        :model="addPointsForm"
        :rules="addPointsRules"
        label-width="100px"
      >
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="addPointsForm.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="积分数量" prop="points">
          <el-input-number v-model="addPointsForm.points" :min="1" :max="100000" />
        </el-form-item>
        <el-form-item label="积分渠道" prop="channelId">
          <el-select v-model="addPointsForm.channelId" placeholder="请选择积分渠道">
            <el-option
              v-for="channel in channelsList"
              :key="channel.id"
              :label="channel.channelName"
              :value="channel.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input
            v-model="addPointsForm.description"
            type="textarea"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addPointsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAddPoints" :loading="submitLoading">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 扣减积分对话框 -->
    <el-dialog
      v-model="deductPointsDialogVisible"
      title="扣减积分"
      width="500px"
    >
      <el-form
        ref="deductPointsFormRef"
        :model="deductPointsForm"
        :rules="deductPointsRules"
        label-width="100px"
      >
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="deductPointsForm.userId" disabled />
        </el-form-item>
        <el-form-item label="用户手机号" prop="phone">
          <el-input v-model="deductPointsForm.phone" disabled />
        </el-form-item>
        <el-form-item label="当前积分">
          <el-input v-model="deductPointsForm.currentPoints" disabled />
        </el-form-item>
        <el-form-item label="扣减积分" prop="points">
          <el-input-number 
            v-model="deductPointsForm.points" 
            :min="1" 
            :max="deductPointsForm.currentPoints || 100000" 
          />
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input
            v-model="deductPointsForm.description"
            type="textarea"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deductPointsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDeductPoints" :loading="submitLoading">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 积分历史记录对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="积分变动记录"
      width="800px"
    >
      <el-table
        :data="pointsHistoryList"
        style="width: 100%"
        border
        v-loading="historyLoading"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="userId" label="用户ID" width="100" />
        <el-table-column prop="phone" label="用户手机号" width="120" />
        <el-table-column prop="pointsChange" label="变动积分" width="100">
          <template #default="scope">
            <span :class="scope.row.pointsChange > 0 ? 'text-success' : 'text-danger'">
              {{ scope.row.pointsChange > 0 ? '+' : '' }}{{ scope.row.pointsChange }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="operationType" label="操作类型" width="120">
          <template #default="scope">
            <el-tag :type="info">
              {{ scope.row.operationTypeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="channelName" label="渠道" width="120" />
        <el-table-column prop="description" label="备注" />
        <el-table-column prop="createTime" label="操作时间" width="180" />
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="historyCurrentPage"
          v-model:page-size="historyPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="historyTotal"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryCurrentChange"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getUserPointsList, 
  getUserPointsHistory, 
  addUserPoints, 
  addPointsViaChannel,
  deductUserPoints, 
  getChannelsList,
  searchUserPointsByPhone
} from '@/api/points'

// 用户积分列表数据
const userPointsList = ref([])
const loading = ref(false)
const submitLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  phone: ''
})

// 渠道列表
const channelsList = ref([])

// 添加积分相关
const addPointsDialogVisible = ref(false)
const addPointsFormRef = ref(null)
const selectedUser = ref(null)
const addPointsForm = reactive({
  userId: '',
  channelId: '',
  points: 100,
  description: ''
})

// 添加积分表单验证规则
const addPointsRules = {
  userId: [
    { required: true, message: '请输入用户ID', trigger: 'blur' }
  ],
  channelId: [
    { required: true, message: '请选择积分渠道', trigger: 'change' }
  ],
  points: [
    { required: true, message: '请输入积分数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '积分数量必须大于0', trigger: 'blur' }
  ]
}

// 扣减积分相关
const deductPointsDialogVisible = ref(false)
const deductPointsFormRef = ref(null)
const deductPointsForm = reactive({
  userId: '',
  phone: '',
  currentPoints: 0,
  points: 0,
  description: ''
})

// 扣减积分表单验证规则
const deductPointsRules = {
  points: [
    { required: true, message: '请输入积分数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '积分数量必须大于0', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入备注', trigger: 'blur' }
  ]
}

// 积分历史记录相关
const historyDialogVisible = ref(false)
const historyLoading = ref(false)
const pointsHistoryList = ref([])
const historyCurrentPage = ref(1)
const historyPageSize = ref(10)
const historyTotal = ref(0)
const currentUserId = ref(null)

// 获取用户积分列表
const fetchUserPoints = async () => {
  loading.value = true
  try {
    const response = await getUserPointsList({
      pageNum: currentPage.value,
      pageSize: pageSize.value
    })
    
    if (response.code === 0 || response.code === 200) {
      if (response.data && Array.isArray(response.data)) {
        userPointsList.value = response.data
        total.value = response.data.length
      } else {
        userPointsList.value = []
        total.value = 0
      }
    } else {
      ElMessage.error(response.message || '获取用户积分列表失败')
    }
  } catch (error) {
    console.error('获取用户积分列表出错:', error)
    ElMessage.error('获取用户积分列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 获取积分渠道列表
const fetchChannels = async () => {
  try {
    const response = await getChannelsList()
    
    if (response.code === 0 || response.code === 200) {
      if (response.data && Array.isArray(response.data)) {
        channelsList.value = response.data.filter(channel => channel.status === 1)
      } else {
        channelsList.value = []
      }
    } else {
      ElMessage.error(response.message || '获取积分渠道列表失败')
    }
  } catch (error) {
    console.error('获取积分渠道列表出错:', error)
    ElMessage.error('获取积分渠道列表失败，请稍后重试')
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchUserPoints()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchUserPoints()
}

// 搜索处理
const handleSearch = async () => {
  currentPage.value = 1
  loading.value = true
  try {
    if (searchForm.phone) {
      // 使用手机号搜索API
      const response = await searchUserPointsByPhone(searchForm.phone)
      
      if (response.code === 0 || response.code === 200) {
        if (response.data && Array.isArray(response.data)) {
          userPointsList.value = response.data
          total.value = response.data.length
        } else {
          userPointsList.value = []
          total.value = 0
        }
      } else {
        ElMessage.error(response.message || '搜索用户积分信息失败')
      }
    } else {
      // 如果没有输入手机号，则获取所有用户积分列表
      await fetchUserPoints()
    }
  } catch (error) {
    console.error('搜索用户积分信息出错:', error)
    ElMessage.error('搜索用户积分信息失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.phone = ''
  currentPage.value = 1
  fetchUserPoints()
}

// 添加积分
const handleAddPoints = (user) => {
  selectedUser.value = user
  addPointsForm.userId = user.userId
  addPointsForm.channelId = ''
  addPointsForm.points = 100
  addPointsForm.description = ''
  addPointsDialogVisible.value = true
}

// 提交添加积分
const submitAddPoints = async () => {
  if (!addPointsFormRef.value) return
  
  await addPointsFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitLoading.value = true
    try {
      // 通过渠道添加积分
      const response = await addPointsViaChannel({
        userId: addPointsForm.userId,
        points: addPointsForm.points,
        channelId: addPointsForm.channelId,
        description: addPointsForm.description
      })
      
      if (response.code === 0 || response.code === 200) {
        ElMessage.success('添加积分成功')
        addPointsDialogVisible.value = false
        fetchUserPoints()
      } else {
        ElMessage.error(response.message || '添加积分失败')
      }
    } catch (error) {
      console.error('添加积分出错:', error)
      ElMessage.error('添加积分失败，请稍后重试')
    } finally {
      submitLoading.value = false
    }
  })
}

// 扣减积分
const handleDeductPoints = (user) => {
  deductPointsForm.userId = user.userId
  deductPointsForm.phone = user.phone
  deductPointsForm.currentPoints = user.points
  deductPointsForm.points = Math.min(100, user.points)
  deductPointsForm.description = ''
  deductPointsDialogVisible.value = true
}

// 提交扣减积分
const submitDeductPoints = async () => {
  if (!deductPointsFormRef.value) return
  
  await deductPointsFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitLoading.value = true
    try {
      const response = await deductUserPoints({
        userId: deductPointsForm.userId,
        points: deductPointsForm.points,
        description: deductPointsForm.description
      })
      
      if (response.code === 0 || response.code === 200) {
        ElMessage.success('扣减积分成功')
        deductPointsDialogVisible.value = false
        fetchUserPoints()
      } else {
        ElMessage.error(response.message || '扣减积分失败')
      }
    } catch (error) {
      console.error('扣减积分出错:', error)
      ElMessage.error('扣减积分失败，请稍后重试')
    } finally {
      submitLoading.value = false
    }
  })
}

// 查看积分历史
const handleViewHistory = (userId) => {
  currentUserId.value = userId
  historyCurrentPage.value = 1
  historyPageSize.value = 10
  historyDialogVisible.value = true
  fetchPointsHistory(userId)
}

// 获取积分历史记录
const fetchPointsHistory = async (userId) => {
  historyLoading.value = true
  try {
    const response = await getUserPointsHistory(userId, {
      pageNum: historyCurrentPage.value,
      pageSize: historyPageSize.value
    })
    
    if (response.code === 0 || response.code === 200) {
      if (response.data && response.data.list) {
        pointsHistoryList.value = response.data.list
        historyTotal.value = response.data.total
      } else {
        pointsHistoryList.value = []
        historyTotal.value = 0
      }
    } else {
      ElMessage.error(response.message || '获取积分历史记录失败')
    }
  } catch (error) {
    console.error('获取积分历史记录出错:', error)
    ElMessage.error('获取积分历史记录失败，请稍后重试')
  } finally {
    historyLoading.value = false
  }
}

// 积分历史分页处理
const handleHistorySizeChange = (val) => {
  historyPageSize.value = val
  fetchPointsHistory(currentUserId.value)
}

const handleHistoryCurrentChange = (val) => {
  historyCurrentPage.value = val
  fetchPointsHistory(currentUserId.value)
}

// 操作类型映射
const operationTypes = {
  1: { name: '管理员奖励', tag: 'success' },
  2: { name: '推荐奖励', tag: 'success' },
  3: { name: '兑换卡密', tag: 'danger' },
  4: { name: '系统调整', tag: 'info' }
}

// 获取操作类型名称
const getOperationTypeName = (type) => {
  return operationTypes[type]?.name || '未知操作'
}

// 获取操作类型标签样式
const getOperationTypeTag = (type) => {
  return operationTypes[type]?.tag || 'info'
}

// 页面加载时获取数据
onMounted(() => {
  fetchUserPoints()
  fetchChannels()
})
</script>

<style scoped>
.users-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.table-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.text-success {
  color: #67C23A;
  font-weight: bold;
}

.text-danger {
  color: #F56C6C;
  font-weight: bold;
}
</style> 
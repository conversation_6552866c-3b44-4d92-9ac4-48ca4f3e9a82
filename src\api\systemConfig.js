import request from '../utils/request'

// 获取所有系统配置
export function getAllSystemConfigs() {
  return request({
    url: '/system/config/list',
    method: 'get'
  })
}

// 根据ID获取系统配置
export function getSystemConfigById(id) {
  return request({
    url: `/system/config/${id}`,
    method: 'get'
  })
}

// 根据类型获取系统配置
export function getSystemConfigsByType(configType) {
  return request({
    url: `/system/config/type/${configType}`,
    method: 'get'
  })
}

// 获取所有启用的系统配置
export function getEnabledSystemConfigs() {
  return request({
    url: '/system/config/enabled',
    method: 'get'
  })
}

// 创建系统配置
export function createSystemConfig(data) {
  return request({
    url: '/system/config',
    method: 'post',
    data
  })
}

// 更新系统配置
export function updateSystemConfig(data) {
  return request({
    url: '/system/config',
    method: 'put',
    data
  })
}

// 删除系统配置
export function deleteSystemConfig(id) {
  return request({
    url: `/system/config/${id}`,
    method: 'delete'
  })
}

// 启用系统配置
export function enableSystemConfig(id) {
  return request({
    url: `/system/config/${id}/enable`,
    method: 'put'
  })
}

// 禁用系统配置
export function disableSystemConfig(id) {
  return request({
    url: `/system/config/${id}/disable`,
    method: 'put'
  })
} 
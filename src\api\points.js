import request from '../utils/request'

/**
 * 积分渠道管理相关接口
 */

/**
 * 获取积分渠道列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getChannelsList(params) {
  return request({
    url: '/points/channel/list',
    method: 'get',
    params
  })
}

/**
 * 添加积分渠道
 * @param {Object} data 渠道数据
 * @returns {Promise}
 */
export function addChannel(data) {
  return request({
    url: '/points/channel/add',
    method: 'post',
    data
  })
}

/**
 * 更新积分渠道
 * @param {Object} data 渠道数据
 * @returns {Promise}
 */
export function updateChannel(data) {
  return request({
    url: '/points/channel/update',
    method: 'put',
    data
  })
}

/**
 * 删除积分渠道
 * @param {number|string} id 渠道ID
 * @returns {Promise}
 */
export function deleteChannel(id) {
  return request({
    url: `/points/channel/delete/${id}`,
    method: 'delete'
  })
}

/**
 * 更新积分渠道状态
 * @param {number|string} id 渠道ID
 * @param {number} status 状态值（0-禁用，1-启用）
 * @returns {Promise}
 */
export function updateChannelStatus(id, status) {
  return request({
    url: `/points/channel/status`,
    method: 'post',
    data: { id, status }
  })
}

/**
 * 用户积分管理相关接口
 */

/**
 * 获取用户积分列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getUserPointsList(params) {
  return request({
    url: '/points/user/list',
    method: 'get',
    params
  })
}

/**
 * 获取用户积分历史记录
 * @param {number|string} userId 用户ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getUserPointsHistory(userId, params) {
  return request({
    url: `/points/records/${userId}`,
    method: 'get',
    params
  })
}

/**
 * 管理员添加用户积分
 * @param {Object} data 添加积分数据
 * @returns {Promise}
 */
export function addUserPoints(data) {
  return request({
    url: '/points/user/add',
    method: 'post',
    data
  })
}

/**
 * 通过渠道添加用户积分
 * @param {Object} data 添加积分数据
 * @returns {Promise}
 */
export function addPointsViaChannel(data) {
  return request({
    url: '/points/admin/add-by-channel',
    method: 'post',
    data
  })
}

/**
 * 管理员扣减用户积分
 * @param {Object} data 扣减积分数据
 * @returns {Promise}
 */
export function deductUserPoints(data) {
  return request({
    url: '/points/admin/deduct',
    method: 'post',
    data
  })
}

/**
 * 用户积分兑换卡密
 * @param {Object} data 兑换卡密数据
 * @returns {Promise}
 */
export function exchangePointsForCard(data) {
  return request({
    url: '/points/user/exchange',
    method: 'post',
    data
  })
}

/**
 * 积分配置管理相关接口
 */

// 获取积分配置列表
export function getPointsConfigList(params) {
  return request({
    url: '/points/config/list',
    method: 'get',
    params
  })
}

// 添加积分配置
export function addPointsConfig(data) {
  return request({
    url: '/points/config/add',
    method: 'post',
    data
  })
}

// 更新积分配置
export function updatePointsConfig(id, data) {
  return request({
    url: `/points/config/update`,
    method: 'put',
    data: {
      id,
      ...data
    }
  })
}

// 删除积分配置
export function deletePointsConfig(id) {
  return request({
    url: `/points/config/delete/${id}`,
    method: 'delete'
  })
}

// 更新积分配置状态
export function updatePointsConfigStatus(id, status) {
  return request({
    url: `/points/config/update`,
    method: 'put',
    data: { 
      id,
      status 
    }
  })
}

/**
 * 根据手机号查询用户积分信息
 * @param {string} phone 手机号（支持模糊匹配）
 * @returns {Promise}
 */
export function searchUserPointsByPhone(phone) {
  return request({
    url: '/points/user/search',
    method: 'get',
    params: { phone }
  })
}

/**
 * 保存积分渠道（添加或更新）
 * @param {Object} data 渠道数据
 * @returns {Promise}
 * @deprecated 请使用addChannel或updateChannel函数
 */
export function saveChannel(data) {
  if (data.id) {
    // 如果有ID，说明是更新
    return updateChannel(data);
  } else {
    // 否则是添加
    return addChannel(data);
  }
} 
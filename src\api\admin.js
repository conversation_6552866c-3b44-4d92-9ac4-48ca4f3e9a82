import request from '../utils/request'

// 管理员登录
export function login(data) {
  return request({
    url: '/admin/login',
    method: 'post',
    data
  })
}

// 修改管理员密码
export function updatePassword(data) {
  return request({
    url: '/admin/password/update',
    method: 'post',
    data
  })
}

// 获取统计数据
export function getStatistics(params) {
  return request({
    url: '/admin/statistics',
    method: 'get',
    params
  })
}

// 获取仪表盘统计数据
export function getDashboardStatistics(params) {
  return request({
    url: '/dashboard/statistics',
    method: 'get',
    params
  })
}

// 查询用户列表
export function getUserList(data) {
  return request({
    url: '/admin/users',
    method: 'post',
    data
  })
}

// 查询用户详情
export function getUserDetail(id) {
  return request({
    url: `/admin/users/${id}`,
    method: 'get'
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/admin/users/${id}`,
    method: 'delete'
  })
}

// 禁用用户
export function disableUser(id) {
  return request({
    url: `/admin/users/${id}/disable`,
    method: 'put'
  })
}

// 启用用户
export function enableUser(id) {
  return request({
    url: `/admin/users/${id}/enable`,
    method: 'put'
  })
}

// 生成卡密
export function generateCardKeys(data) {
  return request({
    url: '/cardkey/generate',
    method: 'post',
    data
  })
}

// 查询卡密列表
export function getCardKeyList(data) {
  return request({
    url: '/cardkey/list',
    method: 'post',
    data
  })
}

// 查询卡密详情
export function getCardKeyDetail(id) {
  return request({
    url: `/cardkey/${id}`,
    method: 'get'
  })
}

// 删除卡密
export function deleteCardKey(id) {
  return request({
    url: `/cardkey/${id}`,
    method: 'delete'
  })
}

// 获取操作日志列表
export function getOperationLogs(data) {
  return request({
    url: '/logs/list',
    method: 'post',
    data
  })
}

// 查看操作日志详情
export function getOperationLogDetail(id) {
  return request({
    url: `/logs/${id}`,
    method: 'get'
  })
}

// 清理指定天数之前的日志
export function cleanLogs(days) {
  return request({
    url: `/logs/clean/${days}`,
    method: 'delete'
  })
}

// 添加管理员
export function addAdmin(data) {
  return request({
    url: '/admin',
    method: 'post',
    data
  })
}

// 获取管理员信息
export function getAdminInfo(id) {
  return request({
    url: `/admin/${id}`,
    method: 'get'
  })
}

// 更新管理员信息
export function updateAdminInfo(data) {
  return request({
    url: '/admin',
    method: 'put',
    data
  })
}

// 更新管理员状态
export function updateAdminStatus(data) {
  return request({
    url: '/admin/status',
    method: 'put',
    data
  })
}

// 获取管理员列表
export function getAdminList(params) {
  return request({
    url: '/admin/list',
    method: 'get',
    params
  })
}

// 获取所有公告列表
export function getAnnouncementList() {
  return request({
    url: '/announcement',
    method: 'get'
  })
}

// 获取启用的公告列表
export function getEnabledAnnouncementList() {
  return request({
    url: '/announcement/enabled',
    method: 'get'
  })
}

// 获取公告详情
export function getAnnouncementDetail(id) {
  return request({
    url: `/announcement/${id}`,
    method: 'get'
  })
}

// 创建公告
export function createAnnouncement(data) {
  return request({
    url: '/announcement',
    method: 'post',
    data
  })
}

// 更新公告
export function updateAnnouncement(data) {
  return request({
    url: '/announcement',
    method: 'put',
    data
  })
}

// 删除公告
export function deleteAnnouncement(id) {
  return request({
    url: `/announcement/${id}`,
    method: 'delete'
  })
}

// 启用公告
export function enableAnnouncement(id) {
  return request({
    url: `/announcement/${id}/enable`,
    method: 'put'
  })
}

// 禁用公告
export function disableAnnouncement(id) {
  return request({
    url: `/announcement/${id}/disable`,
    method: 'put'
  })
}

// 获取所有推广内容列表
export function getPromotionList() {
  return request({
    url: '/promotion',
    method: 'get'
  })
}

// 获取所有启用的推广内容列表
export function getEnabledPromotionList() {
  return request({
    url: '/promotion/enabled/all',
    method: 'get'
  })
}

// 获取随机一条启用的推广内容
export function getRandomEnabledPromotion() {
  return request({
    url: '/promotion/enabled',
    method: 'get'
  })
}

// 获取推广内容详情
export function getPromotionDetail(id) {
  return request({
    url: `/promotion/${id}`,
    method: 'get'
  })
}

// 创建推广内容
export function createPromotion(data) {
  return request({
    url: '/promotion',
    method: 'post',
    data
  })
}

// 更新推广内容
export function updatePromotion(data) {
  return request({
    url: '/promotion',
    method: 'put',
    data
  })
}

// 删除推广内容
export function deletePromotion(id) {
  return request({
    url: `/promotion/${id}`,
    method: 'delete'
  })
}

// 启用推广内容
export function enablePromotion(id) {
  return request({
    url: `/promotion/${id}/enable`,
    method: 'put'
  })
}

// 禁用推广内容
export function disablePromotion(id) {
  return request({
    url: `/promotion/${id}/disable`,
    method: 'put'
  })
}

// 获取启用的分销配置
export function getEnabledDistributionConfig() {
  return request({
    url: '/distribution/config/enabled',
    method: 'get'
  })
}

// 获取所有分销配置
export function getAllDistributionConfig() {
  return request({
    url: '/distribution/config/list',
    method: 'get'
  })
}

// 根据ID获取分销配置
export function getDistributionConfigById(id) {
  return request({
    url: `/distribution/config/${id}`,
    method: 'get'
  })
}

// 更新分销配置
export function updateDistributionConfig(id, data) {
  return request({
    url: `/distribution/config/${id}`,
    method: 'put',
    data
  })
}

// 启用分销配置
export function enableDistributionConfig(id) {
  return request({
    url: `/distribution/config/${id}/enable`,
    method: 'put'
  })
}

// 禁用分销配置
export function disableDistributionConfig(id) {
  return request({
    url: `/distribution/config/${id}/disable`,
    method: 'put'
  })
}

// 查询分销记录列表
export function getDistributionRecordList(data) {
  return request({
    url: '/distribution/record/list',
    method: 'post',
    data
  })
}

// 查询用户的分销记录
export function getUserDistributionRecords(userId) {
  return request({
    url: `/distribution/record/user/${userId}`,
    method: 'get'
  })
}

// 查询用户的分销统计数据
export function getUserDistributionStatistics(userId) {
  return request({
    url: `/distribution/record/user/${userId}/statistics`,
    method: 'get'
  })
} 

/**
 * 获取推广排名配置
 * @returns {Promise}
 */
export function getPromotionRankConfig() {
  return request({
    url: '/admin/promotion/rank/config',
    method: 'get'
  })
}

/**
 * 保存或更新推广排名配置
 * @param {Object} data 配置数据
 * @returns {Promise}
 */
export function savePromotionRankConfig(data) {
  return request({
    url: '/admin/promotion/rank/config',
    method: 'post',
    data
  })
}

/**
 * 删除推广排名配置
 * @param {Number} id 配置ID
 * @returns {Promise}
 */
export function deletePromotionRankConfig(id) {
  return request({
    url: `/admin/promotion/rank/config/${id}`,
    method: 'delete'
  })
} 
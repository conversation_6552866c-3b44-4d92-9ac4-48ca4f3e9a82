import{_ as k,c as h,a,w as r,r as i,b as g,d as v,u as y,E as l,o as V,e as F,f as _}from"./index-DKz5EXvV.js";import{l as x}from"./admin-CZaj3CWx.js";import{u as I}from"./user-De-42Sfb.js";import"./request-YZgFExiB.js";const b={name:"Login",setup(){const u=y(),o=g(null),m=g(!1),n=I(),s=v({username:"",password:""});return{loginFormRef:o,loginForm:s,loginRules:{username:[{required:!0,message:"请输入管理员用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}]},loading:m,handleLogin:async()=>{o.value&&await o.value.validate(async t=>{if(t){m.value=!0;try{const d={username:s.username,password:s.password},e=await x(d);console.log("管理员登录响应数据:",e);try{e.code===200&&e.data&&e.data.token?(n.setUserInfo({id:e.data.id||e.data.adminId||null,username:e.data.username,token:e.data.token,role:"admin"}),console.log("准备跳转到管理员页面"),u.push("/admin"),l.success("登录成功")):e.data&&e.data.token?(n.setUserInfo({id:e.data.id||e.data.adminId||null,username:e.data.username||s.username,token:e.data.token,role:"admin"}),console.log("准备跳转到管理员页面"),u.push("/admin"),l.success("登录成功")):e.token?(n.setUserInfo({id:e.id||e.adminId||null,username:e.username||s.username,token:e.token,role:"admin"}),console.log("准备跳转到管理员页面"),u.push("/admin"),l.success("登录成功")):(console.error("响应中没有找到token:",e),l.error("登录失败: 未获取到授权信息"))}catch(p){console.error("存储管理员信息失败:",p),l.error("登录数据处理失败")}}catch(d){console.error("登录失败:",d),l.error("登录失败，请检查用户名和密码")}finally{m.value=!1}}})}}}},R={class:"login-container"};function U(u,o,m,n,s,w){const c=i("el-input"),t=i("el-form-item"),d=i("el-button"),e=i("el-form"),p=i("el-card");return V(),h("div",R,[a(p,{class:"login-card"},{header:r(()=>o[2]||(o[2]=[_("div",{class:"card-header"},[_("h2",null,"卡密管理系统")],-1)])),default:r(()=>[a(e,{model:n.loginForm,rules:n.loginRules,ref:"loginFormRef","label-width":"0"},{default:r(()=>[a(t,{prop:"username"},{default:r(()=>[a(c,{modelValue:n.loginForm.username,"onUpdate:modelValue":o[0]||(o[0]=f=>n.loginForm.username=f),placeholder:"管理员用户名","prefix-icon":"el-icon-user"},null,8,["modelValue"])]),_:1}),a(t,{prop:"password"},{default:r(()=>[a(c,{modelValue:n.loginForm.password,"onUpdate:modelValue":o[1]||(o[1]=f=>n.loginForm.password=f),type:"password",placeholder:"密码","prefix-icon":"el-icon-lock","show-password":""},null,8,["modelValue"])]),_:1}),a(t,null,{default:r(()=>[a(d,{type:"primary",loading:n.loading,onClick:n.handleLogin,style:{width:"100%"}},{default:r(()=>o[3]||(o[3]=[F("管理员登录")])),_:1,__:[3]},8,["loading","onClick"])]),_:1})]),_:1},8,["model","rules"])]),_:1})])}const N=k(b,[["render",U],["__scopeId","data-v-423e3de3"]]);export{N as default};

import{c as M}from"./user-CM_uicz1.js";import{u as P}from"./user-De-42Sfb.js";import{_ as U,b as c,d as A,g as $,O as j,c as _,a,w as l,r as i,E as L,o as p,j as O,f as e,e as u,L as o,U as q}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const F={class:"promotion-rank"},G={key:0,class:"loading-container"},H={key:1},J={key:0,class:"activity-info"},K={class:"activity-details"},Q={class:"rank-list-container"},W={class:"referral-details"},X={class:"pagination-container"},Y={class:"my-rank-container"},Z={class:"my-rank-card"},ee={class:"rank-info"},te={class:"rank-item"},se={class:"rank-value"},ae={class:"rank-item"},ne={class:"rank-value"},oe={class:"rank-item"},le={class:"rank-value"},ie={class:"rank-item"},re={class:"rank-value"},de={class:"rank-tips"},ce={class:"share-content"},ue={class:"invitation-code"},me={__name:"PromotionRank",setup(ve){const k=c(!0),g=P(),n=A({startTime:"",endTime:"",description:"",rankList:[]}),R=$(()=>n.startTime&&n.endTime),f=c(0),h=c(0),C=c(0),w=c(0),y=c(""),b=c(!1),V=async()=>{k.value=!0;try{const s=await M();if(s.data){n.startTime=s.data.startTime||"",n.endTime=s.data.endTime||"",n.description=s.data.description||"",n.rankList=s.data.rankList||[];const t=g.id;if(t){const r=n.rankList.find(v=>v.userId===t);r&&(f.value=n.rankList.findIndex(v=>v.userId===t)+1,h.value=r.totalReferrals,C.value=r.level1Count,w.value=r.level2Count,y.value=r.invitationCode||g.invitationCode)}}}catch(s){console.error("获取推广排名数据失败:",s),L.error("获取推广排名数据失败")}finally{k.value=!1}},I=s=>s===1?"rank-first":s===2?"rank-second":s===3?"rank-third":"",z=()=>{b.value=!0},D=()=>{const s=document.createElement("textarea");s.value=y.value,document.body.appendChild(s),s.select(),document.execCommand("copy"),document.body.removeChild(s),L.success("邀请码已复制到剪贴板")};return j(()=>{V()}),(s,t)=>{const r=i("el-skeleton"),v=i("el-alert"),m=i("el-table-column"),x=i("el-tag"),E=i("el-table"),N=i("el-pagination"),T=i("el-button"),S=i("el-card"),B=i("el-dialog");return p(),_("div",F,[a(S,{shadow:"hover"},{header:l(()=>t[1]||(t[1]=[e("div",{class:"card-header"},[e("span",null,"推广排名")],-1)])),default:l(()=>[k.value?(p(),_("div",G,[a(r,{rows:10,animated:""})])):(p(),_("div",H,[R.value?(p(),_("div",J,[a(v,{title:"推广活动",type:"success",closable:!1,"show-icon":""},{default:l(()=>[e("div",K,[e("p",null,[t[2]||(t[2]=e("strong",null,"活动时间：",-1)),u(o(n.startTime)+" 至 "+o(n.endTime),1)]),e("p",null,[t[3]||(t[3]=e("strong",null,"活动说明：",-1)),u(o(n.description),1)])])]),_:1})])):O("",!0),e("div",Q,[e("h3",null,[t[4]||(t[4]=u("推广排名榜 ")),e("small",null,"(前"+o(n.rankList.length)+"名)",1)]),a(E,{data:n.rankList,style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:l(()=>[a(m,{type:"index",label:"排名",width:"80",align:"center"},{default:l(d=>[e("div",{class:q(["rank-number",I(d.$index+1)])},o(d.$index+1),3)]),_:1}),a(m,{prop:"phoneMasked",label:"用户","min-width":"120"}),a(m,{prop:"invitationCode",label:"邀请码","min-width":"100"}),a(m,{prop:"totalReferrals",label:"推广总人数","min-width":"100",align:"center",sortable:""}),a(m,{label:"推广详情","min-width":"180"},{default:l(d=>[e("div",W,[a(x,{size:"small"},{default:l(()=>[u("一级: "+o(d.row.level1Count),1)]),_:2},1024),a(x,{size:"small",type:"success"},{default:l(()=>[u("二级: "+o(d.row.level2Count),1)]),_:2},1024)])]),_:1}),a(m,{prop:"registerTime",label:"注册时间","min-width":"180"})]),_:1},8,["data"]),e("div",X,[a(N,{background:"",layout:"prev, pager, next",total:30,"page-size":30,disabled:!0})])]),e("div",Y,[t[11]||(t[11]=e("h3",null,"我的推广情况",-1)),e("div",Z,[e("div",ee,[e("div",te,[t[5]||(t[5]=e("div",{class:"rank-label"},"我的排名",-1)),e("div",se,o(f.value>0?f.value:"未上榜"),1)]),e("div",ae,[t[6]||(t[6]=e("div",{class:"rank-label"},"推广总人数",-1)),e("div",ne,o(h.value),1)]),e("div",oe,[t[7]||(t[7]=e("div",{class:"rank-label"},"一级推广",-1)),e("div",le,o(C.value),1)]),e("div",ie,[t[8]||(t[8]=e("div",{class:"rank-label"},"二级推广",-1)),e("div",re,o(w.value),1)])]),e("div",de,[t[10]||(t[10]=e("p",null,"邀请更多用户注册，提升您的排名！",-1)),a(T,{type:"primary",onClick:z},{default:l(()=>t[9]||(t[9]=[u("分享邀请码")])),_:1,__:[9]})])])])]))]),_:1}),a(B,{modelValue:b.value,"onUpdate:modelValue":t[0]||(t[0]=d=>b.value=d),title:"分享邀请码",width:"400px"},{default:l(()=>[e("div",ce,[t[13]||(t[13]=e("p",null,"您的邀请码：",-1)),e("div",ue,[e("span",null,o(y.value),1),a(T,{type:"primary",size:"small",onClick:D},{default:l(()=>t[12]||(t[12]=[u(" 复制 ")])),_:1,__:[12]})]),t[14]||(t[14]=e("p",{class:"share-tip"},"分享给好友，邀请注册后可获得推广奖励！",-1))])]),_:1},8,["modelValue"])])}}},ye=U(me,[["__scopeId","data-v-3c13590b"]]);export{ye as default};

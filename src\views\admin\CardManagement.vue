<template>
  <div class="card-management-container">
    <div class="header">
      <h2>卡密列表</h2>
      <div class="header-actions">
        <el-button type="primary" @click="openGenerateDialog">生成卡密</el-button>
        <el-button type="success" @click="exportCardKeys">导出卡密</el-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="卡密码">
          <el-input v-model="searchForm.keyCode" placeholder="输入卡密码" clearable />
        </el-form-item>
        <el-form-item label="卡密类型">
          <el-select v-model="searchForm.keyType" placeholder="选择卡密类型" clearable>
            <el-option
              v-for="item in CARD_TYPES"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="卡密状态">
          <el-select v-model="searchForm.status" placeholder="选择卡密状态" clearable>
            <el-option label="未激活" :value="0" />
            <el-option label="已激活" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchCardKeys">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 卡密列表 -->
    <el-card class="card-list">
      <el-table
        v-loading="loading"
        :data="cardList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="keyCode" label="卡密码" width="220" />
        <el-table-column label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getCardTypeTag(scope.row.keyType)">
              {{ scope.row.keyTypeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.status === 0 ? 'success' : 'warning'">
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="experienceHours" label="体验时长(小时)" width="120">
          <template #default="scope">
            {{ scope.row.keyType === 1 ? scope.row.experienceHours : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="价格" width="100">
          <template #default="scope">
            {{ scope.row.price ? scope.row.price.toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="activateTime" label="激活时间" width="180" />
        <el-table-column prop="expireTime" label="到期时间" width="180" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="remark" label="备注" width="120" />
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="scope">
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteCard(scope.row.id)"
              :disabled="scope.row.status !== 0"
            >
              删除
            </el-button>
            <el-button 
              type="primary" 
              size="small" 
              @click="copyCardKey(scope.row.keyCode)"
            >
              复制
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 生成卡密对话框 -->
    <el-dialog
      v-model="generateDialog.visible"
      title="生成卡密"
      width="500px"
    >
      <el-form
        :model="generateForm"
        :rules="generateRules"
        ref="generateFormRef"
        label-width="100px"
      >
        <el-form-item label="卡密类型" prop="keyType">
          <el-select v-model="generateForm.keyType" placeholder="选择卡密类型" style="width: 100%">
            <el-option
              v-for="item in CARD_TYPES"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="体验时长" prop="experienceHours" v-if="generateForm.keyType === 1">
          <el-input-number v-model="generateForm.experienceHours" :min="1" :max="720" style="width: 100%" />
        </el-form-item>
        <el-form-item label="生成数量" prop="count">
          <el-input-number v-model="generateForm.count" :min="1" :max="100" style="width: 100%" />
        </el-form-item>
        <el-form-item label="卡密价格" prop="price">
          <el-input-number v-model="generateForm.price" :min="0" :max="1000" style="width: 100%" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="generateForm.remark" type="textarea" placeholder="可选备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="generateDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitGenerateForm" :loading="generateDialog.loading">
            生成
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCardKeyList, generateCardKeys, deleteCardKey } from '../../api/admin'
import { CARD_TYPES } from '../../utils/constants'

// 搜索表单
const searchForm = reactive({
  keyCode: '',
  keyType: null,
  status: null
})

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 卡密列表
const cardList = ref([])
const loading = ref(false)

// 生成卡密对话框
const generateDialog = reactive({
  visible: false,
  loading: false
})

// 生成卡密表单
const generateFormRef = ref(null)
const generateForm = reactive({
  keyType: 1,
  experienceHours: 24,
  count: 10,
  price: 0,
  remark: ''
})

// 生成卡密表单验证规则
const generateRules = {
  keyType: [{ required: true, message: '请选择卡密类型', trigger: 'change' }],
  experienceHours: [{ required: true, message: '请输入体验时长', trigger: 'change' }],
  count: [{ required: true, message: '请输入生成数量', trigger: 'change' }],
  price: [{ required: true, message: '请输入卡密价格', trigger: 'change' }]
}

// 获取卡密列表
const fetchCardKeys = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    const res = await getCardKeyList(params)
    if (res.code === 200 && res.data) {
      cardList.value = res.data.list || []
      pagination.total = res.data.total || 0
      pagination.pageNum = res.data.pageNum || 1
      pagination.pageSize = res.data.pageSize || 10
    } else {
      ElMessage.error(res.message || '获取卡密列表失败')
      cardList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取卡密列表失败:', error)
    ElMessage.error('获取卡密列表失败')
    cardList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索卡密
const searchCardKeys = () => {
  pagination.pageNum = 1
  fetchCardKeys()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  searchForm.keyType = null
  searchForm.status = null
  pagination.pageNum = 1
  fetchCardKeys()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchCardKeys()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  pagination.pageNum = page
  fetchCardKeys()
}

// 打开生成卡密对话框
const openGenerateDialog = () => {
  generateForm.keyType = 1
  generateForm.experienceHours = 24
  generateForm.count = 10
  generateForm.price = 0
  generateForm.remark = ''
  generateDialog.visible = true
}

// 提交生成卡密表单
const submitGenerateForm = async () => {
  if (!generateFormRef.value) return
  
  await generateFormRef.value.validate(async (valid) => {
    if (valid) {
      generateDialog.loading = true
      try {
        const res = await generateCardKeys(generateForm)
        if (res.code === 200) {
          ElMessage.success(`成功生成 ${generateForm.count} 张卡密`)
        generateDialog.visible = false
        fetchCardKeys() // 刷新列表
        } else {
          ElMessage.error(res.message || '生成卡密失败')
        }
      } catch (error) {
        console.error('生成卡密失败:', error)
        ElMessage.error('生成卡密失败')
      } finally {
        generateDialog.loading = false
      }
    }
  })
}

// 删除卡密
const deleteCard = (id) => {
  ElMessageBox.confirm('确定要删除该卡密吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteCardKey(id)
      if (res.code === 200) {
      ElMessage.success('删除成功')
      fetchCardKeys() // 刷新列表
      } else {
        ElMessage.error(res.message || '删除卡密失败')
      }
    } catch (error) {
      console.error('删除卡密失败:', error)
      ElMessage.error('删除卡密失败')
    }
  }).catch(() => {})
}

// 复制卡密
const copyCardKey = (keyCode) => {
  // 使用 navigator.clipboard API 复制到剪贴板
  navigator.clipboard.writeText(keyCode).then(() => {
    ElMessage.success('已复制到剪贴板')
  }).catch(err => {
    console.error('复制失败:', err)
    ElMessage.error('复制失败')
  })
}

// 导出卡密
const exportCardKeys = () => {
  ElMessageBox.confirm('导出功能暂未实现，请联系管理员', '提示', {
    confirmButtonText: '确定',
    type: 'info',
    showCancelButton: false
  })
}

// 获取卡密类型标签样式
const getCardTypeTag = (type) => {
  switch (type) {
    case 1: return 'info'     // 体验卡
    case 2: return 'warning'  // 天卡
    case 3: return 'success'  // 周卡
    case 4: return 'primary'  // 月卡
    case 5: return ''         // 季卡
    case 6: return 'danger'   // 年卡
    case 7: return 'danger'   // 永久卡
    default: return 'info'
  }
}

onMounted(() => {
  fetchCardKeys()
})
</script>

<style scoped>
.card-management-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  font-size: 22px;
}

.search-card {
  margin-bottom: 20px;
}

.card-list {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
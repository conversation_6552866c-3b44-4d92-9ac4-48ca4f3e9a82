<template>
  <div class="admin-management">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>管理员管理</span>
          <el-button type="primary" @click="handleAddAdmin">添加管理员</el-button>
        </div>
      </template>
      
      <!-- 管理员表格 -->
      <el-table
        v-loading="loading"
        :data="adminList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.role === 2 ? 'danger' : 'primary'">
              {{ scope.row.role === 2 ? '超级管理员' : '普通管理员' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="lastLoginTime" label="最后登录时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button 
              :type="scope.row.status === 1 ? 'danger' : 'success'" 
              link 
              @click="handleToggleStatus(scope.row)"
              :disabled="scope.row.role === 2 && isSelf(scope.row.id)"
            >
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button 
              type="primary" 
              link 
              @click="handleEdit(scope.row)"
              :disabled="scope.row.role === 2 && !isSelf(scope.row.id)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 添加/编辑管理员对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑管理员' : '添加管理员'"
      width="500px"
    >
      <el-form
        ref="adminFormRef"
        :model="adminForm"
        :rules="adminRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="adminForm.username" :disabled="isEdit" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="adminForm.email" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input v-model="adminForm.password" type="password" show-password />
        </el-form-item>
        
        <el-form-item label="角色" prop="role" v-if="isSuperAdmin">
          <el-select v-model="adminForm.role">
            <el-option label="普通管理员" :value="1" />
            <el-option label="超级管理员" :value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAdminList, addAdmin, updateAdminInfo, updateAdminStatus } from '../../api/admin'

const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const adminFormRef = ref(null)

const currentAdminId = localStorage.getItem('adminId')
const currentAdminRole = localStorage.getItem('adminRole')

// 是否是超级管理员
const isSuperAdmin = computed(() => {
  return currentAdminRole === '2'
})

// 是否是当前登录的管理员
const isSelf = (id) => {
  return id.toString() === currentAdminId
}

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 管理员列表
const adminList = ref([])

// 管理员表单
const adminForm = reactive({
  id: '',
  username: '',
  email: '',
  password: '',
  role: 1
})

// 表单验证规则
const adminRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 获取管理员列表
const fetchAdminList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    const res = await getAdminList(params)
    adminList.value = res.data || []
    pagination.total = res.total || 0
  } catch (error) {
    console.error('获取管理员列表失败:', error)
    ElMessage.error('获取管理员列表失败')
    
    // 使用模拟数据
    adminList.value = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 2,
        status: 1,
        createTime: '2023-06-01 12:00:00',
        lastLoginTime: '2023-06-10 12:00:00'
      },
      {
        id: 2,
        username: 'manager',
        email: '<EMAIL>',
        role: 1,
        status: 1,
        createTime: '2023-06-02 12:00:00',
        lastLoginTime: '2023-06-09 12:00:00'
      }
    ]
    pagination.total = 2
  } finally {
    loading.value = false
  }
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchAdminList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.pageNum = val
  fetchAdminList()
}

// 添加管理员
const handleAddAdmin = () => {
  isEdit.value = false
  adminForm.id = ''
  adminForm.username = ''
  adminForm.email = ''
  adminForm.password = ''
  adminForm.role = 1
  
  dialogVisible.value = true
}

// 编辑管理员
const handleEdit = (admin) => {
  isEdit.value = true
  adminForm.id = admin.id
  adminForm.username = admin.username
  adminForm.email = admin.email
  adminForm.password = ''
  adminForm.role = admin.role
  
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!adminFormRef.value) return
  
  await adminFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (isEdit.value) {
          // 编辑管理员
          await updateAdminInfo({
            id: adminForm.id,
            email: adminForm.email,
            role: adminForm.role
          })
          ElMessage.success('管理员信息更新成功')
        } else {
          // 添加管理员
          await addAdmin(adminForm)
          ElMessage.success('管理员添加成功')
        }
        
        dialogVisible.value = false
        fetchAdminList()
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 切换管理员状态
const handleToggleStatus = (admin) => {
  const statusText = admin.status === 1 ? '禁用' : '启用'
  
  ElMessageBox.confirm(`确定要${statusText}管理员 ${admin.username} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await updateAdminStatus({
        id: admin.id,
        status: admin.status === 1 ? 0 : 1
      })
      
      ElMessage.success(`${statusText}管理员成功`)
      fetchAdminList()
    } catch (error) {
      console.error(`${statusText}管理员失败:`, error)
      ElMessage.error(`${statusText}管理员失败`)
    }
  }).catch(() => {})
}

onMounted(() => {
  fetchAdminList()
})
</script>

<style scoped>
.admin-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 
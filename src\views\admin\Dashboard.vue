<template>
  <div class="dashboard-container">
    <!-- 数据概览 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="data-card">
          <div class="data-header">
            <div class="data-title">总用户数</div>
            <el-icon class="data-icon" color="#409EFF"><el-icon-user /></el-icon>
          </div>
          <div class="data-value">{{ statistics.userStatistics?.totalUsers || 0 }}</div>
          <div class="data-footer">
            <span class="data-label">新增: </span>
            <span class="data-change">{{ statistics.userStatistics?.newUsers || 0 }}</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="data-card">
          <div class="data-header">
            <div class="data-title">总卡密数</div>
            <el-icon class="data-icon" color="#67C23A"><el-icon-ticket /></el-icon>
          </div>
          <div class="data-value">{{ statistics.cardKeyStatistics?.totalCardKeys || 0 }}</div>
          <div class="data-footer">
            <span class="data-label">已使用: </span>
            <span class="data-change">{{ statistics.cardKeyStatistics?.usedCardKeys || 0 }}</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="data-card">
          <div class="data-header">
            <div class="data-title">未使用卡密</div>
            <el-icon class="data-icon" color="#E6A23C"><el-icon-document-checked /></el-icon>
          </div>
          <div class="data-value">{{ statistics.cardKeyStatistics?.unusedCardKeys || 0 }}</div>
          <div class="data-footer">
            <span class="data-label">已过期: </span>
            <span class="data-change">{{ statistics.cardKeyStatistics?.expiredCardKeys || 0 }}</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="data-card">
          <div class="data-header">
            <div class="data-title">使用率</div>
            <el-icon class="data-icon" color="#F56C6C"><el-icon-data-line /></el-icon>
          </div>
          <div class="data-value">
            {{ statistics.cardKeyStatistics?.usageRate !== undefined ? 
              statistics.cardKeyStatistics.usageRate : 0 }}%
          </div>
          <div class="data-footer">
            <el-progress 
              :percentage="statistics.cardKeyStatistics?.usageRate !== undefined ? 
                statistics.cardKeyStatistics.usageRate  : 0" 
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 卡密类型分布 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>卡密类型分布</span>
            </div>
          </template>
          <div class="chart-container" ref="cardTypeChartRef"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>最近卡密激活记录</span>
              <el-button type="primary" link>查看更多</el-button>
            </div>
          </template>
          <el-table :data="statistics.recentActivations || []" stripe style="width: 100%">
            <el-table-column prop="userPhone" label="用户" width="120" />
            <el-table-column prop="keyType" label="卡密类型" width="120">
              <template #default="scope">
                <el-tag :type="getCardTypeTag(scope.row.keyType)">
                  {{ scope.row.typeName || getCardTypeName(scope.row.keyType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="activateTime" label="激活时间" />
            <el-table-column prop="expireTime" label="到期时间" />
          </el-table>
          <div v-if="!(statistics.recentActivations && statistics.recentActivations.length)" class="no-data">
            <el-empty description="暂无激活记录" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { getDashboardStatistics } from '../../api/admin'
import { CARD_TYPES } from '../../utils/constants'

// 不再需要单独注册组件
// echarts.use([TitleComponent, TooltipComponent, LegendComponent, PieChart, CanvasRenderer])

const cardTypeChartRef = ref(null)
let cardTypeChart = null

const statistics = reactive({
  cardKeyStatistics: {},
  userStatistics: {},
  recentActivations: []
})

// 获取卡密类型名称
const getCardTypeName = (type) => {
  const cardType = CARD_TYPES.find(item => item.value === type)
  return cardType ? cardType.label : '未知'
}

// 获取卡密类型标签类型
const getCardTypeTag = (type) => {
  switch (type) {
    case 1: return 'info'
    case 2: return 'warning'
    case 3: return 'success'
    case 4: return 'primary'
    case 5: return ''
    case 6: return 'danger'
    case 7: return 'danger'
    default: return 'info'
  }
}

// 初始化卡密类型分布图表
const initCardTypeChart = () => {
  if (!cardTypeChartRef.value) return
  
  cardTypeChart = echarts.init(cardTypeChartRef.value)
  
  const option = {
    title: {
      text: '卡密类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: CARD_TYPES.map(item => item.label)
    },
    series: [
      {
        name: '卡密类型',
        type: 'pie',
        radius: '60%',
        center: ['50%', '60%'],
        data: statistics.cardKeyStatistics?.typeDistribution
          ? statistics.cardKeyStatistics.typeDistribution.map(item => ({
              name: item.typeName || getCardTypeName(item.type),
              value: item.count,
              itemStyle: {
                color: CARD_TYPES.find(type => type.value === item.type)?.color || '#999'
              }
            }))
          : [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  cardTypeChart.setOption(option)
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 添加period参数，默认为week
    const res = await getDashboardStatistics()
    
    if (res.code === 200 && res.data) {
      Object.assign(statistics, res.data)
      
      // 更新图表
      if (cardTypeChart) {
        cardTypeChart.setOption({
          series: [
            {
              data: statistics.cardKeyStatistics?.typeDistribution
                ? statistics.cardKeyStatistics.typeDistribution.map(item => ({
                    name: item.typeName || getCardTypeName(item.type),
                    value: item.count,
                    itemStyle: {
                      color: CARD_TYPES.find(type => type.value === item.type)?.color || '#999'
                    }
                  }))
                : []
            }
          ]
        })
      }
    } else {
      throw new Error('获取数据失败')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

onMounted(() => {
  fetchStatistics()
  
  // 设置一个延时，确保DOM已经渲染
  setTimeout(() => {
    initCardTypeChart()
    
    // 窗口大小改变时重绘图表
    window.addEventListener('resize', () => {
      if (cardTypeChart) {
        cardTypeChart.resize()
      }
    })
  }, 100)
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.data-card {
  height: 160px;
  display: flex;
  flex-direction: column;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.data-title {
  font-size: 16px;
  color: #606266;
}

.data-icon {
  font-size: 24px;
}

.data-value {
  font-size: 28px;
  font-weight: bold;
  margin: 10px 0;
}

.data-footer {
  margin-top: auto;
  font-size: 14px;
}

.data-label {
  color: #909399;
}

.data-change {
  color: #67C23A;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.no-data {
  margin: 20px 0;
}

.mt-20 {
  margin-top: 20px;
}
</style> 
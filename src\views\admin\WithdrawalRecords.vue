<template>
  <div class="withdrawal-records">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>提现记录管理</span>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="手机号" clearable />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 提现记录表格 -->
      <el-table
        v-loading="loading"
        :data="recordsList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="记录ID" width="80" />
        <el-table-column prop="requestId" label="申请ID" width="80" />
        <el-table-column prop="userId" label="用户ID" width="100" />
        <el-table-column prop="phone" label="手机号" width="150" />
        <el-table-column prop="amount" label="提现金额" width="120">
          <template #default="scope">
            {{ scope.row.amount.toFixed(2) }} 元
          </template>
        </el-table-column>
        <el-table-column prop="alipayAccount" label="支付宝账号" width="180" />
        <el-table-column prop="alipayName" label="支付宝实名" width="120" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="adminRemark" label="管理员备注" width="180" />
        <el-table-column prop="transactionId" label="交易单号" width="180" />
        <el-table-column prop="operationTime" label="操作时间" width="180" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getWithdrawalRecords } from '../../api/withdrawal'

const loading = ref(false)
const dateRange = ref([])

// 状态选项
const statusOptions = [
  { value: 1, label: '已通过' },
  { value: 2, label: '已拒绝' },
  { value: 3, label: '已打款' }
]

// 搜索表单
const searchForm = reactive({
  phone: '',
  status: null,
  startTime: '',
  endTime: ''
})

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 提现记录列表
const recordsList = ref([])

// 获取状态名称
const getStatusName = (status) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : '未知状态'
}

// 获取状态对应的标签类型
const getStatusType = (status) => {
  switch (status) {
    case 1: return 'success'  // 已通过
    case 2: return 'danger'   // 已拒绝
    case 3: return 'primary'  // 已打款
    default: return 'info'
  }
}

// 日期范围变化
const handleDateChange = (val) => {
  if (val) {
    searchForm.startTime = val[0]
    searchForm.endTime = val[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  fetchRecordsList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.phone = ''
  searchForm.status = null
  searchForm.startTime = ''
  searchForm.endTime = ''
  dateRange.value = []
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchRecordsList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.pageNum = val
  fetchRecordsList()
}

// 获取提现记录列表
const fetchRecordsList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    const res = await getWithdrawalRecords(params)
    if (res.code === 200 && res.data) {
      recordsList.value = res.data.list || []
      pagination.total = res.data.total || 0
      pagination.pageNum = res.data.pageNum || 1
      pagination.pageSize = res.data.pageSize || 10
    } else {
      ElMessage.error(res.message || '获取提现记录列表失败')
      recordsList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取提现记录列表失败:', error)
    ElMessage.error('获取提现记录列表失败')
    recordsList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchRecordsList()
})
</script>

<style scoped>
.withdrawal-records {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 
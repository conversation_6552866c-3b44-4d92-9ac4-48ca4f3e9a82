import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3333,
    host: true,
    proxy: {
      '/api': {
        //target: 'http://*************:8527',
        target: 'http://127.0.0.1:8527',
        changeOrigin: true,
        // 注释掉 rewrite 配置，如果后端接口已经有 /api 前缀
        // rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  esbuild: {
    jsxFactory: 'h',
    jsxFragment: 'Fragment',
    loader: {
      '.js': 'jsx'
    }
  }
}) 
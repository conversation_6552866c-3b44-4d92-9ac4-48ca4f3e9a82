import{a as X,d as Z,e as ee,b as te,c as le}from"./admin-CZaj3CWx.js";import{C as ae,a as ne}from"./constants-DT5j32Dw.js";import{_ as oe,b as N,d as D,O as se,c as h,a as t,w as n,r as d,E as _,P as re,o as v,Q as ie,f as s,e as o,i as V,L as r,R as de,S as ue,j as B,N as E}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const pe={class:"user-management"},me={class:"pagination-container"},ce={class:"card-header"},fe={key:0},ge={class:"dialog-footer"},_e={__name:"UserManagement",setup(ye){const U=N(!1),b=N(!1),p=D({phone:"",referrerId:"",status:null}),u=D({pageNum:1,pageSize:10,total:0}),C=N([]),i=D({id:"",phone:"",invitationCode:"",referrerId:"",referrerPhone:"",referredCount:0,status:0,createTime:"",updateTime:""}),L=l=>{const e=ae.find(c=>c.value===l);return e?e.label:`未知类型(${l})`},P=l=>{const e=ne.find(c=>c.value===l);return e?e.label:`未知状态(${l})`},M=l=>{if(typeof l=="string")return l==="未使用"?"success":l==="已激活"||l==="已使用"?"primary":"info";switch(l){case 0:return"success";case 1:return"primary";case 2:return"info";default:return"info"}},x=()=>{u.pageNum=1,w()},j=()=>{Object.keys(p).forEach(l=>{p[l]=""}),p.status=null,x()},A=l=>{u.pageSize=l,w()},K=l=>{u.pageNum=l,w()},w=async()=>{U.value=!0;try{const l={...p,pageNum:u.pageNum,pageSize:u.pageSize},e=await X(l);e.code===200&&e.data?(C.value=e.data.list||[],u.total=e.data.total||0,u.pageNum=e.data.pageNum||1,u.pageSize=e.data.pageSize||10):(_.error(e.message||"获取用户列表失败"),C.value=[],u.total=0)}catch(l){console.error("获取用户列表失败:",l),_.error("获取用户列表失败"),C.value=[],u.total=0}finally{U.value=!1}},R=async l=>{try{const e=await te(l.id);e.code===200&&e.data?(Object.assign(i,e.data),b.value=!0):_.error(e.message||"获取用户详情失败")}catch(e){console.error("获取用户详情失败:",e),_.error("获取用户详情失败")}},$=(l,e=!1)=>{const c=l.status===0,f=c?"禁用":"启用";E.confirm(`确定要${f}用户 ${l.phone||l.id} 吗?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const S=await(c?Z:ee)(l.id);if(S.code===200){if(l.status=c?1:0,e){const g=C.value.find(z=>z.id===l.id);g&&(g.status=l.status)}_.success(`${f}用户成功`),e&&(b.value=!1)}else _.error(S.message||`${f}用户失败`)}catch(k){console.error(`${f}用户失败:`,k),_.error(`${f}用户失败`)}}).catch(()=>{})},F=l=>{E.confirm(`确定要删除用户 ${l.phone||l.id} 吗? 此操作不可恢复!`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await le(l.id);e.code===200?(_.success("删除用户成功"),w()):_.error(e.message||"删除用户失败")}catch(e){console.error("删除用户失败:",e),_.error("删除用户失败")}}).catch(()=>{})};return se(()=>{w()}),(l,e)=>{const c=d("el-input"),f=d("el-form-item"),k=d("el-option"),S=d("el-select"),g=d("el-button"),z=d("el-form"),y=d("el-table-column"),I=d("el-tag"),H=d("el-table"),O=d("el-pagination"),Q=d("el-card"),m=d("el-descriptions-item"),Y=d("el-divider"),q=d("el-empty"),G=d("el-descriptions"),J=d("el-dialog"),W=re("loading");return v(),h("div",pe,[t(Q,{shadow:"hover"},{header:n(()=>e[8]||(e[8]=[s("div",{class:"card-header"},[s("span",null,"用户管理")],-1)])),default:n(()=>[t(z,{inline:!0,model:p,class:"search-form"},{default:n(()=>[t(f,{label:"手机号"},{default:n(()=>[t(c,{modelValue:p.phone,"onUpdate:modelValue":e[0]||(e[0]=a=>p.phone=a),placeholder:"手机号",clearable:""},null,8,["modelValue"])]),_:1}),t(f,{label:"推荐人ID"},{default:n(()=>[t(c,{modelValue:p.referrerId,"onUpdate:modelValue":e[1]||(e[1]=a=>p.referrerId=a),placeholder:"推荐人ID",clearable:""},null,8,["modelValue"])]),_:1}),t(f,{label:"状态"},{default:n(()=>[t(S,{modelValue:p.status,"onUpdate:modelValue":e[2]||(e[2]=a=>p.status=a),placeholder:"全部状态",clearable:""},{default:n(()=>[t(k,{label:"正常",value:0}),t(k,{label:"禁用",value:1})]),_:1},8,["modelValue"])]),_:1}),t(f,null,{default:n(()=>[t(g,{type:"primary",onClick:x},{default:n(()=>e[9]||(e[9]=[o("搜索")])),_:1,__:[9]}),t(g,{onClick:j},{default:n(()=>e[10]||(e[10]=[o("重置")])),_:1,__:[10]})]),_:1})]),_:1},8,["model"]),ie((v(),V(H,{data:C.value,border:"",style:{width:"100%"}},{default:n(()=>[t(y,{prop:"id",label:"ID",width:"80"}),t(y,{prop:"phone",label:"手机号",width:"150"}),t(y,{prop:"invitationCode",label:"邀请码",width:"150"}),t(y,{prop:"referrerId",label:"推荐人ID",width:"150"}),t(y,{prop:"status",label:"状态",width:"100"},{default:n(a=>[t(I,{type:a.row.status===0?"success":"danger"},{default:n(()=>[o(r(a.row.status===0?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(y,{prop:"createTime",label:"注册时间",width:"180"}),t(y,{prop:"updateTime",label:"更新时间",width:"180"}),t(y,{label:"操作",width:"320"},{default:n(a=>[t(g,{type:a.row.status===0?"danger":"success",link:"",onClick:T=>$(a.row)},{default:n(()=>[o(r(a.row.status===0?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),t(g,{type:"primary",link:"",onClick:T=>R(a.row)},{default:n(()=>e[11]||(e[11]=[o(" 查看详情 ")])),_:2,__:[11]},1032,["onClick"]),t(g,{type:"danger",link:"",onClick:T=>F(a.row)},{default:n(()=>e[12]||(e[12]=[o(" 删除 ")])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[W,U.value]]),s("div",me,[t(O,{"current-page":u.pageNum,"onUpdate:currentPage":e[3]||(e[3]=a=>u.pageNum=a),"page-size":u.pageSize,"onUpdate:pageSize":e[4]||(e[4]=a=>u.pageSize=a),"page-sizes":[10,20,50,100],total:u.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:A,onCurrentChange:K},null,8,["current-page","page-size","total"])])]),_:1}),t(J,{modelValue:b.value,"onUpdate:modelValue":e[7]||(e[7]=a=>b.value=a),title:"用户详情",width:"500px"},{footer:n(()=>[s("span",ge,[t(g,{onClick:e[5]||(e[5]=a=>b.value=!1)},{default:n(()=>e[21]||(e[21]=[o("关闭")])),_:1,__:[21]}),t(g,{type:i.status===0?"danger":"success",onClick:e[6]||(e[6]=a=>$(i,!0))},{default:n(()=>[o(r(i.status===0?"禁用用户":"启用用户"),1)]),_:1},8,["type"])])]),default:n(()=>[t(G,{column:1,border:""},{default:n(()=>[t(m,{label:"ID"},{default:n(()=>[o(r(i.id),1)]),_:1}),t(m,{label:"手机号"},{default:n(()=>[o(r(i.phone),1)]),_:1}),t(m,{label:"邀请码"},{default:n(()=>[o(r(i.invitationCode),1)]),_:1}),t(m,{label:"推荐人ID"},{default:n(()=>[o(r(i.referrerId),1)]),_:1}),t(m,{label:"推荐人手机号"},{default:n(()=>[o(r(i.referrerPhone),1)]),_:1}),t(m,{label:"已推荐人数"},{default:n(()=>[o(r(i.referredCount),1)]),_:1}),t(m,{label:"状态"},{default:n(()=>[t(I,{type:i.status===0?"success":"danger"},{default:n(()=>[o(r(i.status===0?"正常":"禁用"),1)]),_:1},8,["type"])]),_:1}),t(m,{label:"注册时间"},{default:n(()=>[o(r(i.createTime),1)]),_:1}),t(m,{label:"更新时间"},{default:n(()=>[o(r(i.updateTime),1)]),_:1}),i.cardKeys&&i.cardKeys.length>0?(v(),V(m,{key:0,label:"卡密信息"},{default:n(()=>[(v(!0),h(de,null,ue(i.cardKeys,(a,T)=>(v(),h("div",{class:"card-info",key:a.id},[s("div",ce,[s("strong",null,"卡密 #"+r(T+1),1),T>0?(v(),V(Y,{key:0})):B("",!0)]),s("div",null,[e[13]||(e[13]=s("strong",null,"卡密ID:",-1)),o(" "+r(a.id),1)]),s("div",null,[e[14]||(e[14]=s("strong",null,"卡密码:",-1)),o(" "+r(a.keyCode),1)]),s("div",null,[e[15]||(e[15]=s("strong",null,"卡密类型:",-1)),o(" "+r(a.keyTypeName||L(a.keyType)),1)]),s("div",null,[e[16]||(e[16]=s("strong",null,"体验时长:",-1)),o(" "+r(a.experienceHours)+" 小时",1)]),s("div",null,[e[17]||(e[17]=s("strong",null,"激活时间:",-1)),o(" "+r(a.activateTime),1)]),s("div",null,[e[18]||(e[18]=s("strong",null,"到期时间:",-1)),o(" "+r(a.expireTime),1)]),s("div",null,[e[19]||(e[19]=s("strong",null,"状态:",-1)),t(I,{type:M(a.statusName||a.status)},{default:n(()=>[o(r(a.statusName||P(a.status)),1)]),_:2},1032,["type"])]),a.remark?(v(),h("div",fe,[e[20]||(e[20]=s("strong",null,"备注:",-1)),o(" "+r(a.remark),1)])):B("",!0)]))),128))]),_:1})):(v(),V(m,{key:1,label:"卡密信息"},{default:n(()=>[t(q,{description:"暂无卡密信息","image-size":60})]),_:1}))]),_:1})]),_:1},8,["modelValue"])])}}},ke=oe(_e,[["__scopeId","data-v-633108c5"]]);export{ke as default};

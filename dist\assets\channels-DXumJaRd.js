import{a as J,i as K,j as z,k as W}from"./points-ncRAjWQO.js";import{_ as X,b as d,O as Y,c as Z,f as g,a as l,w as r,r as i,E as u,P as ee,o as k,e as c,Q as ae,i as U,L as _,j as te,N as le}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const re={class:"channels-container"},ne={class:"page-header"},oe={class:"pagination-container"},se={class:"dialog-footer"},ie={__name:"channels",setup(ue){const T=d([]),y=d(!1),C=d(!1),b=d(1),h=d(10),N=d(0),f=d(!1),m=d(!1),x=d(null),n=d({id:"",channelName:"",channelCode:"",isFixed:0,contributeToReferrer:0,referrerRatio:0,status:1,remark:""}),$={channelName:[{required:!0,message:"请输入渠道名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],channelCode:[{required:!0,message:"请输入渠道代码",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],referrerRatio:[{required:!0,message:"请输入推荐人比例",trigger:"blur",validator:(a,e,s)=>{n.value.contributeToReferrer===1&&e==null?s(new Error("当贡献给推荐人时，必须设置推荐人比例")):s()}}],remark:[{max:200,message:"长度不能超过 200 个字符",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},v=async()=>{y.value=!0;try{const a=await J({page:b.value,limit:h.value});a.code===0||a.code===200?(T.value=Array.isArray(a.data)?a.data:[],N.value=Array.isArray(a.data)?a.data.length:0):u.error(a.message||"获取渠道列表失败")}catch(a){console.error("获取渠道列表出错:",a),u.error("获取渠道列表失败，请稍后重试")}finally{y.value=!1}},B=a=>{h.value=a,v()},E=a=>{b.value=a,v()},S=()=>{n.value={id:"",channelName:"",channelCode:"",isFixed:0,contributeToReferrer:0,referrerRatio:0,status:1,remark:""}},A=()=>{m.value=!1,S(),f.value=!0},D=a=>{m.value=!0,n.value={id:a.id,channelName:a.channelName,channelCode:a.channelCode,isFixed:a.isFixed||0,contributeToReferrer:a.contributeToReferrer||0,referrerRatio:a.referrerRatio||0,status:a.status,remark:a.remark},f.value=!0},q=a=>{le.confirm(`确定要删除渠道 "${a.channelName}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await K(a.id);e.code===0||e.code===200?(u.success("删除成功"),v()):u.error(e.message||"删除失败")}catch(e){console.error("删除渠道出错:",e),u.error("删除失败，请稍后重试")}}).catch(()=>{})},L=async a=>{const e=a.status===1?0:1,s=e===1?"启用":"禁用";try{const o=await z({id:a.id,channelName:a.channelName,channelCode:a.channelCode,contributeToReferrer:a.contributeToReferrer,referrerRatio:a.referrerRatio,status:e,remark:a.remark});o.code===0||o.code===200?(u.success(`${s}成功`),a.status=e):u.error(o.message||`${s}失败`)}catch(o){console.error(`${s}渠道出错:`,o),u.error(`${s}失败，请稍后重试`)}},j=async()=>{x.value&&await x.value.validate(async a=>{if(a){C.value=!0;try{let e;m.value?e=await z(n.value):e=await W(n.value),e.code===0||e.code===200?(u.success(m.value?"更新渠道成功":"添加渠道成功"),f.value=!1,v()):u.error(e.message||(m.value?"更新渠道失败":"添加渠道失败"))}catch(e){console.error(m.value?"更新渠道出错:":"添加渠道出错:",e),u.error(m.value?"更新渠道失败，请稍后重试":"添加渠道失败，请稍后重试")}finally{C.value=!1}}})};return Y(()=>{v()}),(a,e)=>{const s=i("el-button"),o=i("el-table-column"),R=i("el-tag"),M=i("el-table"),P=i("el-pagination"),I=i("el-card"),V=i("el-input"),p=i("el-form-item"),w=i("el-switch"),O=i("el-input-number"),Q=i("el-form"),G=i("el-dialog"),H=ee("loading");return k(),Z("div",re,[g("div",ne,[e[12]||(e[12]=g("h2",null,"积分渠道管理",-1)),l(s,{type:"primary",onClick:A},{default:r(()=>e[11]||(e[11]=[c("添加渠道")])),_:1,__:[11]})]),l(I,{class:"table-card"},{default:r(()=>[ae((k(),U(M,{data:T.value,style:{width:"100%"},border:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:r(()=>[l(o,{prop:"id",label:"ID",width:"80"}),l(o,{prop:"channelName",label:"渠道名称",width:"150"}),l(o,{prop:"channelCode",label:"渠道代码",width:"150"}),l(o,{prop:"isFixed",label:"是否固定",width:"100"},{default:r(t=>[l(R,{type:t.row.isFixed===1?"warning":"info"},{default:r(()=>[c(_(t.row.isFixed===1?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),l(o,{prop:"contributeToReferrer",label:"贡献给推荐人",width:"120"},{default:r(t=>[l(R,{type:t.row.contributeToReferrer===1?"success":"info"},{default:r(()=>[c(_(t.row.contributeToReferrer===1?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),l(o,{prop:"referrerRatio",label:"推荐人比例",width:"120"},{default:r(t=>[c(_(t.row.contributeToReferrer===1?(t.row.referrerRatio*100).toFixed(0)+"%":"-"),1)]),_:1}),l(o,{prop:"remark",label:"渠道描述"}),l(o,{prop:"status",label:"状态",width:"100"},{default:r(t=>[l(R,{type:t.row.status===1?"success":"danger"},{default:r(()=>[c(_(t.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),l(o,{prop:"createTime",label:"创建时间",width:"180"}),l(o,{label:"操作",width:"200",fixed:"right"},{default:r(t=>[l(s,{type:"primary",size:"small",onClick:F=>D(t.row)},{default:r(()=>e[13]||(e[13]=[c(" 编辑 ")])),_:2,__:[13]},1032,["onClick"]),l(s,{type:"danger",size:"small",onClick:F=>q(t.row)},{default:r(()=>e[14]||(e[14]=[c(" 删除 ")])),_:2,__:[14]},1032,["onClick"]),l(s,{type:t.row.status===1?"warning":"success",size:"small",onClick:F=>L(t.row)},{default:r(()=>[c(_(t.row.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])),[[H,y.value]]),g("div",oe,[l(P,{"current-page":b.value,"onUpdate:currentPage":e[0]||(e[0]=t=>b.value=t),"page-size":h.value,"onUpdate:pageSize":e[1]||(e[1]=t=>h.value=t),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:N.value,onSizeChange:B,onCurrentChange:E},null,8,["current-page","page-size","total"])])]),_:1}),l(G,{modelValue:f.value,"onUpdate:modelValue":e[10]||(e[10]=t=>f.value=t),title:m.value?"编辑渠道":"添加渠道",width:"500px"},{footer:r(()=>[g("span",se,[l(s,{onClick:e[9]||(e[9]=t=>f.value=!1)},{default:r(()=>e[16]||(e[16]=[c("取消")])),_:1,__:[16]}),l(s,{type:"primary",onClick:j,loading:C.value},{default:r(()=>e[17]||(e[17]=[c(" 确认 ")])),_:1,__:[17]},8,["loading"])])]),default:r(()=>[l(Q,{ref_key:"channelFormRef",ref:x,model:n.value,rules:$,"label-width":"100px"},{default:r(()=>[l(p,{label:"渠道名称",prop:"channelName"},{default:r(()=>[l(V,{modelValue:n.value.channelName,"onUpdate:modelValue":e[2]||(e[2]=t=>n.value.channelName=t),placeholder:"请输入渠道名称"},null,8,["modelValue"])]),_:1}),l(p,{label:"渠道代码",prop:"channelCode"},{default:r(()=>[l(V,{modelValue:n.value.channelCode,"onUpdate:modelValue":e[3]||(e[3]=t=>n.value.channelCode=t),placeholder:"请输入渠道代码",disabled:m.value},null,8,["modelValue","disabled"])]),_:1}),l(p,{label:"渠道描述",prop:"remark"},{default:r(()=>[l(V,{modelValue:n.value.remark,"onUpdate:modelValue":e[4]||(e[4]=t=>n.value.remark=t),type:"textarea",placeholder:"请输入渠道描述"},null,8,["modelValue"])]),_:1}),l(p,{label:"状态",prop:"status"},{default:r(()=>[l(w,{modelValue:n.value.status,"onUpdate:modelValue":e[5]||(e[5]=t=>n.value.status=t),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1}),l(p,{label:"是否固定",prop:"isFixed"},{default:r(()=>[l(w,{modelValue:n.value.isFixed,"onUpdate:modelValue":e[6]||(e[6]=t=>n.value.isFixed=t),"active-value":1,"inactive-value":0,"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1}),l(p,{label:"贡献给推荐人",prop:"contributeToReferrer"},{default:r(()=>[l(w,{modelValue:n.value.contributeToReferrer,"onUpdate:modelValue":e[7]||(e[7]=t=>n.value.contributeToReferrer=t),"active-value":1,"inactive-value":0,"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1}),n.value.contributeToReferrer===1?(k(),U(p,{key:0,label:"推荐人比例",prop:"referrerRatio"},{default:r(()=>[l(O,{modelValue:n.value.referrerRatio,"onUpdate:modelValue":e[8]||(e[8]=t=>n.value.referrerRatio=t),min:0,max:1,step:.01,precision:2,style:{width:"180px"}},null,8,["modelValue"]),e[15]||(e[15]=g("span",{class:"ratio-hint"},"（0-1之间的小数，如0.1表示10%）",-1))]),_:1,__:[15]})):te("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},pe=X(ie,[["__scopeId","data-v-54d0c2e8"]]);export{pe as default};

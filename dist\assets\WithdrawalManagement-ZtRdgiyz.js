import{g as le,a as oe,b as ne}from"./withdrawal-Ddpl8I2L.js";import{_ as se,b as k,d as D,O as re,c as B,a as t,w as a,r as i,E as y,P as de,o as f,Q as ue,f as N,R as ie,S as pe,e as o,i as b,L as d,j as v}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const me={class:"withdrawal-management"},fe={class:"pagination-container"},_e={class:"dialog-footer"},ce={class:"dialog-footer"},ge={__name:"WithdrawalManagement",setup(be){const S=k(!1),T=k(!1),w=k(!1),V=k(!1),z=k([]),$=[{value:0,label:"待审核"},{value:1,label:"已通过"},{value:2,label:"已拒绝"},{value:3,label:"已打款"}],p=D({phone:"",status:null,startTime:"",endTime:""}),u=D({pageNum:1,pageSize:10,total:0}),h=k([]),s=D({id:"",userId:"",phone:"",amount:0,alipayAccount:"",alipayName:"",status:0,createTime:"",updateTime:"",adminRemark:"",transactionId:""}),r=D({id:"",status:0,adminRemark:"",transactionId:""}),A=n=>{const e=$.find(I=>I.value===n);return e?e.label:"未知状态"},F=n=>{switch(n){case 0:return"warning";case 1:return"success";case 2:return"danger";case 3:return"primary";default:return"info"}},O=()=>{switch(r.status){case 1:return"通过提现申请";case 2:return"拒绝提现申请";case 3:return"确认已打款";default:return"审核提现申请"}},Y=n=>{n?(p.startTime=n[0],p.endTime=n[1]):(p.startTime="",p.endTime="")},W=()=>{u.pageNum=1,C()},E=()=>{p.phone="",p.status=null,p.startTime="",p.endTime="",z.value=[],W()},P=n=>{u.pageSize=n,C()},q=n=>{u.pageNum=n,C()},C=async()=>{S.value=!0;try{const n={...p,pageNum:u.pageNum,pageSize:u.pageSize},e=await le(n);e.code===200&&e.data?(h.value=e.data.list||[],u.total=e.data.total||0,u.pageNum=e.data.pageNum||1,u.pageSize=e.data.pageSize||10):(y.error(e.message||"获取提现申请列表失败"),h.value=[],u.total=0)}catch(n){console.error("获取提现申请列表失败:",n),y.error("获取提现申请列表失败"),h.value=[],u.total=0}finally{S.value=!1}},Q=async n=>{try{const e=await oe(n.id);e.code===200&&e.data?(Object.assign(s,e.data),V.value=!0):y.error(e.message||"获取提现详情失败")}catch(e){console.error("获取提现详情失败:",e),y.error("获取提现详情失败")}},x=(n,e)=>{Object.assign(s,n),r.id=n.id,r.status=e,r.adminRemark="",r.transactionId="",w.value=!0},R=n=>{V.value=!1,r.id=s.id,r.status=n,r.adminRemark="",r.transactionId="",w.value=!0},G=async()=>{if(r.status===3&&!r.transactionId){y.warning("请输入交易单号");return}T.value=!0;try{const n=await ne(r);n.code===200?(y.success("审核操作成功"),w.value=!1,C()):y.error(n.message||"审核操作失败")}catch(n){console.error("审核操作失败:",n),y.error("审核操作失败")}finally{T.value=!1}};return re(()=>{C()}),(n,e)=>{const I=i("el-input"),_=i("el-form-item"),H=i("el-option"),J=i("el-select"),K=i("el-date-picker"),m=i("el-button"),M=i("el-form"),g=i("el-table-column"),L=i("el-tag"),X=i("el-table"),Z=i("el-pagination"),ee=i("el-card"),j=i("el-dialog"),c=i("el-descriptions-item"),te=i("el-descriptions"),ae=de("loading");return f(),B("div",me,[t(ee,{shadow:"hover"},{header:a(()=>e[14]||(e[14]=[N("div",{class:"card-header"},[N("span",null,"提现申请管理")],-1)])),default:a(()=>[t(M,{inline:!0,model:p,class:"search-form"},{default:a(()=>[t(_,{label:"手机号"},{default:a(()=>[t(I,{modelValue:p.phone,"onUpdate:modelValue":e[0]||(e[0]=l=>p.phone=l),placeholder:"手机号",clearable:""},null,8,["modelValue"])]),_:1}),t(_,{label:"状态"},{default:a(()=>[t(J,{modelValue:p.status,"onUpdate:modelValue":e[1]||(e[1]=l=>p.status=l),placeholder:"全部状态",clearable:""},{default:a(()=>[(f(),B(ie,null,pe($,l=>t(H,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(_,{label:"申请时间"},{default:a(()=>[t(K,{modelValue:z.value,"onUpdate:modelValue":e[2]||(e[2]=l=>z.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:Y},null,8,["modelValue"])]),_:1}),t(_,null,{default:a(()=>[t(m,{type:"primary",onClick:W},{default:a(()=>e[15]||(e[15]=[o("搜索")])),_:1,__:[15]}),t(m,{onClick:E},{default:a(()=>e[16]||(e[16]=[o("重置")])),_:1,__:[16]})]),_:1})]),_:1},8,["model"]),ue((f(),b(X,{data:h.value,border:"",style:{width:"100%"}},{default:a(()=>[t(g,{prop:"id",label:"ID",width:"80"}),t(g,{prop:"userId",label:"用户ID",width:"100"}),t(g,{prop:"phone",label:"手机号",width:"150"}),t(g,{prop:"amount",label:"提现金额",width:"120"},{default:a(l=>[o(d(l.row.amount.toFixed(2))+" 元 ",1)]),_:1}),t(g,{prop:"alipayAccount",label:"支付宝账号",width:"180"}),t(g,{prop:"alipayName",label:"支付宝实名",width:"120"}),t(g,{prop:"status",label:"状态",width:"120"},{default:a(l=>[t(L,{type:F(l.row.status)},{default:a(()=>[o(d(A(l.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(g,{prop:"createTime",label:"申请时间",width:"180"}),t(g,{prop:"updateTime",label:"更新时间",width:"180"}),t(g,{label:"操作",fixed:"right",width:"180"},{default:a(l=>[l.row.status===0?(f(),b(m,{key:0,type:"primary",link:"",onClick:U=>x(l.row,1)},{default:a(()=>e[17]||(e[17]=[o(" 通过 ")])),_:2,__:[17]},1032,["onClick"])):v("",!0),l.row.status===0?(f(),b(m,{key:1,type:"danger",link:"",onClick:U=>x(l.row,2)},{default:a(()=>e[18]||(e[18]=[o(" 拒绝 ")])),_:2,__:[18]},1032,["onClick"])):v("",!0),l.row.status===1?(f(),b(m,{key:2,type:"success",link:"",onClick:U=>x(l.row,3)},{default:a(()=>e[19]||(e[19]=[o(" 已打款 ")])),_:2,__:[19]},1032,["onClick"])):v("",!0),t(m,{type:"info",link:"",onClick:U=>Q(l.row)},{default:a(()=>e[20]||(e[20]=[o(" 详情 ")])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ae,S.value]]),N("div",fe,[t(Z,{"current-page":u.pageNum,"onUpdate:currentPage":e[3]||(e[3]=l=>u.pageNum=l),"page-size":u.pageSize,"onUpdate:pageSize":e[4]||(e[4]=l=>u.pageSize=l),"page-sizes":[10,20,50,100],total:u.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:P,onCurrentChange:q},null,8,["current-page","page-size","total"])])]),_:1}),t(j,{modelValue:w.value,"onUpdate:modelValue":e[8]||(e[8]=l=>w.value=l),title:O(),width:"500px"},{footer:a(()=>[N("span",_e,[t(m,{onClick:e[7]||(e[7]=l=>w.value=!1)},{default:a(()=>e[21]||(e[21]=[o("取消")])),_:1,__:[21]}),t(m,{type:"primary",onClick:G,loading:T.value},{default:a(()=>e[22]||(e[22]=[o("确认")])),_:1,__:[22]},8,["loading"])])]),default:a(()=>[t(M,{model:r,"label-width":"100px"},{default:a(()=>[t(_,{label:"提现ID"},{default:a(()=>[o(d(s.id),1)]),_:1}),t(_,{label:"用户手机号"},{default:a(()=>[o(d(s.phone),1)]),_:1}),t(_,{label:"提现金额"},{default:a(()=>[o(d(s.amount?s.amount.toFixed(2):"0.00")+" 元 ",1)]),_:1}),t(_,{label:"支付宝账号"},{default:a(()=>[o(d(s.alipayAccount),1)]),_:1}),t(_,{label:"支付宝实名"},{default:a(()=>[o(d(s.alipayName),1)]),_:1}),t(_,{label:"管理员备注"},{default:a(()=>[t(I,{modelValue:r.adminRemark,"onUpdate:modelValue":e[5]||(e[5]=l=>r.adminRemark=l),type:"textarea",rows:"3",placeholder:"请输入审核备注"},null,8,["modelValue"])]),_:1}),r.status===3?(f(),b(_,{key:0,label:"交易单号"},{default:a(()=>[t(I,{modelValue:r.transactionId,"onUpdate:modelValue":e[6]||(e[6]=l=>r.transactionId=l),placeholder:"请输入交易单号"},null,8,["modelValue"])]),_:1})):v("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(j,{modelValue:V.value,"onUpdate:modelValue":e[13]||(e[13]=l=>V.value=l),title:"提现详情",width:"500px"},{footer:a(()=>[N("span",ce,[t(m,{onClick:e[9]||(e[9]=l=>V.value=!1)},{default:a(()=>e[23]||(e[23]=[o("关闭")])),_:1,__:[23]}),s.status===0?(f(),b(m,{key:0,type:"primary",onClick:e[10]||(e[10]=l=>R(1))},{default:a(()=>e[24]||(e[24]=[o(" 通过 ")])),_:1,__:[24]})):v("",!0),s.status===0?(f(),b(m,{key:1,type:"danger",onClick:e[11]||(e[11]=l=>R(2))},{default:a(()=>e[25]||(e[25]=[o(" 拒绝 ")])),_:1,__:[25]})):v("",!0),s.status===1?(f(),b(m,{key:2,type:"success",onClick:e[12]||(e[12]=l=>R(3))},{default:a(()=>e[26]||(e[26]=[o(" 已打款 ")])),_:1,__:[26]})):v("",!0)])]),default:a(()=>[t(te,{column:1,border:""},{default:a(()=>[t(c,{label:"提现ID"},{default:a(()=>[o(d(s.id),1)]),_:1}),t(c,{label:"用户ID"},{default:a(()=>[o(d(s.userId),1)]),_:1}),t(c,{label:"手机号"},{default:a(()=>[o(d(s.phone),1)]),_:1}),t(c,{label:"提现金额"},{default:a(()=>[o(d(s.amount?s.amount.toFixed(2):"0.00")+" 元",1)]),_:1}),t(c,{label:"支付宝账号"},{default:a(()=>[o(d(s.alipayAccount),1)]),_:1}),t(c,{label:"支付宝实名"},{default:a(()=>[o(d(s.alipayName),1)]),_:1}),t(c,{label:"状态"},{default:a(()=>[t(L,{type:F(s.status)},{default:a(()=>[o(d(A(s.status)),1)]),_:1},8,["type"])]),_:1}),t(c,{label:"申请时间"},{default:a(()=>[o(d(s.createTime),1)]),_:1}),t(c,{label:"更新时间"},{default:a(()=>[o(d(s.updateTime),1)]),_:1}),s.adminRemark?(f(),b(c,{key:0,label:"管理员备注"},{default:a(()=>[o(d(s.adminRemark),1)]),_:1})):v("",!0),s.transactionId?(f(),b(c,{key:1,label:"交易单号"},{default:a(()=>[o(d(s.transactionId),1)]),_:1})):v("",!0)]),_:1})]),_:1},8,["modelValue"])])}}},ke=se(ge,[["__scopeId","data-v-591f1308"]]);export{ke as default};

<template>
  <div class="withdrawal-amounts">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>用户可提现金额管理</span>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="手机号" clearable />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 可提现金额表格 -->
      <el-table
        v-loading="loading"
        :data="amountsList"
        border
        style="width: 100%"
      >
        <el-table-column prop="userId" label="用户ID" width="100" />
        <el-table-column prop="phone" label="手机号" width="150" />
        <el-table-column prop="availableAmount" label="可提现金额" width="150">
          <template #default="scope">
            {{ scope.row.availableAmount.toFixed(2) }} 元
          </template>
        </el-table-column>
        <el-table-column prop="registerTime" label="注册时间" width="180" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button 
              type="primary" 
              link 
              @click="handleViewWithdrawalHistory(scope.row)"
            >
              提现记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 提现记录对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="用户提现记录"
      width="80%"
    >
      <div v-if="currentUser.phone" class="dialog-header">
        <h3>用户: {{ currentUser.phone }} (ID: {{ currentUser.userId }})</h3>
        <p>可提现金额: {{ currentUser.availableAmount ? currentUser.availableAmount.toFixed(2) : '0.00' }} 元</p>
      </div>
      
      <el-table
        v-loading="historyLoading"
        :data="withdrawalHistory"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="记录ID" width="80" />
        <el-table-column prop="requestId" label="申请ID" width="80" />
        <el-table-column prop="amount" label="提现金额" width="120">
          <template #default="scope">
            {{ scope.row.amount.toFixed(2) }} 元
          </template>
        </el-table-column>
        <el-table-column prop="alipayAccount" label="支付宝账号" width="180" />
        <el-table-column prop="alipayName" label="支付宝实名" width="120" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="adminRemark" label="管理员备注" width="180" />
        <el-table-column prop="transactionId" label="交易单号" width="180" />
        <el-table-column prop="operationTime" label="操作时间" width="180" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
      </el-table>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getAvailableAmounts, getWithdrawalRecords } from '../../api/withdrawal'

const loading = ref(false)
const historyLoading = ref(false)
const historyDialogVisible = ref(false)

// 状态选项
const statusOptions = [
  { value: 0, label: '待审核' },
  { value: 1, label: '已通过' },
  { value: 2, label: '已拒绝' },
  { value: 3, label: '已打款' }
]

// 搜索表单
const searchForm = reactive({
  phone: ''
})

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 可提现金额列表
const amountsList = ref([])

// 当前选中的用户
const currentUser = reactive({
  userId: '',
  phone: '',
  availableAmount: 0
})

// 提现记录历史
const withdrawalHistory = ref([])

// 获取状态名称
const getStatusName = (status) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : '未知状态'
}

// 获取状态对应的标签类型
const getStatusType = (status) => {
  switch (status) {
    case 0: return 'warning'  // 待审核
    case 1: return 'success'  // 已通过
    case 2: return 'danger'   // 已拒绝
    case 3: return 'primary'  // 已打款
    default: return 'info'
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  fetchAmountsList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.phone = ''
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchAmountsList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.pageNum = val
  fetchAmountsList()
}

// 获取可提现金额列表
const fetchAmountsList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    const res = await getAvailableAmounts(params)
    if (res.code === 200 && res.data) {
      amountsList.value = res.data.list || []
      pagination.total = res.data.total || 0
      pagination.pageNum = res.data.pageNum || 1
      pagination.pageSize = res.data.pageSize || 10
    } else {
      ElMessage.error(res.message || '获取可提现金额列表失败')
      amountsList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取可提现金额列表失败:', error)
    ElMessage.error('获取可提现金额列表失败')
    amountsList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 查看用户提现记录
const handleViewWithdrawalHistory = async (user) => {
  // 保存当前用户信息
  currentUser.userId = user.userId
  currentUser.phone = user.phone
  currentUser.availableAmount = user.availableAmount
  
  historyDialogVisible.value = true
  await fetchWithdrawalHistory(user.userId)
}

// 获取用户提现记录
const fetchWithdrawalHistory = async (userId) => {
  historyLoading.value = true
  try {
    const params = {
      userId: userId
    }
    
    const res = await getWithdrawalRecords(params)
    if (res.code === 200 && res.data) {
      withdrawalHistory.value = res.data.list || []
    } else {
      ElMessage.error(res.message || '获取用户提现记录失败')
      withdrawalHistory.value = []
    }
  } catch (error) {
    console.error('获取用户提现记录失败:', error)
    ElMessage.error('获取用户提现记录失败')
    withdrawalHistory.value = []
  } finally {
    historyLoading.value = false
  }
}

onMounted(() => {
  fetchAmountsList()
})
</script>

<style scoped>
.withdrawal-amounts {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.dialog-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.dialog-header h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.dialog-header p {
  margin: 0;
  font-size: 16px;
  color: #409EFF;
  font-weight: bold;
}
</style> 
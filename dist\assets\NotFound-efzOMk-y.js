import{_ as l,c as u,f as t,a,w as d,r as p,u as _,o as i,e as m}from"./index-DKz5EXvV.js";const c={class:"not-found"},f={class:"not-found-content"},x={__name:"NotFound",setup(N){const e=_(),s=localStorage.getItem("role"),n=()=>{s==="admin"?e.push("/admin"):s==="user"?e.push("/user"):e.push("/login")};return(g,o)=>{const r=p("el-button");return i(),u("div",c,[t("div",f,[o[1]||(o[1]=t("h1",null,"404",-1)),o[2]||(o[2]=t("h2",null,"页面不存在",-1)),o[3]||(o[3]=t("p",null,"您访问的页面不存在或已被删除",-1)),a(r,{type:"primary",onClick:n},{default:d(()=>o[0]||(o[0]=[m("返回首页")])),_:1,__:[0]})])])}}},k=l(x,[["__scopeId","data-v-597067f8"]]);export{k as default};

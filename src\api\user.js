import request from '../utils/request'

// 用户注册
export function register(data) {
  return request({
    url: '/user/register',
    method: 'post',
    data
  })
}

// 用户登录
export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getUserInfo(id) {
  return request({
    url: `/user/${id}`,
    method: 'get'
  })
}

// 更新用户信息
export function updateUserInfo(data) {
  return request({
    url: '/user',
    method: 'put',
    data
  })
}

// 激活卡密（公开接口，无需Token）
// 参数格式: { keyCode: "ABCDEFGH12345678", phone: "13800138000" }
export function activateCard(data) {
  return request({
    url: '/cardkey/activate',
    method: 'post',
    data
  })
}

// 获取用户卡密状态
export function getCardStatus(userId) {
  return request({
    url: '/user/card/status',
    method: 'get',
    params: { userId }
  })
}

// 获取用户列表（管理员接口）
export function getUserList(params) {
  return request({
    url: '/user/list',
    method: 'get',
    params
  })
}

/**
 * 获取推广排名
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getPromotionRanking(params) {
  return request({
    url: '/promotion/rank',
    method: 'get',
    params
  })
}
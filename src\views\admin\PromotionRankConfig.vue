<template>
  <div class="promotion-rank-config">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>推广排名配置</span>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else>
        <div v-if="!hasConfig" class="empty-config">
          <el-empty description="暂无推广排名活动配置">
            <el-button type="primary" @click="handleCreate">创建配置</el-button>
          </el-empty>
        </div>
        
        <div v-else class="config-info">
          <div class="config-header">
            <h3>当前推广排名活动配置</h3>
            <div class="action-buttons">
              <el-button type="primary" @click="handleEdit">编辑</el-button>
              <el-button type="danger" @click="handleDelete">删除</el-button>
            </div>
          </div>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="活动ID">{{ config.id }}</el-descriptions-item>
            <el-descriptions-item label="开始时间">{{ config.startTime }}</el-descriptions-item>
            <el-descriptions-item label="结束时间">{{ config.endTime }}</el-descriptions-item>
            <el-descriptions-item label="活动说明">{{ config.description }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
    
    <!-- 编辑/创建配置对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑推广排名配置' : '创建推广排名配置'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker
            v-model="form.startTime"
            type="datetime"
            placeholder="选择开始时间"
            style="width: 100%"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            type="datetime"
            placeholder="选择结束时间"
            style="width: 100%"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="活动说明" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入活动说明"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPromotionRankConfig, savePromotionRankConfig, deletePromotionRankConfig } from '../../api/admin'

// 加载状态
const loading = ref(true)
const submitting = ref(false)

// 配置数据
const config = reactive({
  id: null,
  startTime: '',
  endTime: '',
  description: ''
})

// 是否有配置
const hasConfig = computed(() => config.id !== null)

// 表单数据
const form = reactive({
  id: null,
  startTime: '',
  endTime: '',
  description: ''
})

// 表单验证规则
const rules = {
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'blur' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && form.startTime && new Date(value) <= new Date(form.startTime)) {
          callback(new Error('结束时间必须晚于开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  description: [
    { required: true, message: '请输入活动说明', trigger: 'blur' },
    { min: 5, max: 200, message: '活动说明长度应在5到200个字符之间', trigger: 'blur' }
  ]
}

// 表单引用
const formRef = ref(null)

// 对话框显示状态
const dialogVisible = ref(false)

// 是否为编辑模式
const isEdit = ref(false)

// 获取配置
const fetchConfig = async () => {
  loading.value = true
  try {
    const res = await getPromotionRankConfig()
    if (res.data) {
      Object.assign(config, res.data)
    } else {
      // 重置配置
      config.id = null
      config.startTime = ''
      config.endTime = ''
      config.description = ''
    }
  } catch (error) {
    console.error('获取推广排名配置失败:', error)
    ElMessage.error('获取推广排名配置失败')
  } finally {
    loading.value = false
  }
}

// 创建配置
const handleCreate = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑配置
const handleEdit = () => {
  isEdit.value = true
  resetForm()
  Object.assign(form, config)
  dialogVisible.value = true
}

// 删除配置
const handleDelete = () => {
  ElMessageBox.confirm('确定要删除当前推广排名配置吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deletePromotionRankConfig(config.id)
      ElMessage.success('删除成功')
      fetchConfig()
    } catch (error) {
      console.error('删除推广排名配置失败:', error)
      ElMessage.error('删除推广排名配置失败')
    }
  }).catch(() => {})
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitting.value = true
    try {
      const data = { ...form }
      await savePromotionRankConfig(data)
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      dialogVisible.value = false
      fetchConfig()
    } catch (error) {
      console.error('保存推广排名配置失败:', error)
      ElMessage.error('保存推广排名配置失败')
    } finally {
      submitting.value = false
    }
  })
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.startTime = ''
  form.endTime = ''
  form.description = ''
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 初始化
onMounted(() => {
  fetchConfig()
})
</script>

<style scoped>
.promotion-rank-config {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container {
  padding: 20px;
}

.empty-config {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.config-info {
  padding: 20px 0;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.config-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.action-buttons {
  display: flex;
  gap: 10px;
}
</style> 
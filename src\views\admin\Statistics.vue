<template>
  <div class="statistics">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>统计分析</span>
          <div class="period-selector">
            <el-radio-group v-model="period" @change="handlePeriodChange">
              <el-radio-button label="day">日</el-radio-button>
              <el-radio-button label="week">周</el-radio-button>
              <el-radio-button label="month">月</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      
      <!-- 数据概览 -->
      <el-row :gutter="20" class="data-overview">
        <el-col :span="8">
          <el-card shadow="hover" class="data-card">
            <div class="data-title">新增用户</div>
            <div class="data-value">{{ statistics.newUsers || 0 }}</div>
            <div class="data-chart" ref="newUsersChartRef"></div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card shadow="hover" class="data-card">
            <div class="data-title">激活卡密</div>
            <div class="data-value">{{ statistics.activatedCards || 0 }}</div>
            <div class="data-chart" ref="activatedCardsChartRef"></div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card shadow="hover" class="data-card">
            <div class="data-title">生成卡密</div>
            <div class="data-value">{{ statistics.generatedCards || 0 }}</div>
            <div class="data-chart" ref="generatedCardsChartRef"></div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 图表区域 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>用户增长趋势</span>
              </div>
            </template>
            <div class="chart-container" ref="userTrendChartRef"></div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>卡密使用情况</span>
              </div>
            </template>
            <div class="chart-container" ref="cardUsageChartRef"></div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>卡密类型分布</span>
              </div>
            </template>
            <div class="chart-container" ref="cardTypeChartRef"></div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { getStatistics } from '../../api/admin'
import { CARD_TYPES } from '../../utils/constants'

const period = ref('week')
const statistics = reactive({
  newUsers: 0,
  activatedCards: 0,
  generatedCards: 0,
  userTrend: [],
  cardUsage: [],
  cardTypeDistribution: []
})

// 图表引用
const newUsersChartRef = ref(null)
const activatedCardsChartRef = ref(null)
const generatedCardsChartRef = ref(null)
const userTrendChartRef = ref(null)
const cardUsageChartRef = ref(null)
const cardTypeChartRef = ref(null)

// 图表实例
let newUsersChart = null
let activatedCardsChart = null
let generatedCardsChart = null
let userTrendChart = null
let cardUsageChart = null
let cardTypeChart = null

// 处理周期变化
const handlePeriodChange = () => {
  fetchStatistics()
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const res = await getStatistics({ period: period.value })
    Object.assign(statistics, res.data)
    
    // 更新图表
    initCharts()
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
    
    // 使用模拟数据
    statistics.newUsers = 120
    statistics.activatedCards = 85
    statistics.generatedCards = 200
    
    // 模拟用户趋势数据
    statistics.userTrend = [
      { date: '2023-06-01', count: 10 },
      { date: '2023-06-02', count: 15 },
      { date: '2023-06-03', count: 12 },
      { date: '2023-06-04', count: 18 },
      { date: '2023-06-05', count: 20 },
      { date: '2023-06-06', count: 25 },
      { date: '2023-06-07', count: 22 }
    ]
    
    // 模拟卡密使用数据
    statistics.cardUsage = [
      { date: '2023-06-01', activated: 5, generated: 20 },
      { date: '2023-06-02', activated: 8, generated: 15 },
      { date: '2023-06-03', activated: 12, generated: 30 },
      { date: '2023-06-04', activated: 10, generated: 25 },
      { date: '2023-06-05', activated: 15, generated: 40 },
      { date: '2023-06-06', activated: 20, generated: 35 },
      { date: '2023-06-07', activated: 15, generated: 35 }
    ]
    
    // 模拟卡密类型分布
    statistics.cardTypeDistribution = [
      { type: 1, count: 20 },
      { type: 2, count: 30 },
      { type: 3, count: 50 },
      { type: 4, count: 80 },
      { type: 5, count: 40 },
      { type: 6, count: 25 },
      { type: 7, count: 15 }
    ]
    
    // 更新图表
    initCharts()
  }
}

// 初始化图表
const initCharts = () => {
  initMiniCharts()
  initUserTrendChart()
  initCardUsageChart()
  initCardTypeChart()
}

// 初始化小图表
const initMiniCharts = () => {
  // 新增用户小图表
  if (newUsersChartRef.value) {
    newUsersChart = echarts.init(newUsersChartRef.value)
    newUsersChart.setOption({
      xAxis: { show: false },
      yAxis: { show: false },
      grid: { left: 0, right: 0, top: 0, bottom: 0 },
      series: [{
        type: 'line',
        data: statistics.userTrend.map(item => item.count),
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#409EFF' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.7)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }]
    })
  }
  
  // 激活卡密小图表
  if (activatedCardsChartRef.value) {
    activatedCardsChart = echarts.init(activatedCardsChartRef.value)
    activatedCardsChart.setOption({
      xAxis: { show: false },
      yAxis: { show: false },
      grid: { left: 0, right: 0, top: 0, bottom: 0 },
      series: [{
        type: 'line',
        data: statistics.cardUsage.map(item => item.activated),
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#67C23A' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(103, 194, 58, 0.7)' },
              { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
            ]
          }
        }
      }]
    })
  }
  
  // 生成卡密小图表
  if (generatedCardsChartRef.value) {
    generatedCardsChart = echarts.init(generatedCardsChartRef.value)
    generatedCardsChart.setOption({
      xAxis: { show: false },
      yAxis: { show: false },
      grid: { left: 0, right: 0, top: 0, bottom: 0 },
      series: [{
        type: 'line',
        data: statistics.cardUsage.map(item => item.generated),
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#E6A23C' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(230, 162, 60, 0.7)' },
              { offset: 1, color: 'rgba(230, 162, 60, 0.1)' }
            ]
          }
        }
      }]
    })
  }
}

// 初始化用户趋势图表
const initUserTrendChart = () => {
  if (userTrendChartRef.value) {
    userTrendChart = echarts.init(userTrendChartRef.value)
    userTrendChart.setOption({
      title: {
        text: '用户增长趋势'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: statistics.userTrend.map(item => item.date)
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        name: '新增用户',
        type: 'line',
        data: statistics.userTrend.map(item => item.count),
        smooth: true,
        lineStyle: { color: '#409EFF' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.5)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }]
    })
  }
}

// 初始化卡密使用图表
const initCardUsageChart = () => {
  if (cardUsageChartRef.value) {
    cardUsageChart = echarts.init(cardUsageChartRef.value)
    cardUsageChart.setOption({
      title: {
        text: '卡密使用情况'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['激活卡密', '生成卡密']
      },
      xAxis: {
        type: 'category',
        data: statistics.cardUsage.map(item => item.date)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '激活卡密',
          type: 'bar',
          data: statistics.cardUsage.map(item => item.activated),
          itemStyle: { color: '#67C23A' }
        },
        {
          name: '生成卡密',
          type: 'bar',
          data: statistics.cardUsage.map(item => item.generated),
          itemStyle: { color: '#E6A23C' }
        }
      ]
    })
  }
}

// 初始化卡密类型分布图表
const initCardTypeChart = () => {
  if (cardTypeChartRef.value) {
    cardTypeChart = echarts.init(cardTypeChartRef.value)
    
    const cardTypeData = statistics.cardTypeDistribution.map(item => {
      const cardType = CARD_TYPES.find(type => type.value === item.type)
      return {
        name: cardType ? cardType.label : `类型${item.type}`,
        value: item.count,
        itemStyle: {
          color: cardType ? cardType.color : '#999'
        }
      }
    })
    
    cardTypeChart.setOption({
      title: {
        text: '卡密类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: cardTypeData.map(item => item.name)
      },
      series: [
        {
          name: '卡密类型',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: cardTypeData
        }
      ]
    })
  }
}

// 窗口大小改变时重绘图表
const resizeCharts = () => {
  const charts = [
    newUsersChart, 
    activatedCardsChart, 
    generatedCardsChart, 
    userTrendChart, 
    cardUsageChart, 
    cardTypeChart
  ]
  
  charts.forEach(chart => {
    if (chart) {
      chart.resize()
    }
  })
}

// 添加窗口大小改变事件监听
window.addEventListener('resize', resizeCharts)

onMounted(() => {
  fetchStatistics()
  
  // 设置一个延时，确保DOM已经渲染
  setTimeout(() => {
    initCharts()
  }, 100)
})

onBeforeUnmount(() => {
  // 移除窗口大小改变事件监听
  window.removeEventListener('resize', resizeCharts)
  
  // 销毁图表实例
  const charts = [
    newUsersChart, 
    activatedCardsChart, 
    generatedCardsChart, 
    userTrendChart, 
    cardUsageChart, 
    cardTypeChart
  ]
  
  charts.forEach(chart => {
    if (chart) {
      chart.dispose()
    }
  })
})
</script>

<style scoped>
.statistics {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-overview {
  margin-bottom: 20px;
}

.data-card {
  height: 160px;
  padding: 15px;
}

.data-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.data-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
}

.data-chart {
  height: 60px;
  width: 100%;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  width: 100%;
}
</style> 
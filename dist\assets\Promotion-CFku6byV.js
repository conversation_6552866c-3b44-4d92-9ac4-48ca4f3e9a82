import{y as W,z as X,A as Y,B as Z,C as ee,D as te}from"./admin-CZaj3CWx.js";import{_ as oe,b as w,d as O,O as le,c as E,a as t,w as o,r as i,E as d,P as re,o as b,Q as ne,i as T,f as n,e as s,L as _,j as B,N as ae}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const ie={class:"promotion-management"},se={class:"card-header"},de={class:"image-error"},ue={class:"promotion-detail"},me={class:"promotion-image"},ce={class:"image-error"},pe={class:"promotion-content"},_e={class:"promotion-info"},fe={key:0,class:"image-preview"},ge={class:"image-error"},ve={class:"dialog-footer"},we={__name:"Promotion",setup(be){const D=w([]),x=w(!1),U=w(!1),u=O({view:!1,edit:!1}),m=O({id:null,title:"",content:"",imageUrl:"",sortOrder:1,status:1,createTime:"",updateTime:""}),r=O({id:null,title:"",content:"",imageUrl:"",sortOrder:1,status:1}),f=w(null),y=w(!0),F={title:[{required:!0,message:"请输入推广标题",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],content:[{required:!0,message:"请输入推广内容",trigger:"blur"}],imageUrl:[{required:!0,message:"请输入图片URL",trigger:"blur"}],sortOrder:[{required:!0,message:"请输入排序值",trigger:"blur"}]},g=async()=>{x.value=!0;try{const a=await W();D.value=a.data||[]}catch(a){console.error("获取推广内容列表失败:",a),d.error("获取推广内容列表失败")}finally{x.value=!1}},R=a=>{Object.assign(m,a),u.view=!0},$=()=>{y.value=!0,L(),u.edit=!0},N=a=>{y.value=!1,L(),Object.assign(r,a),u.edit=!0},q=async a=>{try{await X(a.id),d.success("启用推广内容成功"),g()}catch(e){console.error("启用推广内容失败:",e),d.error("启用推广内容失败")}},j=async a=>{try{await Y(a.id),d.success("禁用推广内容成功"),g()}catch(e){console.error("禁用推广内容失败:",e),d.error("禁用推广内容失败")}},A=a=>{ae.confirm("确定要删除该推广内容吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await Z(a.id),d.success("删除推广内容成功"),g()}catch(e){console.error("删除推广内容失败:",e),d.error("删除推广内容失败")}}).catch(()=>{})},L=()=>{f.value&&f.value.resetFields(),r.id=null,r.title="",r.content="",r.imageUrl="",r.sortOrder=1,r.status=1},M=async()=>{f.value&&await f.value.validate(async a=>{if(a){U.value=!0;try{y.value?(await ee(r),d.success("创建推广内容成功")):(await te(r),d.success("更新推广内容成功")),u.edit=!1,g()}catch(e){console.error("提交推广内容失败:",e),d.error("提交推广内容失败")}finally{U.value=!1}}})};return le(()=>{g()}),(a,e)=>{const c=i("el-button"),p=i("el-table-column"),k=i("el-icon-picture"),P=i("el-icon"),h=i("el-image"),I=i("el-tag"),Q=i("el-table"),S=i("el-card"),z=i("el-dialog"),C=i("el-input"),v=i("el-form-item"),G=i("el-input-number"),H=i("el-switch"),J=i("el-form"),K=re("loading");return b(),E("div",ie,[t(S,{shadow:"hover"},{header:o(()=>[n("div",se,[e[9]||(e[9]=n("span",null,"推广内容管理",-1)),t(c,{type:"primary",onClick:$},{default:o(()=>e[8]||(e[8]=[s("添加推广内容")])),_:1,__:[8]})])]),default:o(()=>[ne((b(),T(Q,{data:D.value,style:{width:"100%"}},{default:o(()=>[t(p,{prop:"id",label:"ID",width:"80"}),t(p,{prop:"title",label:"标题","min-width":"150"}),t(p,{label:"图片",width:"120"},{default:o(l=>[t(h,{style:{width:"80px",height:"45px"},src:l.row.imageUrl,fit:"cover","preview-src-list":[l.row.imageUrl]},{error:o(()=>[n("div",de,[t(P,null,{default:o(()=>[t(k)]),_:1})])]),_:2},1032,["src","preview-src-list"])]),_:1}),t(p,{prop:"sortOrder",label:"排序",width:"80"}),t(p,{label:"状态",width:"100"},{default:o(l=>[t(I,{type:l.row.status===1?"success":"info"},{default:o(()=>[s(_(l.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(p,{prop:"createTime",label:"创建时间",width:"180"}),t(p,{prop:"updateTime",label:"更新时间",width:"180"}),t(p,{label:"操作",width:"250",fixed:"right"},{default:o(l=>[t(c,{size:"small",onClick:V=>R(l.row)},{default:o(()=>e[10]||(e[10]=[s("查看")])),_:2,__:[10]},1032,["onClick"]),t(c,{size:"small",type:"primary",onClick:V=>N(l.row)},{default:o(()=>e[11]||(e[11]=[s("编辑")])),_:2,__:[11]},1032,["onClick"]),l.row.status===0?(b(),T(c,{key:0,size:"small",type:"success",onClick:V=>q(l.row)},{default:o(()=>e[12]||(e[12]=[s("启用")])),_:2,__:[12]},1032,["onClick"])):B("",!0),l.row.status===1?(b(),T(c,{key:1,size:"small",type:"warning",onClick:V=>j(l.row)},{default:o(()=>e[13]||(e[13]=[s("禁用")])),_:2,__:[13]},1032,["onClick"])):B("",!0),t(c,{size:"small",type:"danger",onClick:V=>A(l.row)},{default:o(()=>e[14]||(e[14]=[s("删除")])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[K,x.value]])]),_:1}),t(z,{modelValue:u.view,"onUpdate:modelValue":e[0]||(e[0]=l=>u.view=l),title:"推广内容详情",width:"600px"},{default:o(()=>[n("div",ue,[n("h2",null,_(m.title),1),n("div",me,[t(h,{style:{"max-width":"100%","max-height":"300px"},src:m.imageUrl,fit:"contain"},{error:o(()=>[n("div",ce,[t(P,null,{default:o(()=>[t(k)]),_:1})])]),_:1},8,["src"])]),n("div",pe,_(m.content),1),n("div",_e,[n("p",null,[e[15]||(e[15]=n("strong",null,"排序:",-1)),s(" "+_(m.sortOrder),1)]),n("p",null,[e[16]||(e[16]=n("strong",null,"状态:",-1)),s(" "+_(m.status===1?"启用":"禁用"),1)]),n("p",null,[e[17]||(e[17]=n("strong",null,"创建时间:",-1)),s(" "+_(m.createTime),1)]),n("p",null,[e[18]||(e[18]=n("strong",null,"更新时间:",-1)),s(" "+_(m.updateTime),1)])])])]),_:1},8,["modelValue"]),t(z,{modelValue:u.edit,"onUpdate:modelValue":e[7]||(e[7]=l=>u.edit=l),title:y.value?"添加推广内容":"编辑推广内容",width:"600px"},{footer:o(()=>[n("span",ve,[t(c,{onClick:e[6]||(e[6]=l=>u.edit=!1)},{default:o(()=>e[19]||(e[19]=[s("取消")])),_:1,__:[19]}),t(c,{type:"primary",onClick:M,loading:U.value},{default:o(()=>e[20]||(e[20]=[s(" 确认 ")])),_:1,__:[20]},8,["loading"])])]),default:o(()=>[t(J,{model:r,rules:F,ref_key:"promotionFormRef",ref:f,"label-width":"100px"},{default:o(()=>[t(v,{label:"标题",prop:"title"},{default:o(()=>[t(C,{modelValue:r.title,"onUpdate:modelValue":e[1]||(e[1]=l=>r.title=l),placeholder:"请输入推广标题"},null,8,["modelValue"])]),_:1}),t(v,{label:"内容",prop:"content"},{default:o(()=>[t(C,{modelValue:r.content,"onUpdate:modelValue":e[2]||(e[2]=l=>r.content=l),type:"textarea",rows:4,placeholder:"请输入推广内容"},null,8,["modelValue"])]),_:1}),t(v,{label:"图片URL",prop:"imageUrl"},{default:o(()=>[t(C,{modelValue:r.imageUrl,"onUpdate:modelValue":e[3]||(e[3]=l=>r.imageUrl=l),placeholder:"请输入图片URL"},null,8,["modelValue"]),r.imageUrl?(b(),E("div",fe,[t(h,{style:{width:"100px",height:"60px","margin-top":"10px"},src:r.imageUrl,fit:"cover"},{error:o(()=>[n("div",ge,[t(P,null,{default:o(()=>[t(k)]),_:1})])]),_:1},8,["src"])])):B("",!0)]),_:1}),t(v,{label:"排序",prop:"sortOrder"},{default:o(()=>[t(G,{modelValue:r.sortOrder,"onUpdate:modelValue":e[4]||(e[4]=l=>r.sortOrder=l),min:1,max:999},null,8,["modelValue"])]),_:1}),t(v,{label:"状态",prop:"status"},{default:o(()=>[t(H,{modelValue:r.status,"onUpdate:modelValue":e[5]||(e[5]=l=>r.status=l),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Ue=oe(we,[["__scopeId","data-v-5083a36f"]]);export{Ue as default};

<template>
  <div class="withdrawal-management">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>提现申请管理</span>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="手机号" clearable />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="申请时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 提现申请表格 -->
      <el-table
        v-loading="loading"
        :data="withdrawalList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="userId" label="用户ID" width="100" />
        <el-table-column prop="phone" label="手机号" width="150" />
        <el-table-column prop="amount" label="提现金额" width="120">
          <template #default="scope">
            {{ scope.row.amount.toFixed(2) }} 元
          </template>
        </el-table-column>
        <el-table-column prop="alipayAccount" label="支付宝账号" width="180" />
        <el-table-column prop="alipayName" label="支付宝实名" width="120" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" width="180" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button 
              v-if="scope.row.status === 0"
              type="primary" 
              link 
              @click="handleAudit(scope.row, 1)"
            >
              通过
            </el-button>
            <el-button 
              v-if="scope.row.status === 0"
              type="danger" 
              link 
              @click="handleAudit(scope.row, 2)"
            >
              拒绝
            </el-button>
            <el-button 
              v-if="scope.row.status === 1"
              type="success" 
              link 
              @click="handleAudit(scope.row, 3)"
            >
              已打款
            </el-button>
            <el-button 
              type="info" 
              link 
              @click="handleViewDetails(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialogVisible"
      :title="getAuditDialogTitle()"
      width="500px"
    >
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="提现ID">
          {{ currentWithdrawal.id }}
        </el-form-item>
        <el-form-item label="用户手机号">
          {{ currentWithdrawal.phone }}
        </el-form-item>
        <el-form-item label="提现金额">
          {{ currentWithdrawal.amount ? currentWithdrawal.amount.toFixed(2) : '0.00' }} 元
        </el-form-item>
        <el-form-item label="支付宝账号">
          {{ currentWithdrawal.alipayAccount }}
        </el-form-item>
        <el-form-item label="支付宝实名">
          {{ currentWithdrawal.alipayName }}
        </el-form-item>
        
        <el-form-item label="管理员备注">
          <el-input v-model="auditForm.adminRemark" type="textarea" rows="3" placeholder="请输入审核备注" />
        </el-form-item>
        
        <el-form-item v-if="auditForm.status === 3" label="交易单号">
          <el-input v-model="auditForm.transactionId" placeholder="请输入交易单号" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAudit" :loading="auditLoading">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="提现详情"
      width="500px"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="提现ID">{{ currentWithdrawal.id }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ currentWithdrawal.userId }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ currentWithdrawal.phone }}</el-descriptions-item>
        <el-descriptions-item label="提现金额">{{ currentWithdrawal.amount ? currentWithdrawal.amount.toFixed(2) : '0.00' }} 元</el-descriptions-item>
        <el-descriptions-item label="支付宝账号">{{ currentWithdrawal.alipayAccount }}</el-descriptions-item>
        <el-descriptions-item label="支付宝实名">{{ currentWithdrawal.alipayName }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentWithdrawal.status)">
            {{ getStatusName(currentWithdrawal.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ currentWithdrawal.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ currentWithdrawal.updateTime }}</el-descriptions-item>
        <el-descriptions-item v-if="currentWithdrawal.adminRemark" label="管理员备注">{{ currentWithdrawal.adminRemark }}</el-descriptions-item>
        <el-descriptions-item v-if="currentWithdrawal.transactionId" label="交易单号">{{ currentWithdrawal.transactionId }}</el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button 
            v-if="currentWithdrawal.status === 0"
            type="primary" 
            @click="handleAuditFromDetail(1)"
          >
            通过
          </el-button>
          <el-button 
            v-if="currentWithdrawal.status === 0"
            type="danger" 
            @click="handleAuditFromDetail(2)"
          >
            拒绝
          </el-button>
          <el-button 
            v-if="currentWithdrawal.status === 1"
            type="success" 
            @click="handleAuditFromDetail(3)"
          >
            已打款
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getWithdrawalRequests, auditWithdrawal, getWithdrawalDetail } from '../../api/withdrawal'

const loading = ref(false)
const auditLoading = ref(false)
const auditDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const dateRange = ref([])

// 状态选项
const statusOptions = [
  { value: 0, label: '待审核' },
  { value: 1, label: '已通过' },
  { value: 2, label: '已拒绝' },
  { value: 3, label: '已打款' }
]

// 搜索表单
const searchForm = reactive({
  phone: '',
  status: null,
  startTime: '',
  endTime: ''
})

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 提现列表
const withdrawalList = ref([])

// 当前选中的提现申请
const currentWithdrawal = reactive({
  id: '',
  userId: '',
  phone: '',
  amount: 0,
  alipayAccount: '',
  alipayName: '',
  status: 0,
  createTime: '',
  updateTime: '',
  adminRemark: '',
  transactionId: ''
})

// 审核表单
const auditForm = reactive({
  id: '',
  status: 0,
  adminRemark: '',
  transactionId: ''
})

// 获取状态名称
const getStatusName = (status) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : '未知状态'
}

// 获取状态对应的标签类型
const getStatusType = (status) => {
  switch (status) {
    case 0: return 'warning'  // 待审核
    case 1: return 'success'  // 已通过
    case 2: return 'danger'   // 已拒绝
    case 3: return 'primary'  // 已打款
    default: return 'info'
  }
}

// 获取审核对话框标题
const getAuditDialogTitle = () => {
  switch (auditForm.status) {
    case 1: return '通过提现申请'
    case 2: return '拒绝提现申请'
    case 3: return '确认已打款'
    default: return '审核提现申请'
  }
}

// 日期范围变化
const handleDateChange = (val) => {
  if (val) {
    searchForm.startTime = val[0]
    searchForm.endTime = val[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  fetchWithdrawalList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.phone = ''
  searchForm.status = null
  searchForm.startTime = ''
  searchForm.endTime = ''
  dateRange.value = []
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchWithdrawalList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.pageNum = val
  fetchWithdrawalList()
}

// 获取提现申请列表
const fetchWithdrawalList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    const res = await getWithdrawalRequests(params)
    if (res.code === 200 && res.data) {
      withdrawalList.value = res.data.list || []
      pagination.total = res.data.total || 0
      pagination.pageNum = res.data.pageNum || 1
      pagination.pageSize = res.data.pageSize || 10
    } else {
      ElMessage.error(res.message || '获取提现申请列表失败')
      withdrawalList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取提现申请列表失败:', error)
    ElMessage.error('获取提现申请列表失败')
    withdrawalList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 查看提现详情
const handleViewDetails = async (withdrawal) => {
  try {
    const res = await getWithdrawalDetail(withdrawal.id)
    if (res.code === 200 && res.data) {
      Object.assign(currentWithdrawal, res.data)
      detailDialogVisible.value = true
    } else {
      ElMessage.error(res.message || '获取提现详情失败')
    }
  } catch (error) {
    console.error('获取提现详情失败:', error)
    ElMessage.error('获取提现详情失败')
  }
}

// 审核提现申请
const handleAudit = (withdrawal, status) => {
  Object.assign(currentWithdrawal, withdrawal)
  
  // 重置审核表单
  auditForm.id = withdrawal.id
  auditForm.status = status
  auditForm.adminRemark = ''
  auditForm.transactionId = ''
  
  auditDialogVisible.value = true
}

// 从详情页面审核
const handleAuditFromDetail = (status) => {
  detailDialogVisible.value = false
  
  // 重置审核表单
  auditForm.id = currentWithdrawal.id
  auditForm.status = status
  auditForm.adminRemark = ''
  auditForm.transactionId = ''
  
  auditDialogVisible.value = true
}

// 提交审核
const submitAudit = async () => {
  // 验证表单
  if (auditForm.status === 3 && !auditForm.transactionId) {
    ElMessage.warning('请输入交易单号')
    return
  }
  
  auditLoading.value = true
  try {
    const res = await auditWithdrawal(auditForm)
    
    if (res.code === 200) {
      ElMessage.success('审核操作成功')
      auditDialogVisible.value = false
      fetchWithdrawalList() // 重新加载列表
    } else {
      ElMessage.error(res.message || '审核操作失败')
    }
  } catch (error) {
    console.error('审核操作失败:', error)
    ElMessage.error('审核操作失败')
  } finally {
    auditLoading.value = false
  }
}

onMounted(() => {
  fetchWithdrawalList()
})
</script>

<style scoped>
.withdrawal-management {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 
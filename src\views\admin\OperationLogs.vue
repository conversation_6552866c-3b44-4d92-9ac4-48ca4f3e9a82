<template>
  <div class="operation-logs-container">
    <div class="page-header">
      <h2>操作日志</h2>
      <p>查看系统所有操作记录</p>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-form-item label="操作类型">
          <el-select v-model="searchForm.operationType" placeholder="全部" clearable>
            <el-option label="登录" value="登录" />
            <el-option label="修改密码" value="修改密码" />
            <el-option label="生成卡密" value="生成卡密" />
            <el-option label="激活卡密" value="激活卡密" />
            <el-option label="查询" value="查询" />
            <el-option label="删除" value="删除" />
            <el-option label="禁用" value="禁用" />
            <el-option label="启用" value="启用" />
            <el-option label="清理" value="清理" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作人">
          <el-input v-model="searchForm.username" placeholder="请输入操作人名称" clearable />
        </el-form-item>
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            :shortcuts="dateShortcuts"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="danger" @click="showCleanLogsDialog">清理日志</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table 
        v-loading="loading" 
        :data="logList" 
        border 
        stripe 
        style="width: 100%; margin-top: 10px"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="operationType" label="操作类型" width="120" />
        <el-table-column prop="description" label="操作描述" show-overflow-tooltip />
        <el-table-column prop="username" label="操作人" width="120" />
        <el-table-column prop="ip" label="IP地址" width="140" />
        <el-table-column prop="createTime" label="操作时间" width="180" />
        <el-table-column prop="result" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getOperationResultStatus(scope.row) ? 'success' : 'danger'">
              {{ getOperationResultStatus(scope.row) ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              link
              @click="viewLogDetail(scope.row.id)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 清理日志对话框 -->
    <el-dialog v-model="cleanLogsDialogVisible" title="清理日志" width="400px">
      <el-form :model="cleanLogsForm" label-width="120px">
        <el-form-item label="清理日期范围">
          <el-input-number v-model="cleanLogsForm.days" :min="1" :max="365" placeholder="输入天数"></el-input-number>
          <span class="days-hint">天前的日志</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cleanLogsDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="handleCleanLogs" :loading="cleaningLogs">确认清理</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 日志详情对话框 -->
    <el-dialog v-model="logDetailDialogVisible" title="日志详情" width="600px">
      <div v-if="logDetail" class="log-detail">
        <div class="detail-item">
          <span class="label">ID:</span>
          <span>{{ logDetail.id }}</span>
        </div>
        <div class="detail-item">
          <span class="label">操作类型:</span>
          <span>{{ logDetail.operationType }}</span>
        </div>
        <div class="detail-item">
          <span class="label">操作描述:</span>
          <span>{{ logDetail.description }}</span>
        </div>
        <div class="detail-item">
          <span class="label">操作人:</span>
          <span>{{ logDetail.username }}</span>
        </div>
        <div class="detail-item">
          <span class="label">操作方法:</span>
          <span>{{ logDetail.method }}</span>
        </div>
        <div class="detail-item">
          <span class="label">参数:</span>
          <pre>{{ logDetail.params }}</pre>
        </div>
        <div class="detail-item">
          <span class="label">结果:</span>
          <span>{{ logDetail.result }}</span>
        </div>
        <div class="detail-item">
          <span class="label">IP地址:</span>
          <span>{{ logDetail.ip }}</span>
        </div>
        <div class="detail-item">
          <span class="label">操作时间:</span>
          <span>{{ logDetail.createTime }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOperationLogs, getOperationLogDetail, cleanLogs } from '@/api/admin'

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

// 搜索表单
const searchForm = reactive({
  operationType: '',
  username: '',
  startTime: '',
  endTime: '',
  pageNum: 1,
  pageSize: 10
})

// 日期范围
const dateRange = ref([])

// 监听日期范围变化
const setDateRange = computed(() => {
  if (dateRange.value && dateRange.value.length === 2) {
    searchForm.startTime = dateRange.value[0]
    searchForm.endTime = dateRange.value[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
  return dateRange.value
})

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const logList = ref([])
const loading = ref(false)

// 清理日志对话框
const cleanLogsDialogVisible = ref(false)
const cleanLogsForm = reactive({
  days: 30
})
const cleaningLogs = ref(false)

// 日志详情对话框
const logDetailDialogVisible = ref(false)
const logDetail = ref(null)

// 获取操作结果状态
const getOperationResultStatus = (row) => {
  if (!row.result) return false
  
  try {
    // 尝试解析JSON结果
    const resultObj = typeof row.result === 'string' ? JSON.parse(row.result) : row.result
    // 检查返回的code是否为200
    return resultObj && resultObj.code === 200
  } catch (e) {
    // 如果解析失败，返回false
    return false
  }
}

// 获取日志列表
const fetchLogs = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    const res = await getOperationLogs(params)
    if (res.code === 200) {
      logList.value = res.data.list || []
      pagination.total = res.data.total || 0
      pagination.pageNum = res.data.pageNum || 1
      pagination.pageSize = res.data.pageSize || 10
    } else {
      ElMessage.error(res.message || '获取日志列表失败')
    }
  } catch (error) {
    console.error('获取日志列表出错', error)
    ElMessage.error('获取日志列表出错')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  fetchLogs()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  dateRange.value = []
  pagination.pageNum = 1
  pagination.pageSize = 10
  fetchLogs()
}

// 显示清理日志对话框
const showCleanLogsDialog = () => {
  cleanLogsDialogVisible.value = true
}

// 清理日志
const handleCleanLogs = async () => {
  if (!cleanLogsForm.days || cleanLogsForm.days <= 0) {
    ElMessage.warning('请输入有效的天数')
    return
  }

  try {
    cleaningLogs.value = true
    const res = await cleanLogs(cleanLogsForm.days)
    if (res.code === 200) {
      ElMessage.success(`成功清理了${res.data}条日志记录`)
      cleanLogsDialogVisible.value = false
      fetchLogs()
    } else {
      ElMessage.error(res.message || '清理日志失败')
    }
  } catch (error) {
    console.error('清理日志出错', error)
    ElMessage.error('清理日志出错')
  } finally {
    cleaningLogs.value = false
  }
}

// 查看日志详情
const viewLogDetail = async (id) => {
  try {
    const res = await getOperationLogDetail(id)
    if (res.code === 200) {
      logDetail.value = res.data
      logDetailDialogVisible.value = true
    } else {
      ElMessage.error(res.message || '获取日志详情失败')
    }
  } catch (error) {
    console.error('获取日志详情出错', error)
    ElMessage.error('获取日志详情出错')
  }
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchLogs()
}

// 页码变化
const handleCurrentChange = (page) => {
  pagination.pageNum = page
  fetchLogs()
}

// 组件挂载后获取数据
onMounted(() => {
  fetchLogs()
})
</script>

<style scoped>
.operation-logs-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  font-size: 24px;
  margin-bottom: 5px;
}

.page-header p {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-top: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.days-hint {
  margin-left: 10px;
}

.log-detail {
  padding: 10px;
}

.detail-item {
  margin-bottom: 10px;
  line-height: 1.5;
}

.detail-item .label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 80px;
  display: inline-block;
}

.detail-item pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 5px 0;
}
</style> 
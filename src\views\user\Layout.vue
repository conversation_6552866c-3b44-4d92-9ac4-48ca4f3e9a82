<template>
  <div class="user-layout">
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside width="220px" class="aside">
        <div class="logo">
          <h2>用户中心</h2>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          :collapse="isCollapse"
          router
        >
          <el-menu-item index="/user">
            <el-icon><el-icon-house /></el-icon>
            <span>用户中心</span>
          </el-menu-item>
          
          <el-menu-item index="/user/activate">
            <el-icon><el-icon-check /></el-icon>
            <span>激活卡密</span>
          </el-menu-item>
          
          <el-menu-item index="/user/profile">
            <el-icon><el-icon-user /></el-icon>
            <span>个人资料</span>
          </el-menu-item>
          
          <el-menu-item index="/user/promotion/rank">
            <el-icon><el-icon-data-analysis /></el-icon>
            <span>推广排名</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主要内容区域 -->
      <el-container>
        <!-- 头部 -->
        <el-header class="header">
          <div class="header-left">
            <el-icon class="collapse-btn" @click="toggleCollapse">
              <el-icon-fold v-if="!isCollapse" />
              <el-icon-expand v-else />
            </el-icon>
            <Breadcrumb />
          </div>
          
          <div class="header-right">
            <el-dropdown trigger="click">
              <div class="avatar-container">
                <el-avatar :size="30" icon="el-icon-user" />
                <span class="username">{{ username }}</span>
                <el-icon><el-icon-arrow-down /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区域 -->
        <el-main class="main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

// 面包屑数据
const route = useRoute()
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => item.meta.title)
})

// 面包屑组件
const Breadcrumb = {
  setup() {
    return { breadcrumbs }
  },
  template: `
    <el-breadcrumb separator="/">
      <el-breadcrumb-item v-for="(title, index) in breadcrumbs" :key="index">
        {{ title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  `
}

const router = useRouter()
const isCollapse = ref(false)
const username = ref(localStorage.getItem('username') || '用户')

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 清除本地存储的登录信息
    localStorage.removeItem('token')
    localStorage.removeItem('role')
    localStorage.removeItem('userId')
    localStorage.removeItem('username')
    
    // 跳转到登录页
    router.push('/login')
    
    ElMessage({
      type: 'success',
      message: '已退出登录'
    })
  }).catch(() => {})
}
</script>

<style scoped>
.user-layout {
  height: 100vh;
}

.layout-container {
  height: 100%;
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  overflow-x: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #263445;
}

.logo h2 {
  color: #fff;
  margin: 0;
  font-size: 18px;
}

.el-menu-vertical {
  border-right: none;
}

.header {
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  font-size: 20px;
  cursor: pointer;
  margin-right: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.avatar-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 10px;
  color: #606266;
}

.main {
  padding: 20px;
  background-color: #f0f2f5;
}
</style> 
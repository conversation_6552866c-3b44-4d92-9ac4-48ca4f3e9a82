import{e as Q,u as G,f as H,h as J}from"./points-ncRAjWQO.js";import{_ as W,b as p,d as h,O as X,c as Y,f as V,a as l,w as n,r,E as g,P as Z,o as b,e as f,Q as ee,i as z,L as S,j as K}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const ae={class:"points-configs-container"},te={class:"page-header"},le={class:"config-value"},oe={class:"pagination-container"},ne={__name:"configs",setup(se){const _=p([]),C=p(!1),c=p(!1),N=p(null),u=p(!1),k=p(!1),w=p(null),i=h({pageNum:1,pageSize:10,total:0}),t=h({configName:"",configKey:"",configValue:"",status:1,remark:""}),U={configName:[{required:!0,message:"请输入配置名称",trigger:"blur"}],configKey:[{required:!0,message:"请输入配置键",trigger:"blur"}],configValue:[{required:!0,message:"请输入配置值",trigger:"blur"}]},v=async()=>{C.value=!0;try{const a=await Q({pageNum:i.pageNum,pageSize:i.pageSize});a.code===0||a.code===200?Array.isArray(a.data)?(_.value=a.data,i.total=a.data.length):a.data&&a.data.list?(_.value=a.data.list,i.total=a.data.total||0,i.pageNum=a.data.pageNum||1,i.pageSize=a.data.pageSize||10):(_.value=[],i.total=0):g.error(a.message||"获取积分配置列表失败")}catch(a){console.error("获取积分配置列表出错:",a),g.error("获取积分配置列表失败")}finally{C.value=!1}},D=a=>{i.pageSize=a,v()},$=a=>{i.pageNum=a,v()},P=()=>{u.value=!1,w.value=null,t.configName="",t.configKey="",t.configValue="",t.status=1,t.remark="",c.value=!0},E=a=>{u.value=!0,w.value=a.id,t.configName=a.configName,t.configKey=a.configKey,t.configValue=a.configValue,t.status=a.status,t.remark=a.remark||"",c.value=!0},B=async()=>{N.value&&await N.value.validate(async a=>{if(a){k.value=!0;try{let e;u.value?e=await H(w.value,{configValue:t.configValue,status:t.status,remark:t.remark}):e=await J({configName:t.configName,configKey:t.configKey,configValue:t.configValue,status:t.status,remark:t.remark}),e.code===0||e.code===200?(g.success(u.value?"更新成功":"添加成功"),c.value=!1,v()):g.error(e.message||(u.value?"更新失败":"添加失败"))}catch(e){console.error(u.value?"更新配置出错:":"添加配置出错:",e),g.error((u.value?"更新":"添加")+"配置失败，请稍后重试")}finally{k.value=!1}}})},F=async a=>{const e=a.status===1?0:1,d=e===1?"启用":"禁用";try{const s=await G(a.id,e);s.code===0||s.code===200?(g.success(`${d}成功`),a.status=e,a.statusName=e===1?"启用":"禁用"):g.error(s.message||`${d}失败`)}catch(s){console.error(`${d}配置出错:`,s),g.error(`${d}失败，请稍后重试`)}};return X(()=>{v()}),(a,e)=>{const d=r("el-button"),s=r("el-table-column"),L=r("el-tag"),T=r("el-table"),q=r("el-pagination"),A=r("el-card"),y=r("el-input"),m=r("el-form-item"),x=r("el-radio"),I=r("el-radio-group"),R=r("el-form"),j=r("el-dialog"),M=Z("loading");return b(),Y("div",ae,[V("div",te,[e[10]||(e[10]=V("h2",null,"积分配置管理",-1)),l(d,{type:"primary",onClick:P},{default:n(()=>e[9]||(e[9]=[f("添加配置")])),_:1,__:[9]})]),l(A,{shadow:"hover",class:"table-card"},{default:n(()=>[ee((b(),z(T,{data:_.value,border:"",stripe:"",style:{width:"100%"},fit:!0,height:"calc(100vh - 250px)",resizable:!1},{default:n(()=>[l(s,{prop:"id",label:"ID",width:"80",align:"center",fixed:""}),l(s,{prop:"configName",label:"配置名称",width:"180"}),l(s,{prop:"configKey",label:"配置键",width:"200"}),l(s,{prop:"configValue",label:"配置值",width:"100",align:"center"},{default:n(o=>[V("span",le,S(o.row.configValue),1)]),_:1}),l(s,{prop:"statusName",label:"状态",width:"100",align:"center"},{default:n(o=>[l(L,{type:o.row.status===1?"success":"danger"},{default:n(()=>[f(S(o.row.statusName),1)]),_:2},1032,["type"])]),_:1}),l(s,{prop:"remark",label:"备注","min-width":"200","show-overflow-tooltip":""}),l(s,{prop:"createTime",label:"创建时间",width:"180",align:"center"}),l(s,{prop:"updateTime",label:"更新时间",width:"180",align:"center"}),l(s,{label:"操作",fixed:"right",width:"150",align:"center"},{default:n(o=>[l(d,{type:"primary",size:"small",onClick:O=>E(o.row)},{default:n(()=>e[11]||(e[11]=[f("编辑")])),_:2,__:[11]},1032,["onClick"]),l(d,{type:o.row.status===1?"danger":"success",size:"small",onClick:O=>F(o.row)},{default:n(()=>[f(S(o.row.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])),[[M,C.value]]),V("div",oe,[l(q,{"current-page":i.pageNum,"onUpdate:currentPage":e[0]||(e[0]=o=>i.pageNum=o),"page-size":i.pageSize,"onUpdate:pageSize":e[1]||(e[1]=o=>i.pageSize=o),"page-sizes":[10,20,50,100],total:i.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:D,onCurrentChange:$},null,8,["current-page","page-size","total"])])]),_:1}),l(j,{modelValue:c.value,"onUpdate:modelValue":e[8]||(e[8]=o=>c.value=o),title:u.value?"编辑积分配置":"添加积分配置",width:"500px"},{footer:n(()=>[l(d,{onClick:e[7]||(e[7]=o=>c.value=!1)},{default:n(()=>e[14]||(e[14]=[f("取消")])),_:1,__:[14]}),l(d,{type:"primary",onClick:B,loading:k.value},{default:n(()=>e[15]||(e[15]=[f("确认")])),_:1,__:[15]},8,["loading"])]),default:n(()=>[l(R,{model:t,rules:U,ref_key:"configFormRef",ref:N,"label-width":"100px"},{default:n(()=>[u.value?K("",!0):(b(),z(m,{key:0,label:"配置名称",prop:"configName"},{default:n(()=>[l(y,{modelValue:t.configName,"onUpdate:modelValue":e[2]||(e[2]=o=>t.configName=o),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1})),u.value?K("",!0):(b(),z(m,{key:1,label:"配置键",prop:"configKey"},{default:n(()=>[l(y,{modelValue:t.configKey,"onUpdate:modelValue":e[3]||(e[3]=o=>t.configKey=o),placeholder:"请输入配置键"},null,8,["modelValue"])]),_:1})),l(m,{label:"配置值",prop:"configValue"},{default:n(()=>[l(y,{modelValue:t.configValue,"onUpdate:modelValue":e[4]||(e[4]=o=>t.configValue=o),placeholder:"请输入配置值"},null,8,["modelValue"])]),_:1}),l(m,{label:"状态",prop:"status"},{default:n(()=>[l(I,{modelValue:t.status,"onUpdate:modelValue":e[5]||(e[5]=o=>t.status=o)},{default:n(()=>[l(x,{label:1},{default:n(()=>e[12]||(e[12]=[f("启用")])),_:1,__:[12]}),l(x,{label:0},{default:n(()=>e[13]||(e[13]=[f("禁用")])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"备注",prop:"remark"},{default:n(()=>[l(y,{modelValue:t.remark,"onUpdate:modelValue":e[6]||(e[6]=o=>t.remark=o),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},de=W(ne,[["__scopeId","data-v-21e1d7c4"]]);export{de as default};

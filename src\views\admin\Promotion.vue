<template>
  <div class="promotion-management">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>推广内容管理</span>
          <el-button type="primary" @click="handleAddPromotion">添加推广内容</el-button>
        </div>
      </template>
      
      <el-table 
        :data="promotionList" 
        style="width: 100%" 
        v-loading="loading"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="150" />
        <el-table-column label="图片" width="120">
          <template #default="scope">
            <el-image 
              style="width: 80px; height: 45px" 
              :src="scope.row.imageUrl" 
              fit="cover"
              :preview-src-list="[scope.row.imageUrl]"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><el-icon-picture /></el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              @click="handleViewPromotion(scope.row)"
            >查看</el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleEditPromotion(scope.row)"
            >编辑</el-button>
            <el-button 
              v-if="scope.row.status === 0"
              size="small" 
              type="success" 
              @click="handleEnablePromotion(scope.row)"
            >启用</el-button>
            <el-button 
              v-if="scope.row.status === 1"
              size="small" 
              type="warning" 
              @click="handleDisablePromotion(scope.row)"
            >禁用</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDeletePromotion(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 推广内容详情对话框 -->
    <el-dialog
      v-model="dialogVisible.view"
      title="推广内容详情"
      width="600px"
    >
      <div class="promotion-detail">
        <h2>{{ currentPromotion.title }}</h2>
        <div class="promotion-image">
          <el-image 
            style="max-width: 100%; max-height: 300px" 
            :src="currentPromotion.imageUrl" 
            fit="contain"
          >
            <template #error>
              <div class="image-error">
                <el-icon><el-icon-picture /></el-icon>
              </div>
            </template>
          </el-image>
        </div>
        <div class="promotion-content">{{ currentPromotion.content }}</div>
        <div class="promotion-info">
          <p><strong>排序:</strong> {{ currentPromotion.sortOrder }}</p>
          <p><strong>状态:</strong> {{ currentPromotion.status === 1 ? '启用' : '禁用' }}</p>
          <p><strong>创建时间:</strong> {{ currentPromotion.createTime }}</p>
          <p><strong>更新时间:</strong> {{ currentPromotion.updateTime }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 推广内容编辑对话框 -->
    <el-dialog
      v-model="dialogVisible.edit"
      :title="isAddingPromotion ? '添加推广内容' : '编辑推广内容'"
      width="600px"
    >
      <el-form 
        :model="promotionForm" 
        :rules="promotionRules" 
        ref="promotionFormRef"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="promotionForm.title" placeholder="请输入推广标题" />
        </el-form-item>
        
        <el-form-item label="内容" prop="content">
          <el-input 
            v-model="promotionForm.content" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入推广内容"
          />
        </el-form-item>
        
        <el-form-item label="图片URL" prop="imageUrl">
          <el-input v-model="promotionForm.imageUrl" placeholder="请输入图片URL" />
          <div class="image-preview" v-if="promotionForm.imageUrl">
            <el-image 
              style="width: 100px; height: 60px; margin-top: 10px;" 
              :src="promotionForm.imageUrl" 
              fit="cover"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><el-icon-picture /></el-icon>
                </div>
              </template>
            </el-image>
          </div>
        </el-form-item>
        
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="promotionForm.sortOrder" :min="1" :max="999" />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="promotionForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible.edit = false">取消</el-button>
          <el-button type="primary" @click="submitPromotionForm" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getPromotionList,
  getPromotionDetail,
  createPromotion,
  updatePromotion,
  deletePromotion,
  enablePromotion,
  disablePromotion
} from '../../api/admin'

// 推广内容列表
const promotionList = ref([])

// 加载状态
const loading = ref(false)

// 提交状态
const submitting = ref(false)

// 对话框显示状态
const dialogVisible = reactive({
  view: false,
  edit: false
})

// 当前操作的推广内容
const currentPromotion = reactive({
  id: null,
  title: '',
  content: '',
  imageUrl: '',
  sortOrder: 1,
  status: 1,
  createTime: '',
  updateTime: ''
})

// 推广内容表单
const promotionForm = reactive({
  id: null,
  title: '',
  content: '',
  imageUrl: '',
  sortOrder: 1,
  status: 1
})

// 推广内容表单引用
const promotionFormRef = ref(null)

// 是否为添加推广内容
const isAddingPromotion = ref(true)

// 推广内容表单验证规则
const promotionRules = {
  title: [
    { required: true, message: '请输入推广标题', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入推广内容', trigger: 'blur' }
  ],
  imageUrl: [
    { required: true, message: '请输入图片URL', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序值', trigger: 'blur' }
  ]
}

// 获取推广内容列表
const fetchPromotionList = async () => {
  loading.value = true
  try {
    const res = await getPromotionList()
    promotionList.value = res.data || []
  } catch (error) {
    console.error('获取推广内容列表失败:', error)
    ElMessage.error('获取推广内容列表失败')
  } finally {
    loading.value = false
  }
}

// 查看推广内容详情
const handleViewPromotion = (row) => {
  Object.assign(currentPromotion, row)
  dialogVisible.view = true
}

// 添加推广内容
const handleAddPromotion = () => {
  isAddingPromotion.value = true
  resetPromotionForm()
  dialogVisible.edit = true
}

// 编辑推广内容
const handleEditPromotion = (row) => {
  isAddingPromotion.value = false
  resetPromotionForm()
  Object.assign(promotionForm, row)
  dialogVisible.edit = true
}

// 启用推广内容
const handleEnablePromotion = async (row) => {
  try {
    await enablePromotion(row.id)
    ElMessage.success('启用推广内容成功')
    fetchPromotionList()
  } catch (error) {
    console.error('启用推广内容失败:', error)
    ElMessage.error('启用推广内容失败')
  }
}

// 禁用推广内容
const handleDisablePromotion = async (row) => {
  try {
    await disablePromotion(row.id)
    ElMessage.success('禁用推广内容成功')
    fetchPromotionList()
  } catch (error) {
    console.error('禁用推广内容失败:', error)
    ElMessage.error('禁用推广内容失败')
  }
}

// 删除推广内容
const handleDeletePromotion = (row) => {
  ElMessageBox.confirm('确定要删除该推广内容吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deletePromotion(row.id)
      ElMessage.success('删除推广内容成功')
      fetchPromotionList()
    } catch (error) {
      console.error('删除推广内容失败:', error)
      ElMessage.error('删除推广内容失败')
    }
  }).catch(() => {})
}

// 重置推广内容表单
const resetPromotionForm = () => {
  if (promotionFormRef.value) {
    promotionFormRef.value.resetFields()
  }
  
  promotionForm.id = null
  promotionForm.title = ''
  promotionForm.content = ''
  promotionForm.imageUrl = ''
  promotionForm.sortOrder = 1
  promotionForm.status = 1
}

// 提交推广内容表单
const submitPromotionForm = async () => {
  if (!promotionFormRef.value) return
  
  await promotionFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (isAddingPromotion.value) {
          // 创建推广内容
          await createPromotion(promotionForm)
          ElMessage.success('创建推广内容成功')
        } else {
          // 更新推广内容
          await updatePromotion(promotionForm)
          ElMessage.success('更新推广内容成功')
        }
        
        dialogVisible.edit = false
        fetchPromotionList()
      } catch (error) {
        console.error('提交推广内容失败:', error)
        ElMessage.error('提交推广内容失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

onMounted(() => {
  fetchPromotionList()
})
</script>

<style scoped>
.promotion-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.promotion-detail h2 {
  margin-top: 0;
  margin-bottom: 20px;
}

.promotion-image {
  margin-bottom: 20px;
  text-align: center;
}

.promotion-content {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 5px;
  white-space: pre-wrap;
}

.promotion-info p {
  margin: 8px 0;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 30px;
}
</style>

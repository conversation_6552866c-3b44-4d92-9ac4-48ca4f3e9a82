<template>
  <div class="system-config-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>系统配置管理</span>
          <el-button type="primary" @click="handleAdd">新增配置</el-button>
        </div>
      </template>
      
      <!-- 筛选区域 -->
      <div class="filter-container">
        <el-select v-model="filterType" placeholder="配置类型" clearable>
          <el-option label="全部" value="" />
          <el-option label="链接跳转" :value="1" />
          <el-option label="系统参数" :value="2" />
          <el-option label="其他" :value="3" />
        </el-select>
        <el-select v-model="filterStatus" placeholder="状态" clearable>
          <el-option label="全部" value="" />
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索配置名称或键名"
          clearable
          style="width: 300px; margin-left: 10px"
        />
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="filteredTableData"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="70" align="center" />
        <el-table-column prop="configName" label="配置名称" min-width="150" />
        <el-table-column prop="configKey" label="配置键" min-width="180" />
        <el-table-column prop="configValue" label="配置值" min-width="220">
          <template #default="{ row }">
            <div class="config-value-container">
              <span class="config-value-text">{{ row.configValue }}</span>
              <el-button 
                type="primary" 
                link
                v-if="isUrl(row.configValue)" 
                @click="openUrl(row.configValue)"
              >
                访问
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="configTypeName" label="配置类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getConfigTypeTag(row.configType)">
              {{ row.configType === 1 ? '链接跳转' : row.configType === 2 ? '系统参数' : '其他' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="statusName" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" align="center" />
        <el-table-column fixed="right" label="操作" width="240" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link @click="handleToggleStatus(row)">
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-popconfirm
              title="确定删除该配置吗？"
              @confirm="handleDelete(row.id)"
            >
              <template #reference>
                <el-button type="danger" link>删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 新增/编辑配置弹窗 -->
    <el-dialog
      :title="dialogType === 'add' ? '新增系统配置' : '编辑系统配置'"
      v-model="dialogVisible"
      width="600px"
      append-to-body
    >
      <el-form ref="configFormRef" :model="configForm" :rules="configRules" label-width="100px">
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="configForm.configName" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置键" prop="configKey">
          <el-input v-model="configForm.configKey" placeholder="请输入配置键" :disabled="dialogType === 'edit'" />
        </el-form-item>
        <el-form-item label="配置值" prop="configValue">
          <el-input v-model="configForm.configValue" placeholder="请输入配置值" />
        </el-form-item>
        <el-form-item label="配置类型" prop="configType">
          <el-select v-model="configForm.configType" placeholder="请选择配置类型">
            <el-option label="链接跳转" :value="1" />
            <el-option label="系统参数" :value="2" />
            <el-option label="其他" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="configForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="configForm.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getAllSystemConfigs,
  createSystemConfig,
  updateSystemConfig,
  deleteSystemConfig,
  enableSystemConfig,
  disableSystemConfig
} from '@/api/systemConfig'

export default {
  name: 'SystemConfig',
  setup() {
    // 数据加载状态
    const loading = ref(false)
    // 配置数据
    const tableData = ref([])
    // 筛选条件
    const filterType = ref('')
    const filterStatus = ref('')
    const searchKeyword = ref('')
    
    // 对话框相关
    const dialogVisible = ref(false)
    const dialogType = ref('add') // 'add' 或 'edit'
    const configForm = reactive({
      id: null,
      configName: '',
      configKey: '',
      configValue: '',
      configType: 1,
      status: 1,
      remark: ''
    })
    
    // 表单验证规则
    const configRules = {
      configName: [
        { required: true, message: '请输入配置名称', trigger: 'blur' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
      ],
      configKey: [
        { required: true, message: '请输入配置键', trigger: 'blur' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' },
        { pattern: /^[A-Z][A-Z0-9_]*$/, message: '配置键必须以大写字母开头，只能包含大写字母、数字和下划线', trigger: 'blur' }
      ],
      configValue: [
        { required: true, message: '请输入配置值', trigger: 'blur' },
        { max: 500, message: '长度不能超过 500 个字符', trigger: 'blur' }
      ],
      configType: [
        { required: true, message: '请选择配置类型', trigger: 'change' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }
    
    // 表单引用
    const configFormRef = ref(null)
    
    // 筛选后的表格数据
    const filteredTableData = computed(() => {
      let result = [...tableData.value]
      
      // 根据类型筛选
      if (filterType.value !== '') {
        result = result.filter(item => item.configType === filterType.value)
      }
      
      // 根据状态筛选
      if (filterStatus.value !== '') {
        result = result.filter(item => item.status === filterStatus.value)
      }
      
      // 根据关键字搜索
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase()
        result = result.filter(item => 
          item.configName.toLowerCase().includes(keyword) || 
          item.configKey.toLowerCase().includes(keyword)
        )
      }
      
      return result
    })
    
    // 获取所有系统配置
    const fetchSystemConfigs = async () => {
      loading.value = true
      try {
        const res = await getAllSystemConfigs()
        tableData.value = res.data
      } catch (error) {
        console.error('获取系统配置失败:', error)
        ElMessage.error('获取系统配置失败')
      } finally {
        loading.value = false
      }
    }
    
    // 添加配置
    const handleAdd = () => {
      dialogType.value = 'add'
      resetForm()
      dialogVisible.value = true
    }
    
    // 编辑配置
    const handleEdit = (row) => {
      dialogType.value = 'edit'
      resetForm()
      
      // 填充表单数据
      Object.assign(configForm, {
        id: row.id,
        configName: row.configName,
        configKey: row.configKey,
        configValue: row.configValue,
        configType: row.configType,
        status: row.status,
        remark: row.remark || ''
      })
      
      // 使用nextTick确保DOM更新后再显示对话框
      nextTick(() => {
        dialogVisible.value = true
      })
    }
    
    // 提交表单
    const handleSubmit = async () => {
      if (!configFormRef.value) return
      
      configFormRef.value.validate(async valid => {
        if (valid) {
          loading.value = true
          try {
            const submitData = { ...configForm }; // 创建一个副本避免直接修改reactive对象
            
            if (dialogType.value === 'add') {
              // 创建新配置
              await createSystemConfig(submitData)
              ElMessage.success('添加成功')
            } else {
              // 更新配置
              await updateSystemConfig(submitData)
              ElMessage.success('更新成功')
            }
            
            // 刷新数据
            fetchSystemConfigs()
            // 关闭对话框
            dialogVisible.value = false
          } catch (error) {
            console.error(dialogType.value === 'add' ? '添加失败:' : '更新失败:', error)
            ElMessage.error(dialogType.value === 'add' ? '添加失败' : '更新失败')
          } finally {
            loading.value = false
          }
        }
      })
    }
    
    // 切换状态
    const handleToggleStatus = async (row) => {
      loading.value = true
      try {
        if (row.status === 1) {
          // 禁用
          await disableSystemConfig(row.id)
          ElMessage.success('已禁用')
        } else {
          // 启用
          await enableSystemConfig(row.id)
          ElMessage.success('已启用')
        }
        
        // 刷新数据
        fetchSystemConfigs()
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      } finally {
        loading.value = false
      }
    }
    
    // 删除配置
    const handleDelete = async (id) => {
      loading.value = true
      try {
        await deleteSystemConfig(id)
        ElMessage.success('删除成功')
        
        // 刷新数据
        fetchSystemConfigs()
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      } finally {
        loading.value = false
      }
    }
    
    // 搜索
    const handleSearch = () => {
      // 搜索逻辑已在计算属性中实现
    }
    
    // 重置搜索
    const resetSearch = () => {
      filterType.value = ''
      filterStatus.value = ''
      searchKeyword.value = ''
    }
    
    // 重置表单
    const resetForm = () => {
      // 重置表单数据
      Object.assign(configForm, {
        id: null,
        configName: '',
        configKey: '',
        configValue: '',
        configType: 1,
        status: 1,
        remark: ''
      })
      
      // 重置表单验证
      if (configFormRef.value) {
        configFormRef.value.resetFields()
      }
    }
    
    // 判断是否是URL
    const isUrl = (value) => {
      try {
        return value.startsWith('http://') || value.startsWith('https://')
      } catch (e) {
        return false
      }
    }
    
    // 打开URL
    const openUrl = (url) => {
      window.open(url, '_blank')
    }
    
    // 获取配置类型对应的Tag类型
    const getConfigTypeTag = (type) => {
      switch (type) {
        case 1: return 'primary' // 链接跳转
        case 2: return 'success' // 系统参数
        case 3: return 'info'    // 其他
        default: return ''
      }
    }
    
    onMounted(() => {
      fetchSystemConfigs()
    })
    
    onBeforeUnmount(() => {
      // 清理工作，如取消未完成的请求等
    })
    
    return {
      loading,
      tableData,
      filteredTableData,
      filterType,
      filterStatus,
      searchKeyword,
      dialogVisible,
      dialogType,
      configForm,
      configRules,
      configFormRef,
      handleAdd,
      handleEdit,
      handleSubmit,
      handleToggleStatus,
      handleDelete,
      handleSearch,
      resetSearch,
      isUrl,
      openUrl,
      getConfigTypeTag
    }
  }
}
</script>

<style scoped>
.system-config-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.filter-container > * {
  margin-right: 10px;
}

.config-value-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.config-value-text {
  word-break: break-all;
  flex: 1;
}
</style> 
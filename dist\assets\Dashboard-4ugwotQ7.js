import{i as B}from"./index-Bb6yjXMn.js";import{g as V}from"./admin-CZaj3CWx.js";import{C as m}from"./constants-DT5j32Dw.js";import{_ as F,b as j,d as z,O as L,c as b,a as e,w as s,r as d,E as M,o as w,f as a,L as p,j as P,e as S}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const U={class:"dashboard-container"},I={class:"data-header"},X={class:"data-value"},Y={class:"data-footer"},q={class:"data-change"},G={class:"data-header"},H={class:"data-value"},J={class:"data-footer"},Q={class:"data-change"},W={class:"data-header"},Z={class:"data-value"},$={class:"data-footer"},tt={class:"data-change"},et={class:"data-header"},at={class:"data-value"},st={class:"data-footer"},ot={class:"card-header"},nt={key:0,class:"no-data"},rt={__name:"Dashboard",setup(dt){const v=j(null);let u=null;const o=z({cardKeyStatistics:{},userStatistics:{},recentActivations:[]}),h=_=>{const t=m.find(n=>n.value===_);return t?t.label:"未知"},C=_=>{switch(_){case 1:return"info";case 2:return"warning";case 3:return"success";case 4:return"primary";case 5:return"";case 6:return"danger";case 7:return"danger";default:return"info"}},K=()=>{var t;if(!v.value)return;u=B(v.value);const _={title:{text:"卡密类型分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:m.map(n=>n.label)},series:[{name:"卡密类型",type:"pie",radius:"60%",center:["50%","60%"],data:(t=o.cardKeyStatistics)!=null&&t.typeDistribution?o.cardKeyStatistics.typeDistribution.map(n=>{var i;return{name:n.typeName||h(n.type),value:n.count,itemStyle:{color:((i=m.find(l=>l.value===n.type))==null?void 0:i.color)||"#999"}}}):[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};u.setOption(_)},T=async()=>{var _;try{const t=await V();if(t.code===200&&t.data)Object.assign(o,t.data),u&&u.setOption({series:[{data:(_=o.cardKeyStatistics)!=null&&_.typeDistribution?o.cardKeyStatistics.typeDistribution.map(n=>{var i;return{name:n.typeName||h(n.type),value:n.count,itemStyle:{color:((i=m.find(l=>l.value===n.type))==null?void 0:i.color)||"#999"}}}):[]}]});else throw new Error("获取数据失败")}catch(t){console.error("获取统计数据失败:",t),M.error("获取统计数据失败")}};return L(()=>{T(),setTimeout(()=>{K(),window.addEventListener("resize",()=>{u&&u.resize()})},100)}),(_,t)=>{const n=d("el-icon-user"),i=d("el-icon"),l=d("el-card"),f=d("el-col"),k=d("el-icon-ticket"),D=d("el-icon-document-checked"),x=d("el-icon-data-line"),E=d("el-progress"),g=d("el-row"),N=d("el-button"),y=d("el-table-column"),A=d("el-tag"),R=d("el-table"),O=d("el-empty");return w(),b("div",U,[e(g,{gutter:20},{default:s(()=>[e(f,{span:6},{default:s(()=>[e(l,{shadow:"hover",class:"data-card"},{default:s(()=>{var r,c;return[a("div",I,[t[0]||(t[0]=a("div",{class:"data-title"},"总用户数",-1)),e(i,{class:"data-icon",color:"#409EFF"},{default:s(()=>[e(n)]),_:1})]),a("div",X,p(((r=o.userStatistics)==null?void 0:r.totalUsers)||0),1),a("div",Y,[t[1]||(t[1]=a("span",{class:"data-label"},"新增: ",-1)),a("span",q,p(((c=o.userStatistics)==null?void 0:c.newUsers)||0),1)])]}),_:1})]),_:1}),e(f,{span:6},{default:s(()=>[e(l,{shadow:"hover",class:"data-card"},{default:s(()=>{var r,c;return[a("div",G,[t[2]||(t[2]=a("div",{class:"data-title"},"总卡密数",-1)),e(i,{class:"data-icon",color:"#67C23A"},{default:s(()=>[e(k)]),_:1})]),a("div",H,p(((r=o.cardKeyStatistics)==null?void 0:r.totalCardKeys)||0),1),a("div",J,[t[3]||(t[3]=a("span",{class:"data-label"},"已使用: ",-1)),a("span",Q,p(((c=o.cardKeyStatistics)==null?void 0:c.usedCardKeys)||0),1)])]}),_:1})]),_:1}),e(f,{span:6},{default:s(()=>[e(l,{shadow:"hover",class:"data-card"},{default:s(()=>{var r,c;return[a("div",W,[t[4]||(t[4]=a("div",{class:"data-title"},"未使用卡密",-1)),e(i,{class:"data-icon",color:"#E6A23C"},{default:s(()=>[e(D)]),_:1})]),a("div",Z,p(((r=o.cardKeyStatistics)==null?void 0:r.unusedCardKeys)||0),1),a("div",$,[t[5]||(t[5]=a("span",{class:"data-label"},"已过期: ",-1)),a("span",tt,p(((c=o.cardKeyStatistics)==null?void 0:c.expiredCardKeys)||0),1)])]}),_:1})]),_:1}),e(f,{span:6},{default:s(()=>[e(l,{shadow:"hover",class:"data-card"},{default:s(()=>{var r,c;return[a("div",et,[t[6]||(t[6]=a("div",{class:"data-title"},"使用率",-1)),e(i,{class:"data-icon",color:"#F56C6C"},{default:s(()=>[e(x)]),_:1})]),a("div",at,p(((r=o.cardKeyStatistics)==null?void 0:r.usageRate)!==void 0?o.cardKeyStatistics.usageRate:0)+"% ",1),a("div",st,[e(E,{percentage:((c=o.cardKeyStatistics)==null?void 0:c.usageRate)!==void 0?o.cardKeyStatistics.usageRate:0},null,8,["percentage"])])]}),_:1})]),_:1})]),_:1}),e(g,{gutter:20,class:"mt-20"},{default:s(()=>[e(f,{span:12},{default:s(()=>[e(l,{shadow:"hover"},{header:s(()=>t[7]||(t[7]=[a("div",{class:"card-header"},[a("span",null,"卡密类型分布")],-1)])),default:s(()=>[a("div",{class:"chart-container",ref_key:"cardTypeChartRef",ref:v},null,512)]),_:1})]),_:1}),e(f,{span:12},{default:s(()=>[e(l,{shadow:"hover"},{header:s(()=>[a("div",ot,[t[9]||(t[9]=a("span",null,"最近卡密激活记录",-1)),e(N,{type:"primary",link:""},{default:s(()=>t[8]||(t[8]=[S("查看更多")])),_:1,__:[8]})])]),default:s(()=>[e(R,{data:o.recentActivations||[],stripe:"",style:{width:"100%"}},{default:s(()=>[e(y,{prop:"userPhone",label:"用户",width:"120"}),e(y,{prop:"keyType",label:"卡密类型",width:"120"},{default:s(r=>[e(A,{type:C(r.row.keyType)},{default:s(()=>[S(p(r.row.typeName||h(r.row.keyType)),1)]),_:2},1032,["type"])]),_:1}),e(y,{prop:"activateTime",label:"激活时间"}),e(y,{prop:"expireTime",label:"到期时间"})]),_:1},8,["data"]),o.recentActivations&&o.recentActivations.length?P("",!0):(w(),b("div",nt,[e(O,{description:"暂无激活记录"})]))]),_:1})]),_:1})]),_:1})])}}},ut=F(rt,[["__scopeId","data-v-c5922fa2"]]);export{ut as default};

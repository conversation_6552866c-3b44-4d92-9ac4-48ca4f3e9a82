// 卡密类型
export const CARD_TYPES = [
  { value: 1, label: '体验卡', duration: '12小时', color: '#909399' },
  { value: 2, label: '天卡', duration: '1天', color: '#E6A23C' },
  { value: 3, label: '周卡', duration: '7天', color: '#67C23A' },
  { value: 4, label: '月卡', duration: '30天', color: '#409EFF' },
  { value: 5, label: '季卡', duration: '90天', color: '#9B59B6' },
  { value: 6, label: '年卡', duration: '365天', color: '#F56C6C' },
  { value: 7, label: '永久卡', duration: '永久', color: '#FF4500' }
]

// 卡密类型常量
export const CARD_TYPE = {
  DAILY: 2,
  WEEKLY: 3,
  MONTHLY: 4,
  YEARLY: 6,
  PERMANENT: 7
}

// 卡密状态
export const CARD_STATUS = {
  UNUSED: 0,
  USED: 1,
  EXPIRED: 2
}

// 卡密状态选项
export const CARD_STATUS_OPTIONS = [
  { value: 0, label: '未使用', color: '#67C23A' },
  { value: 1, label: '已使用', color: '#409EFF' },
  { value: 2, label: '已过期', color: '#909399' }
]

// 管理员角色
export const ADMIN_ROLES = [
  { value: 1, label: '普通管理员' },
  { value: 2, label: '超级管理员' }
]

// 用户状态
export const USER_STATUS = [
  { value: 0, label: '禁用', color: '#F56C6C' },
  { value: 1, label: '启用', color: '#67C23A' }
]

// 统计周期
export const STATISTICS_PERIODS = [
  { value: 'day', label: '日' },
  { value: 'week', label: '周' },
  { value: 'month', label: '月' }
]

/**
 * 根据值获取标签
 * @param {Array} options 选项数组
 * @param {*} value 值
 * @returns {String} 标签
 */
export function getLabelByValue(options, value) {
  const option = options.find(item => item.value === value)
  return option ? option.label : ''
}

/**
 * 根据值获取颜色
 * @param {Array} options 选项数组
 * @param {*} value 值
 * @returns {String} 颜色
 */
export function getColorByValue(options, value) {
  const option = options.find(item => item.value === value)
  return option && option.color ? option.color : ''
} 
import{b as O,a as L}from"./user-CM_uicz1.js";import{C as I,g as M}from"./constants-DT5j32Dw.js";import{_ as q,b as k,d as v,O as P,c as g,a as t,w as s,r as o,o as y,j as V,f as e,L as n,e as b,k as U,E as A}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const Y={class:"activate-card"},z={key:0,class:"active-card-info"},G={class:"activate-warning"},H={key:1,class:"activate-result"},J={class:"card-types-info"},K={class:"card-type-desc"},Q={__name:"ActivateCard",setup(W){const d=k(null),u=k(!1),m=localStorage.getItem("userId"),i=v({cardCode:"",userId:m}),N={cardCode:[{required:!0,message:"请输入卡密",trigger:"blur"},{min:16,max:16,message:"卡密长度为16位",trigger:"blur"}]},c=v({hasActiveCard:!1,cardInfo:{cardType:0,typeName:"",activateTime:"",expireTime:"",remainDays:0}}),l=v({success:!1,message:"",cardType:0,activateTime:"",expireTime:""}),C=r=>M(I,r),S=r=>{switch(r){case 1:return"体验卡提供12小时的服务体验，适合新用户试用。";case 2:return"天卡提供1天的服务使用权限，适合短期使用。";case 3:return"周卡提供7天的服务使用权限，性价比较高。";case 4:return"月卡提供30天的服务使用权限，最受欢迎的选择。";case 5:return"季卡提供90天的服务使用权限，长期使用更划算。";case 6:return"年卡提供365天的服务使用权限，长期用户的理想选择。";case 7:return"永久卡提供永久的服务使用权限，一次购买终身使用。";default:return""}},D=async()=>{d.value&&await d.value.validate(async r=>{if(r){u.value=!0;try{const a=await L(i);Object.assign(l,a.data),await h(),A.success("卡密激活成功")}catch(a){console.error("激活卡密失败:",a),A.error("激活卡密失败，请检查卡密是否正确或已被使用"),l.success=!1,l.message=""}finally{u.value=!1}}})},F=()=>{d.value&&d.value.resetFields(),l.success=!1,l.message=""},h=async()=>{if(m)try{const r=await O(m);Object.assign(c,r.data)}catch(r){console.error("获取卡密状态失败:",r),c.hasActiveCard=!1}};return P(()=>{h()}),(r,a)=>{const p=o("el-alert"),R=o("el-input"),w=o("el-form-item"),T=o("el-button"),B=o("el-form"),E=o("el-divider"),x=o("el-card"),_=o("el-table-column"),j=o("el-table");return y(),g("div",Y,[t(x,{shadow:"hover"},{header:s(()=>a[1]||(a[1]=[e("div",{class:"card-header"},[e("span",null,"激活卡密")],-1)])),default:s(()=>[c.hasActiveCard?(y(),g("div",z,[t(p,{title:"您已有激活的卡密",type:"warning",closable:!1,class:"mb-20"},{default:s(()=>[e("p",null,"当前卡密类型: "+n(C(c.cardInfo.cardType)),1),e("p",null,"激活时间: "+n(c.cardInfo.activateTime),1),e("p",null,"到期时间: "+n(c.cardInfo.expireTime),1),e("p",null,"剩余天数: "+n(c.cardInfo.remainDays)+"天",1)]),_:1}),e("div",G,[t(p,{title:"提示：激活新卡密将会覆盖当前的卡密状态",type:"info",closable:!1})])])):V("",!0),t(B,{ref_key:"activateFormRef",ref:d,model:i,rules:N,"label-width":"100px",class:"activate-form"},{default:s(()=>[t(w,{label:"卡密",prop:"cardCode"},{default:s(()=>[t(R,{modelValue:i.cardCode,"onUpdate:modelValue":a[0]||(a[0]=f=>i.cardCode=f),placeholder:"请输入卡密",maxlength:"16","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(w,null,{default:s(()=>[t(T,{type:"primary",loading:u.value,onClick:D},{default:s(()=>a[2]||(a[2]=[b("激活")])),_:1,__:[2]},8,["loading"]),t(T,{onClick:F},{default:s(()=>a[3]||(a[3]=[b("重置")])),_:1,__:[3]})]),_:1})]),_:1},8,["model"]),l.success?(y(),g("div",H,[t(E,null,{default:s(()=>a[4]||(a[4]=[b("激活结果")])),_:1,__:[4]}),t(p,{title:l.message,type:"success",closable:!1,class:"mb-20"},{default:s(()=>[e("p",null,"卡密类型: "+n(C(l.cardType)),1),e("p",null,"激活时间: "+n(l.activateTime),1),e("p",null,"到期时间: "+n(l.expireTime),1)]),_:1},8,["title"])])):V("",!0)]),_:1}),t(x,{shadow:"hover",class:"mt-20"},{header:s(()=>a[5]||(a[5]=[e("div",{class:"card-header"},[e("span",null,"卡密说明")],-1)])),default:s(()=>[e("div",J,[t(j,{data:U(I),border:""},{default:s(()=>[t(_,{prop:"label",label:"卡密类型",width:"120"}),t(_,{prop:"duration",label:"有效期",width:"120"}),t(_,{label:"说明"},{default:s(f=>[e("div",K,n(S(f.row.value)),1)]),_:1})]),_:1},8,["data"])]),a[6]||(a[6]=e("div",{class:"card-usage-tips mt-20"},[e("h3",null,"使用须知："),e("ol",null,[e("li",null,"卡密一旦激活，不可退换。"),e("li",null,"卡密有效期从激活时刻开始计算。"),e("li",null,"同一账号只能激活一张卡密，新卡密会覆盖旧卡密。"),e("li",null,"卡密区分大小写，请准确输入。"),e("li",null,"如有问题，请联系客服。")])],-1))]),_:1,__:[6]})])}}},ae=q(Q,[["__scopeId","data-v-06d7ff07"]]);export{ae as default};

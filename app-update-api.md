# 应用更新系统 API 接口文档

## 项目概述

应用更新系统为AutoX.js移动应用提供在线更新功能，包含移动端检查更新和管理端版本管理两套API。

## 基本信息

- 接口基础路径：`http://localhost:8527/api`
- 认证方式：JWT Token（管理端接口需要）
- 响应格式：JSON

## 通用响应格式

```json
{
  "code": 200,           // 状态码，200表示成功
  "message": "操作成功",  // 提示信息
  "data": {}             // 返回的数据
}
```

## 移动端API（无需认证）

### 1. 检查更新

**接口路径**: `POST /api/v1/update/check`

**请求参数**:
```json
{
    "currentVersion": "1.0.0",      // 当前版本号
    "versionCode": 100,             // 当前版本代码
    "deviceId": "device_123456",    // 设备唯一标识
    "platform": "android",         // 平台类型
    "deviceModel": "Xiaomi 12",     // 设备型号(可选)
    "androidVersion": "12"          // Android版本(可选)
}
```

**响应格式**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "hasUpdate": true,
        "version": "1.0.1",
        "versionCode": 101,
        "changelog": "1. 修复已知bug\n2. 优化性能\n3. 新增功能",
        "downloadUrl": "http://localhost:8527/api/v1/update/download/1.0.1",
        "fileSize": 2048576,
        "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
        "forceUpdate": false,
        "minSupportVersion": "0.9.0",
        "releaseNotes": "重要更新说明"
    }
}
```

### 2. 下载更新文件

**接口路径**: `GET /api/v1/update/download/{version}?deviceId={deviceId}`

**请求参数**:
- `version`: 版本号（路径参数）
- `deviceId`: 设备ID（查询参数）

**响应**: 直接返回文件流，支持断点续传

**响应头**:
```
Content-Type: application/zip
Content-Length: 文件大小
Content-Disposition: attachment; filename="app_v1.0.1.zip"
Accept-Ranges: bytes
ETag: "文件MD5值"
```

### 3. 报告更新状态

**接口路径**: `POST /api/v1/update/status`

**请求参数**:
```
deviceId: device_123456          // 设备ID
fromVersion: 1.0.0              // 原版本（可选）
toVersion: 1.0.1                // 目标版本
status: 3                       // 更新状态：1-检查 2-下载中 3-成功 4-失败
errorMessage: 错误信息          // 错误信息（可选）
```

## 管理端API（需要JWT认证）

### 1. 上传版本文件

**接口路径**: `POST /api/v1/admin/version/upload`

**请求格式**: `multipart/form-data`

**请求参数**:
```
file: [ZIP文件]                  // 更新文件
version: 1.0.1                  // 版本号
versionCode: 101                // 版本代码
changelog: 更新日志             // 更新说明（可选）
forceUpdate: false             // 是否强制更新（默认false）
minSupportVersion: 0.9.0       // 最低支持版本（可选）
releaseNotes: 发布说明         // 发布说明（可选）
status: 1                      // 状态：0-禁用 1-启用 2-测试（默认1）
```

**响应格式**:
```json
{
    "code": 200,
    "message": "版本上传成功",
    "data": {
        "id": 1,
        "version": "1.0.1",
        "versionCode": 101,
        "downloadUrl": "http://localhost:8527/api/v1/update/download/1.0.1",
        "fileSize": 2048576,
        "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
        "status": 1,
        "createdTime": "2024-01-01 12:00:00"
    }
}
```

### 2. 获取版本列表

**接口路径**: `GET /api/v1/admin/versions`

**请求参数**:
```
page: 1                          // 页码（默认1）
size: 10                         // 每页数量（默认10）
status: 1                        // 状态筛选（可选）
```

**响应格式**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "total": 20,
        "pages": 2,
        "pageNum": 1,
        "pageSize": 10,
        "list": [
            {
                "id": 1,
                "version": "1.0.1",
                "versionCode": 101,
                "changelog": "更新日志",
                "fileSize": 2048576,
                "status": 1,
                "createdTime": "2024-01-01 12:00:00"
            }
        ]
    }
}
```

### 3. 获取版本详情

**接口路径**: `GET /api/v1/admin/version/{id}`

### 4. 更新版本信息

**接口路径**: `PUT /api/v1/admin/version/{id}`

**请求参数**:
```json
{
    "version": "1.0.1",
    "versionCode": 101,
    "changelog": "更新日志",
    "forceUpdate": false,
    "minSupportVersion": "0.9.0",
    "releaseNotes": "发布说明",
    "status": 1
}
```

### 5. 删除版本

**接口路径**: `DELETE /api/v1/admin/version/{id}`

### 6. 更新版本状态

**接口路径**: `PATCH /api/v1/admin/version/{id}/status?status={status}`

### 7. 获取启用版本列表

**接口路径**: `GET /api/v1/admin/versions/enabled`

### 8. 获取更新统计

**接口路径**: `GET /api/v1/admin/statistics/update`

**响应格式**:
```json
{
    "code": 200,
    "data": {
        "totalDevices": 10000,
        "todayChecks": 1500,
        "todayUpdates": 800,
        "successRate": 95.5,
        "versionDistribution": {
            "1.0.0": 3000,
            "1.0.1": 7000
        }
    }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200    | 成功 |
| 400    | 请求参数错误 |
| 401    | 未授权（未登录或token过期） |
| 404    | 资源不存在 |
| 500    | 服务器内部错误 |

## 业务错误码

| 错误码 | 说明 |
|--------|------|
| 10001  | 版本号格式错误 |
| 10002  | 文件上传失败 |
| 10003  | 版本已存在 |
| 10004  | 版本不存在 |
| 10005  | 文件不存在 |
| 10006  | 文件校验失败 |
| 10007  | 不支持的文件类型 |
| 10008  | 文件大小超限 |

## 使用示例

### 移动端检查更新示例

```javascript
// 检查更新
const checkUpdate = async () => {
    const response = await fetch('/api/v1/update/check', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            currentVersion: '1.0.0',
            versionCode: 100,
            deviceId: 'device_123456',
            platform: 'android'
        })
    });
    
    const result = await response.json();
    if (result.code === 200 && result.data.hasUpdate) {
        // 有更新可用
        console.log('发现新版本:', result.data.version);
        // 下载更新文件
        window.open(result.data.downloadUrl);
    }
};
```

### 管理端上传版本示例

```javascript
// 上传版本文件
const uploadVersion = async (file) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('version', '1.0.1');
    formData.append('versionCode', '101');
    formData.append('changelog', '修复bug，优化性能');
    
    const response = await fetch('/api/v1/admin/version/upload', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + token
        },
        body: formData
    });
    
    const result = await response.json();
    console.log('上传结果:', result);
};
```

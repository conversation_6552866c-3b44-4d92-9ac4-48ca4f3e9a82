<template>
  <div class="activate-card">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>激活卡密</span>
        </div>
      </template>
      
      <div v-if="cardStatus.hasActiveCard" class="active-card-info">
        <el-alert
          title="您已有激活的卡密"
          type="warning"
          :closable="false"
          class="mb-20"
        >
          <template #default>
            <p>当前卡密类型: {{ getCardTypeName(cardStatus.cardInfo.cardType) }}</p>
            <p>激活时间: {{ cardStatus.cardInfo.activateTime }}</p>
            <p>到期时间: {{ cardStatus.cardInfo.expireTime }}</p>
            <p>剩余天数: {{ cardStatus.cardInfo.remainDays }}天</p>
          </template>
        </el-alert>
        
        <div class="activate-warning">
          <el-alert
            title="提示：激活新卡密将会覆盖当前的卡密状态"
            type="info"
            :closable="false"
          />
        </div>
      </div>
      
      <el-form
        ref="activateFormRef"
        :model="activateForm"
        :rules="activateRules"
        label-width="100px"
        class="activate-form"
      >
        <el-form-item label="卡密" prop="cardCode">
          <el-input 
            v-model="activateForm.cardCode" 
            placeholder="请输入卡密" 
            maxlength="16"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleActivate">激活</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 激活结果 -->
      <div v-if="activateResult.success" class="activate-result">
        <el-divider>激活结果</el-divider>
        
        <el-alert
          :title="activateResult.message"
          type="success"
          :closable="false"
          class="mb-20"
        >
          <template #default>
            <p>卡密类型: {{ getCardTypeName(activateResult.cardType) }}</p>
            <p>激活时间: {{ activateResult.activateTime }}</p>
            <p>到期时间: {{ activateResult.expireTime }}</p>
          </template>
        </el-alert>
      </div>
    </el-card>
    
    <!-- 卡密说明 -->
    <el-card shadow="hover" class="mt-20">
      <template #header>
        <div class="card-header">
          <span>卡密说明</span>
        </div>
      </template>
      
      <div class="card-types-info">
        <el-table :data="CARD_TYPES" border>
          <el-table-column prop="label" label="卡密类型" width="120" />
          <el-table-column prop="duration" label="有效期" width="120" />
          <el-table-column label="说明">
            <template #default="scope">
              <div class="card-type-desc">
                {{ getCardTypeDescription(scope.row.value) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div class="card-usage-tips mt-20">
        <h3>使用须知：</h3>
        <ol>
          <li>卡密一旦激活，不可退换。</li>
          <li>卡密有效期从激活时刻开始计算。</li>
          <li>同一账号只能激活一张卡密，新卡密会覆盖旧卡密。</li>
          <li>卡密区分大小写，请准确输入。</li>
          <li>如有问题，请联系客服。</li>
        </ol>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { activateCard, getCardStatus } from '../../api/user'
import { CARD_TYPES, getLabelByValue } from '../../utils/constants'

const activateFormRef = ref(null)
const loading = ref(false)
const userId = localStorage.getItem('userId')

// 激活表单
const activateForm = reactive({
  cardCode: '',
  userId: userId
})

// 表单验证规则
const activateRules = {
  cardCode: [
    { required: true, message: '请输入卡密', trigger: 'blur' },
    { min: 16, max: 16, message: '卡密长度为16位', trigger: 'blur' }
  ]
}

// 卡密状态
const cardStatus = reactive({
  hasActiveCard: false,
  cardInfo: {
    cardType: 0,
    typeName: '',
    activateTime: '',
    expireTime: '',
    remainDays: 0
  }
})

// 激活结果
const activateResult = reactive({
  success: false,
  message: '',
  cardType: 0,
  activateTime: '',
  expireTime: ''
})

// 获取卡密类型名称
const getCardTypeName = (type) => {
  return getLabelByValue(CARD_TYPES, type)
}

// 获取卡密类型说明
const getCardTypeDescription = (type) => {
  switch (type) {
    case 1: return '体验卡提供12小时的服务体验，适合新用户试用。'
    case 2: return '天卡提供1天的服务使用权限，适合短期使用。'
    case 3: return '周卡提供7天的服务使用权限，性价比较高。'
    case 4: return '月卡提供30天的服务使用权限，最受欢迎的选择。'
    case 5: return '季卡提供90天的服务使用权限，长期使用更划算。'
    case 6: return '年卡提供365天的服务使用权限，长期用户的理想选择。'
    case 7: return '永久卡提供永久的服务使用权限，一次购买终身使用。'
    default: return ''
  }
}

// 处理激活卡密
const handleActivate = async () => {
  if (!activateFormRef.value) return
  
  await activateFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const res = await activateCard(activateForm)
        
        // 处理激活结果
        Object.assign(activateResult, res.data)
        
        // 更新卡密状态
        await fetchCardStatus()
        
        ElMessage.success('卡密激活成功')
      } catch (error) {
        console.error('激活卡密失败:', error)
        ElMessage.error('激活卡密失败，请检查卡密是否正确或已被使用')
        
        // 重置激活结果
        activateResult.success = false
        activateResult.message = ''
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (activateFormRef.value) {
    activateFormRef.value.resetFields()
  }
  
  // 重置激活结果
  activateResult.success = false
  activateResult.message = ''
}

// 获取卡密状态
const fetchCardStatus = async () => {
  if (!userId) return
  
  try {
    const res = await getCardStatus(userId)
    Object.assign(cardStatus, res.data)
  } catch (error) {
    console.error('获取卡密状态失败:', error)
    
    // 使用模拟数据
    cardStatus.hasActiveCard = false
  }
}

onMounted(() => {
  fetchCardStatus()
})
</script>

<style scoped>
.activate-card {
  padding: 20px;
}

.active-card-info {
  margin-bottom: 20px;
}

.activate-form {
  max-width: 500px;
}

.activate-result {
  margin-top: 30px;
}

.activate-warning {
  margin: 20px 0;
}

.card-types-info {
  margin-bottom: 20px;
}

.card-type-desc {
  line-height: 1.5;
  color: #606266;
}

.card-usage-tips {
  color: #606266;
}

.card-usage-tips h3 {
  margin-bottom: 10px;
  color: #303133;
}

.card-usage-tips ol {
  padding-left: 20px;
  line-height: 1.8;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}
</style> 
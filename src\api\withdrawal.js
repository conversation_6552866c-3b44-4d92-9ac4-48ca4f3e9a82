import request from '../utils/request'

/**
 * 管理员查询提现申请列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getWithdrawalRequests(params) {
  return request({
    url: '/withdrawal/admin/requests',
    method: 'get',
    params
  })
}

/**
 * 管理员审核提现申请
 * @param {Object} data 审核数据
 * @returns {Promise}
 */
export function auditWithdrawal(data) {
  const token = localStorage.getItem('token') || ''
  return request({
    url: '/withdrawal/admin/audit',
    method: 'post',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    data
  })
}

/**
 * 管理员查询提现记录列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getWithdrawalRecords(params) {
  return request({
    url: '/withdrawal/admin/records',
    method: 'get',
    params
  })
}

/**
 * 管理员查询用户可提现金额列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getAvailableAmounts(params) {
  return request({
    url: '/withdrawal/admin/available-amounts',
    method: 'get',
    params
  })
}

/**
 * 获取用户提现申请列表
 * @param {Number} userId 用户ID
 * @returns {Promise}
 */
export function getUserWithdrawalRequests(userId) {
  return request({
    url: '/withdrawal/user/requests',
    method: 'get',
    params: { userId }
  })
}



/**
 * 获取提现申请详情
 * @param {Number} id 提现申请ID
 * @returns {Promise}
 */
export function getWithdrawalDetail(id) {
  return request({
    url: `/withdrawal/request/${id}`,
    method: 'get'
  })
}

/**
 * 获取用户可提现金额
 * @param {Number} userId 用户ID
 * @returns {Promise}
 */
export function getUserAvailableAmount(userId) {
  return request({
    url: '/withdrawal/available-amount',
    method: 'get',
    params: { userId }
  })
}

/**
 * 获取提现配置汇总信息
 * @returns {Promise}
 */
export function getWithdrawalConfigSummary() {
  return request({
    url: '/withdrawal/config/summary',
    method: 'get'
  })
}

/**
 * 获取提现金额选项列表
 * @returns {Promise}
 */
export function getWithdrawalAmountOptions() {
  return request({
    url: '/withdrawal/config/amount-options',
    method: 'get'
  })
}

/**
 * 获取提现说明
 * @returns {Promise}
 */
export function getWithdrawalInstruction() {
  return request({
    url: '/withdrawal/config/instruction',
    method: 'get'
  })
}

/**
 * 获取所有提现配置列表
 * @returns {Promise}
 */
export function getAllWithdrawalConfigs() {
  return request({
    url: '/withdrawal/config/list',
    method: 'get'
  })
}

/**
 * 根据类型获取提现配置列表
 * @param {String} configType 配置类型（AMOUNT-金额选项，LIMIT-限制设置，INSTRUCTION-提现说明）
 * @returns {Promise}
 */
export function getWithdrawalConfigsByType(configType) {
  return request({
    url: `/withdrawal/config/list/${configType}`,
    method: 'get'
  })
}

/**
 * 获取指定ID的提现配置
 * @param {Number} id 配置ID
 * @returns {Promise}
 */
export function getWithdrawalConfigById(id) {
  return request({
    url: `/withdrawal/config/${id}`,
    method: 'get'
  })
}

/**
 * 添加提现配置
 * @param {Object} data 配置数据
 * @returns {Promise}
 */
export function addWithdrawalConfig(data) {
  return request({
    url: '/withdrawal/config',
    method: 'post',
    data
  })
}

/**
 * 更新提现配置
 * @param {Number} id 配置ID
 * @param {Object} data 配置数据
 * @returns {Promise}
 */
export function updateWithdrawalConfig(id, data) {
  return request({
    url: `/withdrawal/config/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除提现配置
 * @param {Number} id 配置ID
 * @returns {Promise}
 */
export function deleteWithdrawalConfig(id) {
  return request({
    url: `/withdrawal/config/${id}`,
    method: 'delete'
  })
} 
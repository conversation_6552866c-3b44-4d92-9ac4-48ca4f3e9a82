import{g as ge,a as ve,s as ye,b as be,d as _e,c as he}from"./points-ncRAjWQO.js";import{_ as Ve,b as s,d as B,O as we,c as K,f as v,a as l,w as o,r as u,E as i,P as Ce,o as z,e as m,Q as W,i as $,R as Ie,S as Pe,U as Ue,L as E}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const ze={class:"users-container"},ke={class:"pagination-container"},xe={class:"dialog-footer"},De={class:"dialog-footer"},Se={class:"pagination-container"},Le={__name:"users",setup(Ae){const h=s([]),V=s(!1),y=s(!1),b=s(1),k=s(10),w=s(0),_=B({phone:""}),x=s([]),C=s(!1),D=s(null),X=s(null),r=B({userId:"",channelId:"",points:100,description:""}),Y={userId:[{required:!0,message:"请输入用户ID",trigger:"blur"}],channelId:[{required:!0,message:"请选择积分渠道",trigger:"change"}],points:[{required:!0,message:"请输入积分数量",trigger:"blur"},{type:"number",min:1,message:"积分数量必须大于0",trigger:"blur"}]},I=s(!1),S=s(null),n=B({userId:"",phone:"",currentPoints:0,points:0,description:""}),Z={points:[{required:!0,message:"请输入积分数量",trigger:"blur"},{type:"number",min:1,message:"积分数量必须大于0",trigger:"blur"}],description:[{required:!0,message:"请输入备注",trigger:"blur"}]},L=s(!1),A=s(!1),F=s([]),P=s(1),U=s(10),N=s(0),R=s(null),f=async()=>{V.value=!0;try{const t=await ge({pageNum:b.value,pageSize:k.value});t.code===0||t.code===200?t.data&&Array.isArray(t.data)?(h.value=t.data,w.value=t.data.length):(h.value=[],w.value=0):i.error(t.message||"获取用户积分列表失败")}catch(t){console.error("获取用户积分列表出错:",t),i.error("获取用户积分列表失败，请稍后重试")}finally{V.value=!1}},ee=async()=>{try{const t=await ve();t.code===0||t.code===200?t.data&&Array.isArray(t.data)?x.value=t.data.filter(e=>e.status===1):x.value=[]:i.error(t.message||"获取积分渠道列表失败")}catch(t){console.error("获取积分渠道列表出错:",t),i.error("获取积分渠道列表失败，请稍后重试")}},le=t=>{k.value=t,f()},te=t=>{b.value=t,f()},ae=async()=>{b.value=1,V.value=!0;try{if(_.phone){const t=await ye(_.phone);t.code===0||t.code===200?t.data&&Array.isArray(t.data)?(h.value=t.data,w.value=t.data.length):(h.value=[],w.value=0):i.error(t.message||"搜索用户积分信息失败")}else await f()}catch(t){console.error("搜索用户积分信息出错:",t),i.error("搜索用户积分信息失败，请稍后重试")}finally{V.value=!1}},oe=()=>{_.phone="",b.value=1,f()},ne=t=>{X.value=t,r.userId=t.userId,r.channelId="",r.points=100,r.description="",C.value=!0},se=async()=>{D.value&&await D.value.validate(async t=>{if(t){y.value=!0;try{const e=await be({userId:r.userId,points:r.points,channelId:r.channelId,description:r.description});e.code===0||e.code===200?(i.success("添加积分成功"),C.value=!1,f()):i.error(e.message||"添加积分失败")}catch(e){console.error("添加积分出错:",e),i.error("添加积分失败，请稍后重试")}finally{y.value=!1}}})},re=t=>{n.userId=t.userId,n.phone=t.phone,n.currentPoints=t.points,n.points=Math.min(100,t.points),n.description="",I.value=!0},de=async()=>{S.value&&await S.value.validate(async t=>{if(t){y.value=!0;try{const e=await _e({userId:n.userId,points:n.points,description:n.description});e.code===0||e.code===200?(i.success("扣减积分成功"),I.value=!1,f()):i.error(e.message||"扣减积分失败")}catch(e){console.error("扣减积分出错:",e),i.error("扣减积分失败，请稍后重试")}finally{y.value=!1}}})},ie=t=>{R.value=t,P.value=1,U.value=10,L.value=!0,H(t)},H=async t=>{A.value=!0;try{const e=await he(t,{pageNum:P.value,pageSize:U.value});e.code===0||e.code===200?e.data&&e.data.list?(F.value=e.data.list,N.value=e.data.total):(F.value=[],N.value=0):i.error(e.message||"获取积分历史记录失败")}catch(e){console.error("获取积分历史记录出错:",e),i.error("获取积分历史记录失败，请稍后重试")}finally{A.value=!1}},ue=t=>{U.value=t,H(R.value)},pe=t=>{P.value=t,H(R.value)};return we(()=>{f(),ee()}),(t,e)=>{const g=u("el-input"),p=u("el-form-item"),c=u("el-button"),T=u("el-form"),M=u("el-card"),d=u("el-table-column"),j=u("el-table"),O=u("el-pagination"),Q=u("el-input-number"),me=u("el-option"),ce=u("el-select"),q=u("el-dialog"),fe=u("el-tag"),G=Ce("loading");return z(),K("div",ze,[e[28]||(e[28]=v("div",{class:"page-header"},[v("h2",null,"用户积分管理")],-1)),l(M,{class:"search-card"},{default:o(()=>[l(T,{inline:!0,model:_,class:"search-form"},{default:o(()=>[l(p,{label:"手机号"},{default:o(()=>[l(g,{modelValue:_.phone,"onUpdate:modelValue":e[0]||(e[0]=a=>_.phone=a),placeholder:"请输入手机号",clearable:""},null,8,["modelValue"])]),_:1}),l(p,null,{default:o(()=>[l(c,{type:"primary",onClick:ae},{default:o(()=>e[19]||(e[19]=[m("查询")])),_:1,__:[19]}),l(c,{onClick:oe},{default:o(()=>e[20]||(e[20]=[m("重置")])),_:1,__:[20]})]),_:1})]),_:1},8,["model"])]),_:1}),l(M,{class:"table-card"},{default:o(()=>[W((z(),$(j,{data:h.value,style:{width:"100%"},border:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:o(()=>[l(d,{prop:"userId",label:"用户ID",width:"120"}),l(d,{prop:"phone",label:"用户手机号",width:"150"}),l(d,{prop:"points",label:"积分余额",width:"120"}),l(d,{prop:"totalEarnedPoints",label:"累计获得",width:"120"}),l(d,{prop:"totalConsumedPoints",label:"累计消费",width:"120"}),l(d,{prop:"updateTime",label:"最后更新时间",width:"180"}),l(d,{label:"操作",width:"300",fixed:"right"},{default:o(a=>[l(c,{type:"primary",size:"small",onClick:J=>ie(a.row.userId)},{default:o(()=>e[21]||(e[21]=[m(" 查看记录 ")])),_:2,__:[21]},1032,["onClick"]),l(c,{type:"success",size:"small",onClick:J=>ne(a.row)},{default:o(()=>e[22]||(e[22]=[m(" 添加积分 ")])),_:2,__:[22]},1032,["onClick"]),l(c,{type:"warning",size:"small",onClick:J=>re(a.row)},{default:o(()=>e[23]||(e[23]=[m(" 扣减积分 ")])),_:2,__:[23]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[G,V.value]]),v("div",ke,[l(O,{"current-page":b.value,"onUpdate:currentPage":e[1]||(e[1]=a=>b.value=a),"page-size":k.value,"onUpdate:pageSize":e[2]||(e[2]=a=>k.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:w.value,onSizeChange:le,onCurrentChange:te},null,8,["current-page","page-size","total"])])]),_:1}),l(q,{modelValue:C.value,"onUpdate:modelValue":e[8]||(e[8]=a=>C.value=a),title:"添加积分",width:"500px"},{footer:o(()=>[v("span",xe,[l(c,{onClick:e[7]||(e[7]=a=>C.value=!1)},{default:o(()=>e[24]||(e[24]=[m("取消")])),_:1,__:[24]}),l(c,{type:"primary",onClick:se,loading:y.value},{default:o(()=>e[25]||(e[25]=[m(" 确认 ")])),_:1,__:[25]},8,["loading"])])]),default:o(()=>[l(T,{ref_key:"addPointsFormRef",ref:D,model:r,rules:Y,"label-width":"100px"},{default:o(()=>[l(p,{label:"用户ID",prop:"userId"},{default:o(()=>[l(g,{modelValue:r.userId,"onUpdate:modelValue":e[3]||(e[3]=a=>r.userId=a),placeholder:"请输入用户ID"},null,8,["modelValue"])]),_:1}),l(p,{label:"积分数量",prop:"points"},{default:o(()=>[l(Q,{modelValue:r.points,"onUpdate:modelValue":e[4]||(e[4]=a=>r.points=a),min:1,max:1e5},null,8,["modelValue"])]),_:1}),l(p,{label:"积分渠道",prop:"channelId"},{default:o(()=>[l(ce,{modelValue:r.channelId,"onUpdate:modelValue":e[5]||(e[5]=a=>r.channelId=a),placeholder:"请选择积分渠道"},{default:o(()=>[(z(!0),K(Ie,null,Pe(x.value,a=>(z(),$(me,{key:a.id,label:a.channelName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"备注",prop:"description"},{default:o(()=>[l(g,{modelValue:r.description,"onUpdate:modelValue":e[6]||(e[6]=a=>r.description=a),type:"textarea",placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(q,{modelValue:I.value,"onUpdate:modelValue":e[15]||(e[15]=a=>I.value=a),title:"扣减积分",width:"500px"},{footer:o(()=>[v("span",De,[l(c,{onClick:e[14]||(e[14]=a=>I.value=!1)},{default:o(()=>e[26]||(e[26]=[m("取消")])),_:1,__:[26]}),l(c,{type:"primary",onClick:de,loading:y.value},{default:o(()=>e[27]||(e[27]=[m(" 确认 ")])),_:1,__:[27]},8,["loading"])])]),default:o(()=>[l(T,{ref_key:"deductPointsFormRef",ref:S,model:n,rules:Z,"label-width":"100px"},{default:o(()=>[l(p,{label:"用户ID",prop:"userId"},{default:o(()=>[l(g,{modelValue:n.userId,"onUpdate:modelValue":e[9]||(e[9]=a=>n.userId=a),disabled:""},null,8,["modelValue"])]),_:1}),l(p,{label:"用户手机号",prop:"phone"},{default:o(()=>[l(g,{modelValue:n.phone,"onUpdate:modelValue":e[10]||(e[10]=a=>n.phone=a),disabled:""},null,8,["modelValue"])]),_:1}),l(p,{label:"当前积分"},{default:o(()=>[l(g,{modelValue:n.currentPoints,"onUpdate:modelValue":e[11]||(e[11]=a=>n.currentPoints=a),disabled:""},null,8,["modelValue"])]),_:1}),l(p,{label:"扣减积分",prop:"points"},{default:o(()=>[l(Q,{modelValue:n.points,"onUpdate:modelValue":e[12]||(e[12]=a=>n.points=a),min:1,max:n.currentPoints||1e5},null,8,["modelValue","max"])]),_:1}),l(p,{label:"备注",prop:"description"},{default:o(()=>[l(g,{modelValue:n.description,"onUpdate:modelValue":e[13]||(e[13]=a=>n.description=a),type:"textarea",placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(q,{modelValue:L.value,"onUpdate:modelValue":e[18]||(e[18]=a=>L.value=a),title:"积分变动记录",width:"800px"},{default:o(()=>[W((z(),$(j,{data:F.value,style:{width:"100%"},border:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:o(()=>[l(d,{prop:"id",label:"ID",width:"80"}),l(d,{prop:"userId",label:"用户ID",width:"100"}),l(d,{prop:"phone",label:"用户手机号",width:"120"}),l(d,{prop:"pointsChange",label:"变动积分",width:"100"},{default:o(a=>[v("span",{class:Ue(a.row.pointsChange>0?"text-success":"text-danger")},E(a.row.pointsChange>0?"+":"")+E(a.row.pointsChange),3)]),_:1}),l(d,{prop:"operationType",label:"操作类型",width:"120"},{default:o(a=>[l(fe,{type:t.info},{default:o(()=>[m(E(a.row.operationTypeName),1)]),_:2},1032,["type"])]),_:1}),l(d,{prop:"channelName",label:"渠道",width:"120"}),l(d,{prop:"description",label:"备注"}),l(d,{prop:"createTime",label:"操作时间",width:"180"})]),_:1},8,["data"])),[[G,A.value]]),v("div",Se,[l(O,{"current-page":P.value,"onUpdate:currentPage":e[16]||(e[16]=a=>P.value=a),"page-size":U.value,"onUpdate:pageSize":e[17]||(e[17]=a=>U.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:N.value,onSizeChange:ue,onCurrentChange:pe},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue"])])}}},He=Ve(Le,[["__scopeId","data-v-d2d34b3d"]]);export{He as default};

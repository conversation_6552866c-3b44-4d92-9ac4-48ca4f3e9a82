import{s as n}from"./request-YZgFExiB.js";function s(t){return n({url:"/points/channel/list",method:"get",params:t})}function i(t){return n({url:"/points/channel/add",method:"post",data:t})}function r(t){return n({url:"/points/channel/update",method:"put",data:t})}function u(t){return n({url:`/points/channel/delete/${t}`,method:"delete"})}function a(t){return n({url:"/points/user/list",method:"get",params:t})}function d(t,e){return n({url:`/points/records/${t}`,method:"get",params:e})}function c(t){return n({url:"/points/admin/add-by-channel",method:"post",data:t})}function l(t){return n({url:"/points/admin/deduct",method:"post",data:t})}function p(t){return n({url:"/points/config/list",method:"get",params:t})}function h(t){return n({url:"/points/config/add",method:"post",data:t})}function f(t,e){return n({url:"/points/config/update",method:"put",data:{id:t,...e}})}function g(t,e){return n({url:"/points/config/update",method:"put",data:{id:t,status:e}})}function m(t){return n({url:"/points/user/search",method:"get",params:{phone:t}})}export{s as a,c as b,d as c,l as d,p as e,f,a as g,h,u as i,r as j,i as k,m as s,g as u};

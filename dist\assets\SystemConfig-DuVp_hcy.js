import{s as v}from"./request-YZgFExiB.js";import{_ as L,c as R,a as e,w as n,r as u,b as y,d as j,g as O,O as I,T as M,E as g,V as W,P as Z,o as A,f as b,Q as P,e as c,i as E,j as Q,L as x}from"./index-DKz5EXvV.js";function z(){return v({url:"/system/config/list",method:"get"})}function G(r){return v({url:"/system/config",method:"post",data:r})}function H(r){return v({url:"/system/config",method:"put",data:r})}function J(r){return v({url:`/system/config/${r}`,method:"delete"})}function X(r){return v({url:`/system/config/${r}/enable`,method:"put"})}function Y(r){return v({url:`/system/config/${r}/disable`,method:"put"})}const $={name:"SystemConfig",setup(){const r=y(!1),l=y([]),V=y(""),a=y(""),C=y(""),k=y(!1),i=y("add"),d=j({id:null,configName:"",configKey:"",configValue:"",configType:1,status:1,remark:""}),T={configName:[{required:!0,message:"请输入配置名称",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],configKey:[{required:!0,message:"请输入配置键",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"},{pattern:/^[A-Z][A-Z0-9_]*$/,message:"配置键必须以大写字母开头，只能包含大写字母、数字和下划线",trigger:"blur"}],configValue:[{required:!0,message:"请输入配置值",trigger:"blur"},{max:500,message:"长度不能超过 500 个字符",trigger:"blur"}],configType:[{required:!0,message:"请选择配置类型",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},f=y(null),m=O(()=>{let t=[...l.value];if(V.value!==""&&(t=t.filter(s=>s.configType===V.value)),a.value!==""&&(t=t.filter(s=>s.status===a.value)),C.value){const s=C.value.toLowerCase();t=t.filter(B=>B.configName.toLowerCase().includes(s)||B.configKey.toLowerCase().includes(s))}return t}),_=async()=>{r.value=!0;try{const t=await z();l.value=t.data}catch(t){console.error("获取系统配置失败:",t),g.error("获取系统配置失败")}finally{r.value=!1}},N=()=>{i.value="add",F(),k.value=!0},w=t=>{i.value="edit",F(),Object.assign(d,{id:t.id,configName:t.configName,configKey:t.configKey,configValue:t.configValue,configType:t.configType,status:t.status,remark:t.remark||""}),W(()=>{k.value=!0})},U=async()=>{f.value&&f.value.validate(async t=>{if(t){r.value=!0;try{const s={...d};i.value==="add"?(await G(s),g.success("添加成功")):(await H(s),g.success("更新成功")),_(),k.value=!1}catch(s){console.error(i.value==="add"?"添加失败:":"更新失败:",s),g.error(i.value==="add"?"添加失败":"更新失败")}finally{r.value=!1}}})},p=async t=>{r.value=!0;try{t.status===1?(await Y(t.id),g.success("已禁用")):(await X(t.id),g.success("已启用")),_()}catch(s){console.error("操作失败:",s),g.error("操作失败")}finally{r.value=!1}},S=async t=>{r.value=!0;try{await J(t),g.success("删除成功"),_()}catch(s){console.error("删除失败:",s),g.error("删除失败")}finally{r.value=!1}},K=()=>{},D=()=>{V.value="",a.value="",C.value=""},F=()=>{Object.assign(d,{id:null,configName:"",configKey:"",configValue:"",configType:1,status:1,remark:""}),f.value&&f.value.resetFields()},q=t=>{try{return t.startsWith("http://")||t.startsWith("https://")}catch{return!1}},o=t=>{window.open(t,"_blank")},h=t=>{switch(t){case 1:return"primary";case 2:return"success";case 3:return"info";default:return""}};return I(()=>{_()}),M(()=>{}),{loading:r,tableData:l,filteredTableData:m,filterType:V,filterStatus:a,searchKeyword:C,dialogVisible:k,dialogType:i,configForm:d,configRules:T,configFormRef:f,handleAdd:N,handleEdit:w,handleSubmit:U,handleToggleStatus:p,handleDelete:S,handleSearch:K,resetSearch:D,isUrl:q,openUrl:o,getConfigTypeTag:h}}},ee={class:"system-config-container"},le={class:"card-header"},ae={class:"filter-container"},oe={class:"config-value-container"},te={class:"config-value-text"},ne={class:"dialog-footer"};function re(r,l,V,a,C,k){const i=u("el-button"),d=u("el-option"),T=u("el-select"),f=u("el-input"),m=u("el-table-column"),_=u("el-tag"),N=u("el-popconfirm"),w=u("el-table"),U=u("el-card"),p=u("el-form-item"),S=u("el-radio"),K=u("el-radio-group"),D=u("el-form"),F=u("el-dialog"),q=Z("loading");return A(),R("div",ee,[e(U,{class:"box-card"},{header:n(()=>[b("div",le,[l[12]||(l[12]=b("span",null,"系统配置管理",-1)),e(i,{type:"primary",onClick:a.handleAdd},{default:n(()=>l[11]||(l[11]=[c("新增配置")])),_:1,__:[11]},8,["onClick"])])]),default:n(()=>[b("div",ae,[e(T,{modelValue:a.filterType,"onUpdate:modelValue":l[0]||(l[0]=o=>a.filterType=o),placeholder:"配置类型",clearable:""},{default:n(()=>[e(d,{label:"全部",value:""}),e(d,{label:"链接跳转",value:1}),e(d,{label:"系统参数",value:2}),e(d,{label:"其他",value:3})]),_:1},8,["modelValue"]),e(T,{modelValue:a.filterStatus,"onUpdate:modelValue":l[1]||(l[1]=o=>a.filterStatus=o),placeholder:"状态",clearable:""},{default:n(()=>[e(d,{label:"全部",value:""}),e(d,{label:"启用",value:1}),e(d,{label:"禁用",value:0})]),_:1},8,["modelValue"]),e(f,{modelValue:a.searchKeyword,"onUpdate:modelValue":l[2]||(l[2]=o=>a.searchKeyword=o),placeholder:"搜索配置名称或键名",clearable:"",style:{width:"300px","margin-left":"10px"}},null,8,["modelValue"]),e(i,{type:"primary",onClick:a.handleSearch},{default:n(()=>l[13]||(l[13]=[c("搜索")])),_:1,__:[13]},8,["onClick"]),e(i,{onClick:a.resetSearch},{default:n(()=>l[14]||(l[14]=[c("重置")])),_:1,__:[14]},8,["onClick"])]),P((A(),E(w,{data:a.filteredTableData,border:"",style:{width:"100%"}},{default:n(()=>[e(m,{prop:"id",label:"ID",width:"70",align:"center"}),e(m,{prop:"configName",label:"配置名称","min-width":"150"}),e(m,{prop:"configKey",label:"配置键","min-width":"180"}),e(m,{prop:"configValue",label:"配置值","min-width":"220"},{default:n(({row:o})=>[b("div",oe,[b("span",te,x(o.configValue),1),a.isUrl(o.configValue)?(A(),E(i,{key:0,type:"primary",link:"",onClick:h=>a.openUrl(o.configValue)},{default:n(()=>l[15]||(l[15]=[c(" 访问 ")])),_:2,__:[15]},1032,["onClick"])):Q("",!0)])]),_:1}),e(m,{prop:"configTypeName",label:"配置类型",width:"100",align:"center"},{default:n(({row:o})=>[e(_,{type:a.getConfigTypeTag(o.configType)},{default:n(()=>[c(x(o.configType===1?"链接跳转":o.configType===2?"系统参数":"其他"),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"statusName",label:"状态",width:"80",align:"center"},{default:n(({row:o})=>[e(_,{type:o.status===1?"success":"danger"},{default:n(()=>[c(x(o.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"createTime",label:"创建时间",width:"180",align:"center"}),e(m,{fixed:"right",label:"操作",width:"240",align:"center"},{default:n(({row:o})=>[e(i,{type:"primary",link:"",onClick:h=>a.handleEdit(o)},{default:n(()=>l[16]||(l[16]=[c("编辑")])),_:2,__:[16]},1032,["onClick"]),e(i,{type:"primary",link:"",onClick:h=>a.handleToggleStatus(o)},{default:n(()=>[c(x(o.status===1?"禁用":"启用"),1)]),_:2},1032,["onClick"]),e(N,{title:"确定删除该配置吗？",onConfirm:h=>a.handleDelete(o.id)},{reference:n(()=>[e(i,{type:"danger",link:""},{default:n(()=>l[17]||(l[17]=[c("删除")])),_:1,__:[17]})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[q,a.loading]])]),_:1}),e(F,{title:a.dialogType==="add"?"新增系统配置":"编辑系统配置",modelValue:a.dialogVisible,"onUpdate:modelValue":l[10]||(l[10]=o=>a.dialogVisible=o),width:"600px","append-to-body":""},{footer:n(()=>[b("div",ne,[e(i,{onClick:l[9]||(l[9]=o=>a.dialogVisible=!1)},{default:n(()=>l[20]||(l[20]=[c("取消")])),_:1,__:[20]}),e(i,{type:"primary",onClick:a.handleSubmit},{default:n(()=>l[21]||(l[21]=[c("确定")])),_:1,__:[21]},8,["onClick"])])]),default:n(()=>[e(D,{ref:"configFormRef",model:a.configForm,rules:a.configRules,"label-width":"100px"},{default:n(()=>[e(p,{label:"配置名称",prop:"configName"},{default:n(()=>[e(f,{modelValue:a.configForm.configName,"onUpdate:modelValue":l[3]||(l[3]=o=>a.configForm.configName=o),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),e(p,{label:"配置键",prop:"configKey"},{default:n(()=>[e(f,{modelValue:a.configForm.configKey,"onUpdate:modelValue":l[4]||(l[4]=o=>a.configForm.configKey=o),placeholder:"请输入配置键",disabled:a.dialogType==="edit"},null,8,["modelValue","disabled"])]),_:1}),e(p,{label:"配置值",prop:"configValue"},{default:n(()=>[e(f,{modelValue:a.configForm.configValue,"onUpdate:modelValue":l[5]||(l[5]=o=>a.configForm.configValue=o),placeholder:"请输入配置值"},null,8,["modelValue"])]),_:1}),e(p,{label:"配置类型",prop:"configType"},{default:n(()=>[e(T,{modelValue:a.configForm.configType,"onUpdate:modelValue":l[6]||(l[6]=o=>a.configForm.configType=o),placeholder:"请选择配置类型"},{default:n(()=>[e(d,{label:"链接跳转",value:1}),e(d,{label:"系统参数",value:2}),e(d,{label:"其他",value:3})]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"状态",prop:"status"},{default:n(()=>[e(K,{modelValue:a.configForm.status,"onUpdate:modelValue":l[7]||(l[7]=o=>a.configForm.status=o)},{default:n(()=>[e(S,{label:1},{default:n(()=>l[18]||(l[18]=[c("启用")])),_:1,__:[18]}),e(S,{label:0},{default:n(()=>l[19]||(l[19]=[c("禁用")])),_:1,__:[19]})]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"备注",prop:"remark"},{default:n(()=>[e(f,{modelValue:a.configForm.remark,"onUpdate:modelValue":l[8]||(l[8]=o=>a.configForm.remark=o),type:"textarea",placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}const de=L($,[["render",re],["__scopeId","data-v-e4b5d58a"]]);export{de as default};

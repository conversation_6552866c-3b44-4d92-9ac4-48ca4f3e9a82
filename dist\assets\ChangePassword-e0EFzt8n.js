import{M as b}from"./admin-CZaj3CWx.js";import{_ as v,c as F,a as o,w as s,r as m,d as V,b as c,E as g,o as C,e as _,f as P}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const y={name:"ChangePassword",setup(){const i=V({oldPassword:"",newPassword:"",confirmPassword:""}),e=c(null),w=c(!1),u={oldPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"},{min:6,message:"密码长度不能小于6个字符",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能小于6个字符",trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,}$/,message:"密码必须包含大小写字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(d,a,l)=>{a===""?l(new Error("请再次输入新密码")):a!==i.newPassword?l(new Error("两次输入的密码不一致")):l()},trigger:"blur"}]},f=()=>{e.value&&e.value.validate(async d=>{var a,l;if(d){w.value=!0;try{await b(i),g.success("密码修改成功"),n()}catch(p){console.error("密码修改失败:",p),g.error(((l=(a=p.response)==null?void 0:a.data)==null?void 0:l.message)||"密码修改失败")}finally{w.value=!1}}})},n=()=>{e.value&&e.value.resetFields()};return{passwordForm:i,passwordFormRef:e,passwordRules:u,loading:w,handleSubmit:f,resetForm:n}}},x={class:"change-password-container"};function h(i,e,w,r,u,f){const n=m("el-input"),d=m("el-form-item"),a=m("el-button"),l=m("el-form"),p=m("el-card");return C(),F("div",x,[o(p,{class:"box-card"},{header:s(()=>e[3]||(e[3]=[P("div",{class:"card-header"},[P("span",null,"修改密码")],-1)])),default:s(()=>[o(l,{ref:"passwordFormRef",model:r.passwordForm,rules:r.passwordRules,"label-width":"120px",class:"password-form"},{default:s(()=>[o(d,{label:"当前密码",prop:"oldPassword"},{default:s(()=>[o(n,{modelValue:r.passwordForm.oldPassword,"onUpdate:modelValue":e[0]||(e[0]=t=>r.passwordForm.oldPassword=t),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),_:1}),o(d,{label:"新密码",prop:"newPassword"},{default:s(()=>[o(n,{modelValue:r.passwordForm.newPassword,"onUpdate:modelValue":e[1]||(e[1]=t=>r.passwordForm.newPassword=t),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(d,{label:"确认新密码",prop:"confirmPassword"},{default:s(()=>[o(n,{modelValue:r.passwordForm.confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=t=>r.passwordForm.confirmPassword=t),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(d,null,{default:s(()=>[o(a,{type:"primary",loading:r.loading,onClick:r.handleSubmit},{default:s(()=>e[4]||(e[4]=[_("修改密码")])),_:1,__:[4]},8,["loading","onClick"]),o(a,{onClick:r.resetForm},{default:s(()=>e[5]||(e[5]=[_("重置")])),_:1,__:[5]},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])]),_:1})])}const q=v(y,[["render",h],["__scopeId","data-v-e082ea52"]]);export{q as default};

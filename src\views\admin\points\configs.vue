<template>
  <div class="points-configs-container">
    <div class="page-header">
      <h2>积分配置管理</h2>
      <el-button type="primary" @click="openAddConfigDialog">添加配置</el-button>
    </div>
    
    <!-- 积分配置列表 -->
    <el-card shadow="hover" class="table-card">
      <el-table 
        :data="configsList" 
        border 
        stripe 
        v-loading="loading" 
        style="width: 100%"
        :fit="true"
        height="calc(100vh - 250px)"
        :resizable="false"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" fixed />
        <el-table-column prop="configName" label="配置名称" width="180" />
        <el-table-column prop="configKey" label="配置键" width="200" />
        <el-table-column prop="configValue" label="配置值" width="100" align="center">
          <template #default="scope">
            <span class="config-value">{{ scope.row.configValue }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="statusName" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="180" align="center" />
        <el-table-column prop="updateTime" label="更新时间" width="180" align="center" />
        <el-table-column label="操作" fixed="right" width="150" align="center">
          <template #default="scope">
            <el-button type="primary" size="small" @click="openEditConfigDialog(scope.row)">编辑</el-button>
            <el-button 
              :type="scope.row.status === 1 ? 'danger' : 'success'" 
              size="small" 
              @click="toggleConfigStatus(scope.row)"
            >
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 添加/编辑配置对话框 -->
    <el-dialog 
      v-model="configDialogVisible" 
      :title="isEdit ? '编辑积分配置' : '添加积分配置'" 
      width="500px"
    >
      <el-form :model="configForm" :rules="configRules" ref="configFormRef" label-width="100px">
        <el-form-item label="配置名称" prop="configName" v-if="!isEdit">
          <el-input v-model="configForm.configName" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置键" prop="configKey" v-if="!isEdit">
          <el-input v-model="configForm.configKey" placeholder="请输入配置键" />
        </el-form-item>
        <el-form-item label="配置值" prop="configValue">
          <el-input v-model="configForm.configValue" placeholder="请输入配置值" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="configForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="configForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitConfigForm" :loading="submitting">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getPointsConfigList, 
  addPointsConfig, 
  updatePointsConfig, 
  updatePointsConfigStatus 
} from '../../../api/points'

// 积分配置列表
const configsList = ref([])
const loading = ref(false)

// 添加/编辑配置相关
const configDialogVisible = ref(false)
const configFormRef = ref(null)
const isEdit = ref(false)
const submitting = ref(false)
const currentConfigId = ref(null)

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

const configForm = reactive({
  configName: '',
  configKey: '',
  configValue: '',
  status: 1,
  remark: ''
})

const configRules = {
  configName: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
  configKey: [{ required: true, message: '请输入配置键', trigger: 'blur' }],
  configValue: [{ required: true, message: '请输入配置值', trigger: 'blur' }]
}

// 获取所有积分配置
const fetchConfigsList = async () => {
  loading.value = true
  try {
    const response = await getPointsConfigList({
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    })
    
    if (response.code === 0 || response.code === 200) {
      if (Array.isArray(response.data)) {
        configsList.value = response.data
        pagination.total = response.data.length
      } else if (response.data && response.data.list) {
        configsList.value = response.data.list
        pagination.total = response.data.total || 0
        pagination.pageNum = response.data.pageNum || 1
        pagination.pageSize = response.data.pageSize || 10
      } else {
        configsList.value = []
        pagination.total = 0
      }
    } else {
      ElMessage.error(response.message || '获取积分配置列表失败')
    }
  } catch (error) {
    console.error('获取积分配置列表出错:', error)
    ElMessage.error('获取积分配置列表失败')
  } finally {
    loading.value = false
  }
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchConfigsList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.pageNum = val
  fetchConfigsList()
}

// 打开添加配置对话框
const openAddConfigDialog = () => {
  isEdit.value = false
  currentConfigId.value = null
  
  configForm.configName = ''
  configForm.configKey = ''
  configForm.configValue = ''
  configForm.status = 1
  configForm.remark = ''
  
  configDialogVisible.value = true
}

// 打开编辑配置对话框
const openEditConfigDialog = (row) => {
  isEdit.value = true
  currentConfigId.value = row.id
  
  configForm.configName = row.configName
  configForm.configKey = row.configKey
  configForm.configValue = row.configValue
  configForm.status = row.status
  configForm.remark = row.remark || ''
  
  configDialogVisible.value = true
}

// 提交配置表单
const submitConfigForm = async () => {
  if (!configFormRef.value) return
  
  await configFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      
      try {
        let response
        
        if (isEdit.value) {
          // 编辑配置
          response = await updatePointsConfig(currentConfigId.value, {
            configValue: configForm.configValue,
            status: configForm.status,
            remark: configForm.remark
          })
        } else {
          // 添加配置
          response = await addPointsConfig({
            configName: configForm.configName,
            configKey: configForm.configKey,
            configValue: configForm.configValue,
            status: configForm.status,
            remark: configForm.remark
          })
        }
        
        if (response.code === 0 || response.code === 200) {
          ElMessage.success(isEdit.value ? '更新成功' : '添加成功')
          configDialogVisible.value = false
          fetchConfigsList()
        } else {
          ElMessage.error(response.message || (isEdit.value ? '更新失败' : '添加失败'))
        }
      } catch (error) {
        console.error(isEdit.value ? '更新配置出错:' : '添加配置出错:', error)
        ElMessage.error((isEdit.value ? '更新' : '添加') + '配置失败，请稍后重试')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 切换配置状态
const toggleConfigStatus = async (row) => {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'
  
  try {
    const response = await updatePointsConfigStatus(row.id, newStatus)
    
    if (response.code === 0 || response.code === 200) {
      ElMessage.success(`${statusText}成功`)
      row.status = newStatus
      row.statusName = newStatus === 1 ? '启用' : '禁用'
    } else {
      ElMessage.error(response.message || `${statusText}失败`)
    }
  } catch (error) {
    console.error(`${statusText}配置出错:`, error)
    ElMessage.error(`${statusText}失败，请稍后重试`)
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchConfigsList()
})
</script>

<style scoped>
.points-configs-container {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.table-card {
  margin-bottom: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-card :deep(.el-card__body) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-card :deep(.el-table) {
  flex: 1;
}

.table-card :deep(.el-table__header th) {
  background-color: #f5f7fa;
  user-select: none;
}

.table-card :deep(.el-table__header-wrapper) {
  user-select: none;
}

.table-card :deep(.el-table__column-resize-proxy) {
  display: none !important;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.config-value {
  font-weight: bold;
  color: #409EFF;
}
</style> 
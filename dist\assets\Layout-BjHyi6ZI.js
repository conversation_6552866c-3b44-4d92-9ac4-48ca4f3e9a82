import{_ as j,g as r,h as G,b as H,c as J,a as e,w as l,r as d,u as K,o,f as t,i as f,j as k,k as a,l as O,m as y,n as Q,t as W,p as X,q as Y,s as p,v as Z,x as m,y as $,z as h,A as ee,B as le,C as ne,D as te,F as ae,G as se,H as ue,I as de,J as oe,K as ie,L as _e,M as re,e as C,N as fe,E as me}from"./index-DKz5EXvV.js";import{u as pe}from"./user-De-42Sfb.js";const xe={class:"admin-layout"},ce={class:"header-left"},ve={class:"header-right"},we={class:"avatar-container"},be={class:"username"},ge={__name:"Layout",setup(ke){const x=G(),B=r(()=>x.matched.filter(n=>n.meta&&n.meta.title).map(n=>n.meta.title)),E={setup(){return{breadcrumbs:B}},template:`
    <el-breadcrumb separator="/">
      <el-breadcrumb-item v-for="(title, index) in breadcrumbs" :key="index">
        {{ title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  `},c=K(),v=pe(),i=H(!1),N=r(()=>v.username||"管理员"),S=r(()=>localStorage.getItem("adminRole")==="2"),I=w=>!0,L=r(()=>x.path),M=()=>{i.value=!i.value},T=()=>{fe.confirm("确定要退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{v.clearUserInfo(),c.push("/login"),me({type:"success",message:"已退出登录"})}).catch(()=>{})},V=()=>{c.push("/admin/password")};return(w,n)=>{const s=d("el-icon"),u=d("el-menu-item"),_=d("el-sub-menu"),F=d("el-menu"),R=d("el-aside"),q=d("el-avatar"),b=d("el-dropdown-item"),z=d("el-dropdown-menu"),A=d("el-dropdown"),D=d("el-header"),P=d("router-view"),U=d("el-main"),g=d("el-container");return o(),J("div",xe,[e(g,{class:"layout-container"},{default:l(()=>[e(R,{width:"220px",class:"aside"},{default:l(()=>[n[25]||(n[25]=t("div",{class:"logo"},[t("h2",null,"脚本后台管理系统")],-1)),e(F,{"default-active":L.value,class:"el-menu-vertical","background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF",collapse:i.value,router:""},{default:l(()=>[e(u,{index:"/admin"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(O))]),_:1}),n[0]||(n[0]=t("span",null,"仪表盘",-1))]),_:1,__:[0]}),e(u,{index:"/admin/users"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(y))]),_:1}),n[1]||(n[1]=t("span",null,"用户管理",-1))]),_:1,__:[1]}),S.value?(o(),f(u,{key:0,index:"/admin/admins"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(Q))]),_:1}),n[2]||(n[2]=t("span",null,"管理员管理",-1))]),_:1,__:[2]})):k("",!0),e(u,{index:"/admin/cards"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(W))]),_:1}),n[3]||(n[3]=t("span",null,"卡密管理",-1))]),_:1,__:[3]}),e(u,{index:"/admin/activate"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(X))]),_:1}),n[4]||(n[4]=t("span",null,"激活卡密",-1))]),_:1,__:[4]}),e(u,{index:"/admin/statistics"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(Y))]),_:1}),n[5]||(n[5]=t("span",null,"统计分析",-1))]),_:1,__:[5]}),e(u,{index:"/admin/logs"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(p))]),_:1}),n[6]||(n[6]=t("span",null,"操作日志",-1))]),_:1,__:[6]}),e(u,{index:"/admin/announcements"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(Z))]),_:1}),n[7]||(n[7]=t("span",null,"公告管理",-1))]),_:1,__:[7]}),e(u,{index:"/admin/about-us"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(p))]),_:1}),n[8]||(n[8]=t("span",null,"关于我们管理",-1))]),_:1,__:[8]}),e(u,{index:"/admin/system/config"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(m))]),_:1}),n[9]||(n[9]=t("span",null,"系统配置管理",-1))]),_:1,__:[9]}),e(_,{index:"distribution"},{title:l(()=>[e(s,null,{default:l(()=>[e(a(h))]),_:1}),n[10]||(n[10]=t("span",null,"分销管理",-1))]),default:l(()=>[e(u,{index:"/admin/distribution/config"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(m))]),_:1}),n[11]||(n[11]=t("span",null,"分销配置",-1))]),_:1,__:[11]}),e(u,{index:"/admin/distribution/record"},{default:l(()=>[e(s,null,{default:l(()=>[e(a($))]),_:1}),n[12]||(n[12]=t("span",null,"分销记录",-1))]),_:1,__:[12]})]),_:1}),I("points")?(o(),f(_,{key:1,index:"points"},{title:l(()=>[e(s,null,{default:l(()=>[e(a(le))]),_:1}),n[13]||(n[13]=t("span",null,"积分管理",-1))]),default:l(()=>[e(u,{index:"/admin/points/users"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(y))]),_:1}),n[14]||(n[14]=t("span",null,"用户积分管理",-1))]),_:1,__:[14]}),e(u,{index:"/admin/points/configs"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(m))]),_:1}),n[15]||(n[15]=t("span",null,"积分配置管理",-1))]),_:1,__:[15]}),e(u,{index:"/admin/points/channels"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(ee))]),_:1}),n[16]||(n[16]=t("span",null,"积分渠道管理",-1))]),_:1,__:[16]})]),_:1})):k("",!0),e(_,{index:"withdrawal"},{title:l(()=>[e(s,null,{default:l(()=>[e(a(ae))]),_:1}),n[17]||(n[17]=t("span",null,"提现管理",-1))]),default:l(()=>[e(u,{index:"/admin/withdrawal/requests"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(p))]),_:1}),n[18]||(n[18]=t("span",null,"提现申请管理",-1))]),_:1,__:[18]}),e(u,{index:"/admin/withdrawal/records"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(ne))]),_:1}),n[19]||(n[19]=t("span",null,"提现记录管理",-1))]),_:1,__:[19]}),e(u,{index:"/admin/withdrawal/amounts"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(te))]),_:1}),n[20]||(n[20]=t("span",null,"可提现金额管理",-1))]),_:1,__:[20]}),e(u,{index:"/admin/withdrawal/config"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(m))]),_:1}),n[21]||(n[21]=t("span",null,"提现配置管理",-1))]),_:1,__:[21]})]),_:1}),e(_,{index:"/admin/promotion"},{title:l(()=>[e(s,null,{default:l(()=>[e(a(de))]),_:1}),n[22]||(n[22]=t("span",null,"推广管理",-1))]),default:l(()=>[e(u,{index:"/admin/promotions"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(se))]),_:1}),n[23]||(n[23]=t("span",null,"推广内容管理",-1))]),_:1,__:[23]}),e(u,{index:"/admin/promotion/rank"},{default:l(()=>[e(s,null,{default:l(()=>[e(a(ue))]),_:1}),n[24]||(n[24]=t("span",null,"推广排名配置",-1))]),_:1,__:[24]})]),_:1})]),_:1},8,["default-active","collapse"])]),_:1,__:[25]}),e(g,null,{default:l(()=>[e(D,{class:"header"},{default:l(()=>[t("div",ce,[e(s,{class:"collapse-btn",onClick:M},{default:l(()=>[i.value?(o(),f(a(ie),{key:1})):(o(),f(a(oe),{key:0}))]),_:1}),e(E)]),t("div",ve,[e(A,{trigger:"click"},{dropdown:l(()=>[e(z,null,{default:l(()=>[e(b,{onClick:V},{default:l(()=>n[26]||(n[26]=[C("修改密码")])),_:1,__:[26]}),e(b,{onClick:T},{default:l(()=>n[27]||(n[27]=[C("退出登录")])),_:1,__:[27]})]),_:1})]),default:l(()=>[t("div",we,[e(q,{size:30,icon:"el-icon-user"}),t("span",be,_e(N.value),1),e(s,null,{default:l(()=>[e(a(re))]),_:1})])]),_:1})])]),_:1}),e(U,{class:"main"},{default:l(()=>[e(P)]),_:1})]),_:1})]),_:1})])}}},Be=j(ge,[["__scopeId","data-v-7d272a46"]]);export{Be as default};

import{s as n}from"./request-YZgFExiB.js";function r(t){return n({url:"/admin/login",method:"post",data:t})}function u(t){return n({url:"/admin/password/update",method:"post",data:t})}function i(t){return n({url:"/admin/statistics",method:"get",params:t})}function s(t){return n({url:"/dashboard/statistics",method:"get",params:t})}function a(t){return n({url:"/admin/users",method:"post",data:t})}function d(t){return n({url:`/admin/users/${t}`,method:"get"})}function m(t){return n({url:`/admin/users/${t}`,method:"delete"})}function l(t){return n({url:`/admin/users/${t}/disable`,method:"put"})}function c(t){return n({url:`/admin/users/${t}/enable`,method:"put"})}function f(t){return n({url:"/cardkey/generate",method:"post",data:t})}function g(t){return n({url:"/cardkey/list",method:"post",data:t})}function p(t){return n({url:`/cardkey/${t}`,method:"delete"})}function h(t){return n({url:"/logs/list",method:"post",data:t})}function b(t){return n({url:`/logs/${t}`,method:"get"})}function $(t){return n({url:`/logs/clean/${t}`,method:"delete"})}function A(t){return n({url:"/admin",method:"post",data:t})}function C(t){return n({url:"/admin",method:"put",data:t})}function k(t){return n({url:"/admin/status",method:"put",data:t})}function L(t){return n({url:"/admin/list",method:"get",params:t})}function P(){return n({url:"/announcement",method:"get"})}function D(t){return n({url:"/announcement",method:"post",data:t})}function y(t){return n({url:"/announcement",method:"put",data:t})}function U(t){return n({url:`/announcement/${t}`,method:"delete"})}function K(t){return n({url:`/announcement/${t}/enable`,method:"put"})}function R(t){return n({url:`/announcement/${t}/disable`,method:"put"})}function v(){return n({url:"/promotion",method:"get"})}function w(t){return n({url:"/promotion",method:"post",data:t})}function S(t){return n({url:"/promotion",method:"put",data:t})}function x(t){return n({url:`/promotion/${t}`,method:"delete"})}function I(t){return n({url:`/promotion/${t}/enable`,method:"put"})}function O(t){return n({url:`/promotion/${t}/disable`,method:"put"})}function j(){return n({url:"/distribution/config/list",method:"get"})}function q(t,e){return n({url:`/distribution/config/${t}`,method:"put",data:e})}function z(t){return n({url:`/distribution/config/${t}/enable`,method:"put"})}function B(t){return n({url:`/distribution/config/${t}/disable`,method:"put"})}function E(t){return n({url:"/distribution/record/list",method:"post",data:t})}function F(){return n({url:"/admin/promotion/rank/config",method:"get"})}function G(t){return n({url:"/admin/promotion/rank/config",method:"post",data:t})}function H(t){return n({url:`/admin/promotion/rank/config/${t}`,method:"delete"})}export{O as A,x as B,w as C,S as D,F as E,H as F,G,j as H,z as I,B as J,q as K,E as L,u as M,a,d as b,m as c,l as d,c as e,L as f,s as g,C as h,A as i,g as j,p as k,r as l,f as m,i as n,h as o,b as p,$ as q,P as r,K as s,R as t,k as u,U as v,D as w,y as x,v as y,I as z};

import{d as $,c as q}from"./withdrawal-Ddpl8I2L.js";import{_ as Q,b as _,d as N,O as G,c as k,a,w as l,r as s,E as b,P as J,o as y,Q as x,f as p,e as u,i as D,L as c,j as K}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const X={class:"withdrawal-amounts"},Y={class:"pagination-container"},Z={key:0,class:"dialog-header"},ee={class:"dialog-footer"},ae={__name:"WithdrawalAmounts",setup(te){const S=_(!1),z=_(!1),g=_(!1),T=[{value:0,label:"待审核"},{value:1,label:"已通过"},{value:2,label:"已拒绝"},{value:3,label:"已打款"}],m=N({phone:""}),n=N({pageNum:1,pageSize:10,total:0}),h=_([]),i=N({userId:"",phone:"",availableAmount:0}),v=_([]),U=t=>{const e=T.find(d=>d.value===t);return e?e.label:"未知状态"},W=t=>{switch(t){case 0:return"warning";case 1:return"success";case 2:return"danger";case 3:return"primary";default:return"info"}},C=()=>{n.pageNum=1,f()},B=()=>{m.phone="",C()},F=t=>{n.pageSize=t,f()},L=t=>{n.pageNum=t,f()},f=async()=>{S.value=!0;try{const t={...m,pageNum:n.pageNum,pageSize:n.pageSize},e=await $(t);e.code===200&&e.data?(h.value=e.data.list||[],n.total=e.data.total||0,n.pageNum=e.data.pageNum||1,n.pageSize=e.data.pageSize||10):(b.error(e.message||"获取可提现金额列表失败"),h.value=[],n.total=0)}catch(t){console.error("获取可提现金额列表失败:",t),b.error("获取可提现金额列表失败"),h.value=[],n.total=0}finally{S.value=!1}},E=async t=>{i.userId=t.userId,i.phone=t.phone,i.availableAmount=t.availableAmount,g.value=!0,await H(t.userId)},H=async t=>{z.value=!0;try{const d=await q({userId:t});d.code===200&&d.data?v.value=d.data.list||[]:(b.error(d.message||"获取用户提现记录失败"),v.value=[])}catch(e){console.error("获取用户提现记录失败:",e),b.error("获取用户提现记录失败"),v.value=[]}finally{z.value=!1}};return G(()=>{f()}),(t,e)=>{const d=s("el-input"),I=s("el-form-item"),w=s("el-button"),j=s("el-form"),r=s("el-table-column"),A=s("el-table"),M=s("el-pagination"),O=s("el-card"),P=s("el-tag"),R=s("el-dialog"),V=J("loading");return y(),k("div",X,[a(O,{shadow:"hover"},{header:l(()=>e[5]||(e[5]=[p("div",{class:"card-header"},[p("span",null,"用户可提现金额管理")],-1)])),default:l(()=>[a(j,{inline:!0,model:m,class:"search-form"},{default:l(()=>[a(I,{label:"手机号"},{default:l(()=>[a(d,{modelValue:m.phone,"onUpdate:modelValue":e[0]||(e[0]=o=>m.phone=o),placeholder:"手机号",clearable:""},null,8,["modelValue"])]),_:1}),a(I,null,{default:l(()=>[a(w,{type:"primary",onClick:C},{default:l(()=>e[6]||(e[6]=[u("搜索")])),_:1,__:[6]}),a(w,{onClick:B},{default:l(()=>e[7]||(e[7]=[u("重置")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"]),x((y(),D(A,{data:h.value,border:"",style:{width:"100%"}},{default:l(()=>[a(r,{prop:"userId",label:"用户ID",width:"100"}),a(r,{prop:"phone",label:"手机号",width:"150"}),a(r,{prop:"availableAmount",label:"可提现金额",width:"150"},{default:l(o=>[u(c(o.row.availableAmount.toFixed(2))+" 元 ",1)]),_:1}),a(r,{prop:"registerTime",label:"注册时间",width:"180"}),a(r,{label:"操作",width:"120"},{default:l(o=>[a(w,{type:"primary",link:"",onClick:le=>E(o.row)},{default:l(()=>e[8]||(e[8]=[u(" 提现记录 ")])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[V,S.value]]),p("div",Y,[a(M,{"current-page":n.pageNum,"onUpdate:currentPage":e[1]||(e[1]=o=>n.pageNum=o),"page-size":n.pageSize,"onUpdate:pageSize":e[2]||(e[2]=o=>n.pageSize=o),"page-sizes":[10,20,50,100],total:n.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:F,onCurrentChange:L},null,8,["current-page","page-size","total"])])]),_:1}),a(R,{modelValue:g.value,"onUpdate:modelValue":e[4]||(e[4]=o=>g.value=o),title:"用户提现记录",width:"80%"},{footer:l(()=>[p("span",ee,[a(w,{onClick:e[3]||(e[3]=o=>g.value=!1)},{default:l(()=>e[9]||(e[9]=[u("关闭")])),_:1,__:[9]})])]),default:l(()=>[i.phone?(y(),k("div",Z,[p("h3",null,"用户: "+c(i.phone)+" (ID: "+c(i.userId)+")",1),p("p",null,"可提现金额: "+c(i.availableAmount?i.availableAmount.toFixed(2):"0.00")+" 元",1)])):K("",!0),x((y(),D(A,{data:v.value,border:"",style:{width:"100%"}},{default:l(()=>[a(r,{prop:"id",label:"记录ID",width:"80"}),a(r,{prop:"requestId",label:"申请ID",width:"80"}),a(r,{prop:"amount",label:"提现金额",width:"120"},{default:l(o=>[u(c(o.row.amount.toFixed(2))+" 元 ",1)]),_:1}),a(r,{prop:"alipayAccount",label:"支付宝账号",width:"180"}),a(r,{prop:"alipayName",label:"支付宝实名",width:"120"}),a(r,{prop:"status",label:"状态",width:"120"},{default:l(o=>[a(P,{type:W(o.row.status)},{default:l(()=>[u(c(U(o.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(r,{prop:"adminRemark",label:"管理员备注",width:"180"}),a(r,{prop:"transactionId",label:"交易单号",width:"180"}),a(r,{prop:"operationTime",label:"操作时间",width:"180"}),a(r,{prop:"createTime",label:"创建时间",width:"180"})]),_:1},8,["data"])),[[V,z.value]])]),_:1},8,["modelValue"])])}}},se=Q(ae,[["__scopeId","data-v-9ec61598"]]);export{se as default};

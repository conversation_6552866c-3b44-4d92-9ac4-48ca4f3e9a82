<template>
  <div class="announcement-management">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>公告管理</span>
          <el-button type="primary" @click="handleAddAnnouncement">添加公告</el-button>
        </div>
      </template>
      
      <el-table 
        :data="announcementList" 
        style="width: 100%" 
        v-loading="loading"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="200" />
        <el-table-column prop="qqGroup" label="QQ群链接" min-width="150" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              @click="handleViewAnnouncement(scope.row)"
            >查看</el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleEditAnnouncement(scope.row)"
            >编辑</el-button>
            <el-button 
              v-if="scope.row.status === 0"
              size="small" 
              type="success" 
              @click="handleEnableAnnouncement(scope.row)"
            >启用</el-button>
            <el-button 
              v-if="scope.row.status === 1"
              size="small" 
              type="warning" 
              @click="handleDisableAnnouncement(scope.row)"
            >禁用</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDeleteAnnouncement(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 公告详情对话框 -->
    <el-dialog
      v-model="dialogVisible.view"
      title="公告详情"
      width="600px"
    >
      <div class="announcement-detail">
        <h2>{{ currentAnnouncement.title }}</h2>
        <div class="announcement-content">{{ currentAnnouncement.content }}</div>
        <div class="announcement-info">
          <p><strong>QQ群链接:</strong> {{ currentAnnouncement.qqGroup }}</p>
          <p><strong>状态:</strong> {{ currentAnnouncement.status === 1 ? '启用' : '禁用' }}</p>
          <p><strong>创建时间:</strong> {{ currentAnnouncement.createTime }}</p>
          <p><strong>更新时间:</strong> {{ currentAnnouncement.updateTime }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 公告编辑对话框 -->
    <el-dialog
      v-model="dialogVisible.edit"
      :title="isAddingAnnouncement ? '添加公告' : '编辑公告'"
      width="600px"
    >
      <el-form 
        :model="announcementForm" 
        :rules="announcementRules" 
        ref="announcementFormRef"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="announcementForm.title" placeholder="请输入公告标题" />
        </el-form-item>
        
        <el-form-item label="内容" prop="content">
          <el-input 
            v-model="announcementForm.content" 
            type="textarea" 
            :rows="6" 
            placeholder="请输入公告内容"
          />
        </el-form-item>
        
        <el-form-item label="QQ群链接" prop="qqGroup">
          <el-input v-model="announcementForm.qqGroup" placeholder="请输入QQ群链接" />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="announcementForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible.edit = false">取消</el-button>
          <el-button type="primary" @click="submitAnnouncementForm" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getAnnouncementList,
  getAnnouncementDetail,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
  enableAnnouncement,
  disableAnnouncement
} from '../../api/admin'

// 公告列表
const announcementList = ref([])

// 加载状态
const loading = ref(false)

// 提交状态
const submitting = ref(false)

// 对话框显示状态
const dialogVisible = reactive({
  view: false,
  edit: false
})

// 当前操作的公告
const currentAnnouncement = reactive({
  id: null,
  title: '',
  content: '',
  qqGroup: '',
  status: 1,
  createTime: '',
  updateTime: ''
})

// 公告表单
const announcementForm = reactive({
  id: null,
  title: '',
  content: '',
  qqGroup: '',
  status: 1
})

// 公告表单引用
const announcementFormRef = ref(null)

// 是否为添加公告
const isAddingAnnouncement = ref(true)

// 公告表单验证规则
const announcementRules = {
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' }
  ]
}

// 获取公告列表
const fetchAnnouncementList = async () => {
  loading.value = true
  try {
    const res = await getAnnouncementList()
    announcementList.value = res.data || []
  } catch (error) {
    console.error('获取公告列表失败:', error)
    ElMessage.error('获取公告列表失败')
  } finally {
    loading.value = false
  }
}

// 查看公告详情
const handleViewAnnouncement = (row) => {
  Object.assign(currentAnnouncement, row)
  dialogVisible.view = true
}

// 添加公告
const handleAddAnnouncement = () => {
  isAddingAnnouncement.value = true
  resetAnnouncementForm()
  dialogVisible.edit = true
}

// 编辑公告
const handleEditAnnouncement = (row) => {
  isAddingAnnouncement.value = false
  resetAnnouncementForm()
  Object.assign(announcementForm, row)
  dialogVisible.edit = true
}

// 启用公告
const handleEnableAnnouncement = async (row) => {
  try {
    await enableAnnouncement(row.id)
    ElMessage.success('启用公告成功')
    fetchAnnouncementList()
  } catch (error) {
    console.error('启用公告失败:', error)
    ElMessage.error('启用公告失败')
  }
}

// 禁用公告
const handleDisableAnnouncement = async (row) => {
  try {
    await disableAnnouncement(row.id)
    ElMessage.success('禁用公告成功')
    fetchAnnouncementList()
  } catch (error) {
    console.error('禁用公告失败:', error)
    ElMessage.error('禁用公告失败')
  }
}

// 删除公告
const handleDeleteAnnouncement = (row) => {
  ElMessageBox.confirm('确定要删除该公告吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteAnnouncement(row.id)
      ElMessage.success('删除公告成功')
      fetchAnnouncementList()
    } catch (error) {
      console.error('删除公告失败:', error)
      ElMessage.error('删除公告失败')
    }
  }).catch(() => {})
}

// 重置公告表单
const resetAnnouncementForm = () => {
  if (announcementFormRef.value) {
    announcementFormRef.value.resetFields()
  }
  
  announcementForm.id = null
  announcementForm.title = ''
  announcementForm.content = ''
  announcementForm.qqGroup = ''
  announcementForm.status = 1
}

// 提交公告表单
const submitAnnouncementForm = async () => {
  if (!announcementFormRef.value) return
  
  await announcementFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (isAddingAnnouncement.value) {
          // 创建公告
          await createAnnouncement(announcementForm)
          ElMessage.success('创建公告成功')
        } else {
          // 更新公告
          await updateAnnouncement(announcementForm)
          ElMessage.success('更新公告成功')
        }
        
        dialogVisible.edit = false
        fetchAnnouncementList()
      } catch (error) {
        console.error('提交公告失败:', error)
        ElMessage.error('提交公告失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

onMounted(() => {
  fetchAnnouncementList()
})
</script>

<style scoped>
.announcement-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.announcement-detail h2 {
  margin-top: 0;
  margin-bottom: 20px;
}

.announcement-content {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 5px;
  white-space: pre-wrap;
}

.announcement-info p {
  margin: 8px 0;
}
</style>
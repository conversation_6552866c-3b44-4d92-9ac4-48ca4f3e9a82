<template>
  <div class="login-container">
    <el-card class="login-card">
      <template #header>
        <div class="card-header">
          <h2>卡密管理系统</h2>
        </div>
      </template>
      
      <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" label-width="0">
        <el-form-item prop="username">
          <el-input 
            v-model="loginForm.username" 
            placeholder="管理员用户名" 
            prefix-icon="el-icon-user"
          ></el-input>
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="密码" 
            prefix-icon="el-icon-lock"
            show-password
          ></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleLogin" style="width: 100%">管理员登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { login as adminLogin } from '../api/admin'
import { useUserStore } from '../stores/user'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const loginFormRef = ref(null)
    const loading = ref(false)
    const userStore = useUserStore()
    
    // 登录表单
    const loginForm = reactive({
      username: '',
      password: '',
    })
    
    // 表单验证规则
    const loginRules = {
      username: [
        { required: true, message: '请输入管理员用户名', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
      ]
    }
    
    // 处理登录
    const handleLogin = async () => {
      if (!loginFormRef.value) return
      
      await loginFormRef.value.validate(async (valid) => {
        if (valid) {
          loading.value = true
          try {
            const loginData = {
              username: loginForm.username,
              password: loginForm.password
            }
            
            // 管理员登录
            const res = await adminLogin(loginData)
            console.log('管理员登录响应数据:', res)
            
            try {
              if (res.code === 200 && res.data && res.data.token) {
                // 标准响应结构
                userStore.setUserInfo({
                  id: res.data.id || res.data.adminId || null,
                  username: res.data.username,
                  token: res.data.token,
                  role: 'admin'
                })
                
                console.log('准备跳转到管理员页面')
                router.push('/admin')
                ElMessage.success('登录成功')
              } else if (res.data && res.data.token) {
                // 备用响应结构1
                userStore.setUserInfo({
                  id: res.data.id || res.data.adminId || null,
                  username: res.data.username || loginForm.username,
                  token: res.data.token,
                  role: 'admin'
                })
                
                console.log('准备跳转到管理员页面')
                router.push('/admin')
                ElMessage.success('登录成功')
              } else if (res.token) {
                // 备用响应结构2
                userStore.setUserInfo({
                  id: res.id || res.adminId || null,
                  username: res.username || loginForm.username,
                  token: res.token,
                  role: 'admin'
                })
                
                console.log('准备跳转到管理员页面')
                router.push('/admin')
                ElMessage.success('登录成功')
              } else {
                console.error('响应中没有找到token:', res)
                ElMessage.error('登录失败: 未获取到授权信息')
              }
            } catch (err) {
              console.error('存储管理员信息失败:', err)
              ElMessage.error('登录数据处理失败')
            }
          } catch (error) {
            console.error('登录失败:', error)
            ElMessage.error('登录失败，请检查用户名和密码')
          } finally {
            loading.value = false
          }
        }
      })
    }
    
    return {
      loginFormRef,
      loginForm,
      loginRules,
      loading,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f0f2f5;
  padding: 20px;
}

.login-card {
  width: 400px;
  margin-bottom: 20px;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0;
  font-size: 24px;
}
</style>
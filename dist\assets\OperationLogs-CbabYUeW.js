import{o as G,p as K,q as W}from"./admin-CZaj3CWx.js";import{_ as X,d as C,b as v,g as Z,O as ee,c as O,f as a,a as l,w as o,r,E as c,P as le,o as z,e as _,Q as ae,i as te,L as u,j as oe}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const se={class:"operation-logs-container"},ne={class:"pagination"},re={class:"dialog-footer"},ie={key:0,class:"log-detail"},de={class:"detail-item"},ue={class:"detail-item"},pe={class:"detail-item"},me={class:"detail-item"},ge={class:"detail-item"},ce={class:"detail-item"},ve={class:"detail-item"},_e={class:"detail-item"},fe={class:"detail-item"},be={__name:"OperationLogs",setup(ye){const U=[{text:"最近一周",value:()=>{const t=new Date,e=new Date;return e.setTime(e.getTime()-6048e5),[e,t]}},{text:"最近一个月",value:()=>{const t=new Date,e=new Date;return e.setTime(e.getTime()-2592e6),[e,t]}},{text:"最近三个月",value:()=>{const t=new Date,e=new Date;return e.setTime(e.getTime()-7776e6),[e,t]}}],i=C({operationType:"",username:"",startTime:"",endTime:"",pageNum:1,pageSize:10}),m=v([]);Z(()=>(m.value&&m.value.length===2?(i.startTime=m.value[0],i.endTime=m.value[1]):(i.startTime="",i.endTime=""),m.value));const n=C({pageNum:1,pageSize:10,total:0}),x=v([]),D=v(!1),w=v(!1),f=C({days:30}),T=v(!1),S=v(!1),d=v(null),N=t=>{if(!t.result)return!1;try{const e=typeof t.result=="string"?JSON.parse(t.result):t.result;return e&&e.code===200}catch{return!1}},b=async()=>{D.value=!0;try{const t={...i,pageNum:n.pageNum,pageSize:n.pageSize},e=await G(t);e.code===200?(x.value=e.data.list||[],n.total=e.data.total||0,n.pageNum=e.data.pageNum||1,n.pageSize=e.data.pageSize||10):c.error(e.message||"获取日志列表失败")}catch(t){console.error("获取日志列表出错",t),c.error("获取日志列表出错")}finally{D.value=!1}},I=()=>{n.pageNum=1,b()},j=()=>{Object.keys(i).forEach(t=>{i[t]=""}),m.value=[],n.pageNum=1,n.pageSize=10,b()},B=()=>{w.value=!0},E=async()=>{if(!f.days||f.days<=0){c.warning("请输入有效的天数");return}try{T.value=!0;const t=await W(f.days);t.code===200?(c.success(`成功清理了${t.data}条日志记录`),w.value=!1,b()):c.error(t.message||"清理日志失败")}catch(t){console.error("清理日志出错",t),c.error("清理日志出错")}finally{T.value=!1}},M=async t=>{try{const e=await K(t);e.code===200?(d.value=e.data,S.value=!0):c.error(e.message||"获取日志详情失败")}catch(e){console.error("获取日志详情出错",e),c.error("获取日志详情出错")}},P=t=>{n.pageSize=t,b()},Y=t=>{n.pageNum=t,b()};return ee(()=>{b()}),(t,e)=>{const p=r("el-option"),$=r("el-select"),V=r("el-form-item"),F=r("el-input"),H=r("el-date-picker"),y=r("el-button"),k=r("el-form"),L=r("el-card"),g=r("el-table-column"),R=r("el-tag"),q=r("el-table"),J=r("el-pagination"),Q=r("el-input-number"),h=r("el-dialog"),A=le("loading");return z(),O("div",se,[e[25]||(e[25]=a("div",{class:"page-header"},[a("h2",null,"操作日志"),a("p",null,"查看系统所有操作记录")],-1)),l(L,{class:"search-card"},{default:o(()=>[l(k,{model:i,"label-width":"100px",inline:!0},{default:o(()=>[l(V,{label:"操作类型"},{default:o(()=>[l($,{modelValue:i.operationType,"onUpdate:modelValue":e[0]||(e[0]=s=>i.operationType=s),placeholder:"全部",clearable:""},{default:o(()=>[l(p,{label:"登录",value:"登录"}),l(p,{label:"修改密码",value:"修改密码"}),l(p,{label:"生成卡密",value:"生成卡密"}),l(p,{label:"激活卡密",value:"激活卡密"}),l(p,{label:"查询",value:"查询"}),l(p,{label:"删除",value:"删除"}),l(p,{label:"禁用",value:"禁用"}),l(p,{label:"启用",value:"启用"}),l(p,{label:"清理",value:"清理"})]),_:1},8,["modelValue"])]),_:1}),l(V,{label:"操作人"},{default:o(()=>[l(F,{modelValue:i.username,"onUpdate:modelValue":e[1]||(e[1]=s=>i.username=s),placeholder:"请输入操作人名称",clearable:""},null,8,["modelValue"])]),_:1}),l(V,{label:"操作时间"},{default:o(()=>[l(H,{modelValue:m.value,"onUpdate:modelValue":e[2]||(e[2]=s=>m.value=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD HH:mm:ss",shortcuts:U},null,8,["modelValue"])]),_:1}),l(V,null,{default:o(()=>[l(y,{type:"primary",onClick:I},{default:o(()=>e[9]||(e[9]=[_("查询")])),_:1,__:[9]}),l(y,{onClick:j},{default:o(()=>e[10]||(e[10]=[_("重置")])),_:1,__:[10]}),l(y,{type:"danger",onClick:B},{default:o(()=>e[11]||(e[11]=[_("清理日志")])),_:1,__:[11]})]),_:1})]),_:1},8,["model"])]),_:1}),l(L,{class:"table-card"},{default:o(()=>[ae((z(),te(q,{data:x.value,border:"",stripe:"",style:{width:"100%","margin-top":"10px"}},{default:o(()=>[l(g,{prop:"id",label:"ID",width:"80"}),l(g,{prop:"operationType",label:"操作类型",width:"120"}),l(g,{prop:"description",label:"操作描述","show-overflow-tooltip":""}),l(g,{prop:"username",label:"操作人",width:"120"}),l(g,{prop:"ip",label:"IP地址",width:"140"}),l(g,{prop:"createTime",label:"操作时间",width:"180"}),l(g,{prop:"result",label:"状态",width:"100"},{default:o(s=>[l(R,{type:N(s.row)?"success":"danger"},{default:o(()=>[_(u(N(s.row)?"成功":"失败"),1)]),_:2},1032,["type"])]),_:1}),l(g,{label:"操作",width:"100",fixed:"right"},{default:o(s=>[l(y,{type:"primary",link:"",onClick:we=>M(s.row.id)},{default:o(()=>e[12]||(e[12]=[_(" 详情 ")])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[A,D.value]]),a("div",ne,[l(J,{"current-page":n.pageNum,"onUpdate:currentPage":e[3]||(e[3]=s=>n.pageNum=s),"page-size":n.pageSize,"onUpdate:pageSize":e[4]||(e[4]=s=>n.pageSize=s),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:n.total,onSizeChange:P,onCurrentChange:Y},null,8,["current-page","page-size","total"])])]),_:1}),l(h,{modelValue:w.value,"onUpdate:modelValue":e[7]||(e[7]=s=>w.value=s),title:"清理日志",width:"400px"},{footer:o(()=>[a("span",re,[l(y,{onClick:e[6]||(e[6]=s=>w.value=!1)},{default:o(()=>e[14]||(e[14]=[_("取消")])),_:1,__:[14]}),l(y,{type:"danger",onClick:E,loading:T.value},{default:o(()=>e[15]||(e[15]=[_("确认清理")])),_:1,__:[15]},8,["loading"])])]),default:o(()=>[l(k,{model:f,"label-width":"120px"},{default:o(()=>[l(V,{label:"清理日期范围"},{default:o(()=>[l(Q,{modelValue:f.days,"onUpdate:modelValue":e[5]||(e[5]=s=>f.days=s),min:1,max:365,placeholder:"输入天数"},null,8,["modelValue"]),e[13]||(e[13]=a("span",{class:"days-hint"},"天前的日志",-1))]),_:1,__:[13]})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(h,{modelValue:S.value,"onUpdate:modelValue":e[8]||(e[8]=s=>S.value=s),title:"日志详情",width:"600px"},{default:o(()=>[d.value?(z(),O("div",ie,[a("div",de,[e[16]||(e[16]=a("span",{class:"label"},"ID:",-1)),a("span",null,u(d.value.id),1)]),a("div",ue,[e[17]||(e[17]=a("span",{class:"label"},"操作类型:",-1)),a("span",null,u(d.value.operationType),1)]),a("div",pe,[e[18]||(e[18]=a("span",{class:"label"},"操作描述:",-1)),a("span",null,u(d.value.description),1)]),a("div",me,[e[19]||(e[19]=a("span",{class:"label"},"操作人:",-1)),a("span",null,u(d.value.username),1)]),a("div",ge,[e[20]||(e[20]=a("span",{class:"label"},"操作方法:",-1)),a("span",null,u(d.value.method),1)]),a("div",ce,[e[21]||(e[21]=a("span",{class:"label"},"参数:",-1)),a("pre",null,u(d.value.params),1)]),a("div",ve,[e[22]||(e[22]=a("span",{class:"label"},"结果:",-1)),a("span",null,u(d.value.result),1)]),a("div",_e,[e[23]||(e[23]=a("span",{class:"label"},"IP地址:",-1)),a("span",null,u(d.value.ip),1)]),a("div",fe,[e[24]||(e[24]=a("span",{class:"label"},"操作时间:",-1)),a("span",null,u(d.value.createTime),1)])])):oe("",!0)]),_:1},8,["modelValue"])])}}},Se=X(be,[["__scopeId","data-v-c44e01de"]]);export{Se as default};

import{f as K,u as W,h as X,i as Y}from"./admin-CZaj3CWx.js";import{_ as Z,b as g,g as ee,d as N,O as ae,c as te,a as t,w as r,r as s,E as c,P as le,o as w,Q as oe,f as v,i as k,e as u,L as x,j as T,N as re}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const se={class:"admin-management"},ne={class:"card-header"},ie={class:"pagination-container"},de={class:"dialog-footer"},ue={__name:"AdminManagement",setup(me){const b=g(!1),y=g(!1),m=g(!1),p=g(!1),V=g(null),$=localStorage.getItem("adminId"),B=localStorage.getItem("adminRole"),I=ee(()=>B==="2"),h=o=>o.toString()===$,n=N({pageNum:1,pageSize:10,total:0}),S=g([]),l=N({id:"",username:"",email:"",password:"",role:1}),L={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6个字符",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}]},_=async()=>{b.value=!0;try{const o={pageNum:n.pageNum,pageSize:n.pageSize},e=await K(o);S.value=e.data||[],n.total=e.total||0}catch(o){console.error("获取管理员列表失败:",o),c.error("获取管理员列表失败"),S.value=[{id:1,username:"admin",email:"<EMAIL>",role:2,status:1,createTime:"2023-06-01 12:00:00",lastLoginTime:"2023-06-10 12:00:00"},{id:2,username:"manager",email:"<EMAIL>",role:1,status:1,createTime:"2023-06-02 12:00:00",lastLoginTime:"2023-06-09 12:00:00"}],n.total=2}finally{b.value=!1}},U=o=>{n.pageSize=o,_()},E=o=>{n.pageNum=o,_()},M=()=>{p.value=!1,l.id="",l.username="",l.email="",l.password="",l.role=1,m.value=!0},R=o=>{p.value=!0,l.id=o.id,l.username=o.username,l.email=o.email,l.password="",l.role=o.role,m.value=!0},q=async()=>{V.value&&await V.value.validate(async o=>{if(o){y.value=!0;try{p.value?(await X({id:l.id,email:l.email,role:l.role}),c.success("管理员信息更新成功")):(await Y(l),c.success("管理员添加成功")),m.value=!1,_()}catch(e){console.error("操作失败:",e),c.error("操作失败")}finally{y.value=!1}}})},D=o=>{const e=o.status===1?"禁用":"启用";re.confirm(`确定要${e}管理员 ${o.username} 吗?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await W({id:o.id,status:o.status===1?0:1}),c.success(`${e}管理员成功`),_()}catch(d){console.error(`${e}管理员失败:`,d),c.error(`${e}管理员失败`)}}).catch(()=>{})};return ae(()=>{_()}),(o,e)=>{const d=s("el-button"),i=s("el-table-column"),z=s("el-tag"),F=s("el-table"),j=s("el-pagination"),P=s("el-card"),C=s("el-input"),f=s("el-form-item"),A=s("el-option"),O=s("el-select"),Q=s("el-form"),G=s("el-dialog"),H=le("loading");return w(),te("div",se,[t(P,{shadow:"hover"},{header:r(()=>[v("div",ne,[e[9]||(e[9]=v("span",null,"管理员管理",-1)),t(d,{type:"primary",onClick:M},{default:r(()=>e[8]||(e[8]=[u("添加管理员")])),_:1,__:[8]})])]),default:r(()=>[oe((w(),k(F,{data:S.value,border:"",style:{width:"100%"}},{default:r(()=>[t(i,{prop:"id",label:"ID",width:"80"}),t(i,{prop:"username",label:"用户名",width:"150"}),t(i,{prop:"email",label:"邮箱",width:"200"}),t(i,{prop:"role",label:"角色",width:"120"},{default:r(a=>[t(z,{type:a.row.role===2?"danger":"primary"},{default:r(()=>[u(x(a.row.role===2?"超级管理员":"普通管理员"),1)]),_:2},1032,["type"])]),_:1}),t(i,{prop:"status",label:"状态",width:"100"},{default:r(a=>[t(z,{type:a.row.status===1?"success":"danger"},{default:r(()=>[u(x(a.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(i,{prop:"createTime",label:"创建时间",width:"180"}),t(i,{prop:"lastLoginTime",label:"最后登录时间",width:"180"}),t(i,{label:"操作",width:"200"},{default:r(a=>[t(d,{type:a.row.status===1?"danger":"success",link:"",onClick:J=>D(a.row),disabled:a.row.role===2&&h(a.row.id)},{default:r(()=>[u(x(a.row.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick","disabled"]),t(d,{type:"primary",link:"",onClick:J=>R(a.row),disabled:a.row.role===2&&!h(a.row.id)},{default:r(()=>e[10]||(e[10]=[u(" 编辑 ")])),_:2,__:[10]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[H,b.value]]),v("div",ie,[t(j,{"current-page":n.pageNum,"onUpdate:currentPage":e[0]||(e[0]=a=>n.pageNum=a),"page-size":n.pageSize,"onUpdate:pageSize":e[1]||(e[1]=a=>n.pageSize=a),"page-sizes":[10,20,50,100],total:n.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:U,onCurrentChange:E},null,8,["current-page","page-size","total"])])]),_:1}),t(G,{modelValue:m.value,"onUpdate:modelValue":e[7]||(e[7]=a=>m.value=a),title:p.value?"编辑管理员":"添加管理员",width:"500px"},{footer:r(()=>[v("span",de,[t(d,{onClick:e[6]||(e[6]=a=>m.value=!1)},{default:r(()=>e[11]||(e[11]=[u("取消")])),_:1,__:[11]}),t(d,{type:"primary",onClick:q,loading:y.value},{default:r(()=>e[12]||(e[12]=[u("确定")])),_:1,__:[12]},8,["loading"])])]),default:r(()=>[t(Q,{ref_key:"adminFormRef",ref:V,model:l,rules:L,"label-width":"100px"},{default:r(()=>[t(f,{label:"用户名",prop:"username"},{default:r(()=>[t(C,{modelValue:l.username,"onUpdate:modelValue":e[2]||(e[2]=a=>l.username=a),disabled:p.value},null,8,["modelValue","disabled"])]),_:1}),t(f,{label:"邮箱",prop:"email"},{default:r(()=>[t(C,{modelValue:l.email,"onUpdate:modelValue":e[3]||(e[3]=a=>l.email=a)},null,8,["modelValue"])]),_:1}),p.value?T("",!0):(w(),k(f,{key:0,label:"密码",prop:"password"},{default:r(()=>[t(C,{modelValue:l.password,"onUpdate:modelValue":e[4]||(e[4]=a=>l.password=a),type:"password","show-password":""},null,8,["modelValue"])]),_:1})),I.value?(w(),k(f,{key:1,label:"角色",prop:"role"},{default:r(()=>[t(O,{modelValue:l.role,"onUpdate:modelValue":e[5]||(e[5]=a=>l.role=a)},{default:r(()=>[t(A,{label:"普通管理员",value:1}),t(A,{label:"超级管理员",value:2})]),_:1},8,["modelValue"])]),_:1})):T("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},_e=Z(ue,[["__scopeId","data-v-415cf00c"]]);export{_e as default};

<template>
  <div class="promotion-rank">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>推广排名</span>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      
      <div v-else>
        <!-- 活动信息 -->
        <div v-if="hasActivity" class="activity-info">
          <el-alert
            title="推广活动"
            type="success"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="activity-details">
                <p><strong>活动时间：</strong>{{ rankData.startTime }} 至 {{ rankData.endTime }}</p>
                <p><strong>活动说明：</strong>{{ rankData.description }}</p>
              </div>
            </template>
          </el-alert>
        </div>
        
        <!-- 排名列表 -->
        <div class="rank-list-container">
          <h3>推广排名榜 <small>(前{{ rankData.rankList.length }}名)</small></h3>
          
          <el-table
            :data="rankData.rankList"
            style="width: 100%"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          >
            <el-table-column type="index" label="排名" width="80" align="center">
              <template #default="scope">
                <div class="rank-number" :class="getRankClass(scope.$index + 1)">
                  {{ scope.$index + 1 }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="phoneMasked" label="用户" min-width="120" />
            <el-table-column prop="invitationCode" label="邀请码" min-width="100" />
            <el-table-column prop="totalReferrals" label="推广总人数" min-width="100" align="center" sortable />
            <el-table-column label="推广详情" min-width="180">
              <template #default="scope">
                <div class="referral-details">
                  <el-tag size="small">一级: {{ scope.row.level1Count }}</el-tag>
                  <el-tag size="small" type="success">二级: {{ scope.row.level2Count }}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="registerTime" label="注册时间" min-width="180" />
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="30"
              :page-size="30"
              :disabled="true"
            />
          </div>
        </div>
        
        <!-- 我的排名 -->
        <div class="my-rank-container">
          <h3>我的推广情况</h3>
          <div class="my-rank-card">
            <div class="rank-info">
              <div class="rank-item">
                <div class="rank-label">我的排名</div>
                <div class="rank-value">{{ myRank > 0 ? myRank : '未上榜' }}</div>
              </div>
              <div class="rank-item">
                <div class="rank-label">推广总人数</div>
                <div class="rank-value">{{ myTotalReferrals }}</div>
              </div>
              <div class="rank-item">
                <div class="rank-label">一级推广</div>
                <div class="rank-value">{{ myLevel1Count }}</div>
              </div>
              <div class="rank-item">
                <div class="rank-label">二级推广</div>
                <div class="rank-value">{{ myLevel2Count }}</div>
              </div>
            </div>
            <div class="rank-tips">
              <p>邀请更多用户注册，提升您的排名！</p>
              <el-button type="primary" @click="handleShare">分享邀请码</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 分享对话框 -->
    <el-dialog
      v-model="shareDialogVisible"
      title="分享邀请码"
      width="400px"
    >
      <div class="share-content">
        <p>您的邀请码：</p>
        <div class="invitation-code">
          <span>{{ invitationCode }}</span>
          <el-button type="primary" size="small" @click="copyInvitationCode">
            复制
          </el-button>
        </div>
        <p class="share-tip">分享给好友，邀请注册后可获得推广奖励！</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getPromotionRanking } from '../../api/user'
import { useUserStore } from '../../stores/user'

// 加载状态
const loading = ref(true)

// store实例
const userStore = useUserStore()

// 排名数据
const rankData = reactive({
  startTime: '',
  endTime: '',
  description: '',
  rankList: []
})

// 是否有活动
const hasActivity = computed(() => rankData.startTime && rankData.endTime)

// 我的排名信息
const myRank = ref(0)
const myTotalReferrals = ref(0)
const myLevel1Count = ref(0)
const myLevel2Count = ref(0)
const invitationCode = ref('')

// 分享对话框
const shareDialogVisible = ref(false)

// 获取排名数据
const fetchRankData = async () => {
  loading.value = true
  try {
    const res = await getPromotionRanking()
    if (res.data) {
      // 更新排名数据
      rankData.startTime = res.data.startTime || ''
      rankData.endTime = res.data.endTime || ''
      rankData.description = res.data.description || ''
      rankData.rankList = res.data.rankList || []
      
      // 查找我的排名
      const userId = userStore.id
      if (userId) {
        const myRankInfo = rankData.rankList.find(item => item.userId === userId)
        if (myRankInfo) {
          myRank.value = rankData.rankList.findIndex(item => item.userId === userId) + 1
          myTotalReferrals.value = myRankInfo.totalReferrals
          myLevel1Count.value = myRankInfo.level1Count
          myLevel2Count.value = myRankInfo.level2Count
          invitationCode.value = myRankInfo.invitationCode || userStore.invitationCode
        }
      }
    }
  } catch (error) {
    console.error('获取推广排名数据失败:', error)
    ElMessage.error('获取推广排名数据失败')
  } finally {
    loading.value = false
  }
}

// 获取排名样式
const getRankClass = (rank) => {
  if (rank === 1) return 'rank-first'
  if (rank === 2) return 'rank-second'
  if (rank === 3) return 'rank-third'
  return ''
}

// 分享邀请码
const handleShare = () => {
  shareDialogVisible.value = true
}

// 复制邀请码
const copyInvitationCode = () => {
  const textArea = document.createElement('textarea')
  textArea.value = invitationCode.value
  document.body.appendChild(textArea)
  textArea.select()
  document.execCommand('copy')
  document.body.removeChild(textArea)
  ElMessage.success('邀请码已复制到剪贴板')
}

// 初始化
onMounted(() => {
  fetchRankData()
})
</script>

<style scoped>
.promotion-rank {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container {
  padding: 20px;
}

.activity-info {
  margin-bottom: 20px;
}

.activity-details p {
  margin: 5px 0;
}

.rank-list-container {
  margin-bottom: 30px;
}

.rank-list-container h3,
.my-rank-container h3 {
  font-size: 18px;
  color: #303133;
  margin-bottom: 15px;
}

.rank-number {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
  margin: 0 auto;
  font-weight: bold;
}

.rank-first {
  background-color: #f7ba2a;
  color: white;
}

.rank-second {
  background-color: #a0a0a0;
  color: white;
}

.rank-third {
  background-color: #cd853f;
  color: white;
}

.referral-details {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.my-rank-container {
  margin-top: 30px;
}

.my-rank-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  background-color: #f8f9fa;
}

.rank-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.rank-item {
  flex: 1;
  min-width: 120px;
  text-align: center;
  padding: 15px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.rank-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.rank-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.rank-tips {
  text-align: center;
  margin-top: 20px;
}

.rank-tips p {
  margin-bottom: 15px;
  color: #606266;
}

.share-content {
  text-align: center;
}

.invitation-code {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin: 20px 0;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-size: 20px;
  font-weight: bold;
}

.share-tip {
  color: #909399;
  font-size: 14px;
  margin-top: 20px;
}
</style> 
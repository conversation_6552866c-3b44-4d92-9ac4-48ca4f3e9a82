<template>
  <div class="admin-layout">
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside width="220px" class="aside">
        <div class="logo">
          <h2>脚本后台管理系统</h2>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          :collapse="isCollapse"
          router
        >
          <el-menu-item index="/admin">
            <el-icon><Odometer /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>
          
          <el-menu-item index="/admin/users">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
          
          <el-menu-item v-if="isSuperAdmin" index="/admin/admins">
            <el-icon><UserFilled /></el-icon>
            <span>管理员管理</span>
          </el-menu-item>
          
          <el-menu-item index="/admin/cards">
            <el-icon><Ticket /></el-icon>
            <span>卡密管理</span>
          </el-menu-item>
          
          <el-menu-item index="/admin/activate">
            <el-icon><CircleCheck /></el-icon>
            <span>激活卡密</span>
          </el-menu-item>
          
          <el-menu-item index="/admin/statistics">
            <el-icon><DataAnalysis /></el-icon>
            <span>统计分析</span>
          </el-menu-item>
          
          <el-menu-item index="/admin/logs">
            <el-icon><Document /></el-icon>
            <span>操作日志</span>
          </el-menu-item>
          
          <el-menu-item index="/admin/announcements">
            <el-icon><Bell /></el-icon>
            <span>公告管理</span>
          </el-menu-item>
          
          <el-menu-item index="/admin/about-us">
            <el-icon><Document /></el-icon>
            <span>关于我们管理</span>
          </el-menu-item>
          
          <!-- 添加系统配置管理菜单 -->
          <el-menu-item index="/admin/system/config">
            <el-icon><Setting /></el-icon>
            <span>系统配置管理</span>
          </el-menu-item>
          
          <el-sub-menu index="distribution">
            <template #title>
              <el-icon><Share /></el-icon>
              <span>分销管理</span>
            </template>
            <el-menu-item index="/admin/distribution/config">
              <el-icon><Setting /></el-icon>
              <span>分销配置</span>
            </el-menu-item>
            <el-menu-item index="/admin/distribution/record">
              <el-icon><DataLine /></el-icon>
              <span>分销记录</span>
            </el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="points" v-if="hasPermission('points')">
            <template #title>
              <el-icon><StarFilled /></el-icon>
              <span>积分管理</span>
            </template>
            <el-menu-item index="/admin/points/users">
              <el-icon><User /></el-icon>
              <span>用户积分管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/points/configs">
              <el-icon><Setting /></el-icon>
              <span>积分配置管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/points/channels">
              <el-icon><Connection /></el-icon>
              <span>积分渠道管理</span>
            </el-menu-item>
          </el-sub-menu>
          
          <!-- 添加提现管理菜单 -->
          <el-sub-menu index="withdrawal">
            <template #title>
              <el-icon><Money /></el-icon>
              <span>提现管理</span>
            </template>
            <el-menu-item index="/admin/withdrawal/requests">
              <el-icon><Document /></el-icon>
              <span>提现申请管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/withdrawal/records">
              <el-icon><Tickets /></el-icon>
              <span>提现记录管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/withdrawal/amounts">
              <el-icon><Coin /></el-icon>
              <span>可提现金额管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/withdrawal/config">
              <el-icon><Setting /></el-icon>
              <span>提现配置管理</span>
            </el-menu-item>
          </el-sub-menu>

          <!-- 推广管理 -->
          <el-sub-menu index="/admin/promotion">
            <template #title>
              <el-icon><Promotion /></el-icon>
              <span>推广管理</span>
            </template>
            <el-menu-item index="/admin/promotions">
              <el-icon><DataBoard /></el-icon>
              <span>推广内容管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/promotion/rank">
              <el-icon><Sort /></el-icon>
              <span>推广排名配置</span>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>
      
      <!-- 主要内容区域 -->
      <el-container>
        <!-- 头部 -->
        <el-header class="header">
          <div class="header-left">
            <el-icon class="collapse-btn" @click="toggleCollapse">
              <Fold v-if="!isCollapse" />
              <Expand v-else />
            </el-icon>
            <Breadcrumb />
          </div>
          
          <div class="header-right">
            <el-dropdown trigger="click">
              <div class="avatar-container">
                <el-avatar :size="30" icon="el-icon-user" />
                <span class="username">{{ username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="navigateToChangePassword">修改密码</el-dropdown-item>
                  <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区域 -->
        <el-main class="main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '../../stores/user'
import { 
  Document, 
  Menu as IconMenu, 
  Location, 
  Setting, 
  User, 
  Bell, 
  Promotion, 
  Histogram, 
  StarFilled,
  Money,
  Connection,
  Tickets,
  Coin,
  DataBoard,
  Sort,
  Odometer,
  UserFilled,
  Ticket,
  CircleCheck,
  DataAnalysis,
  DataLine,
  Share,
  Fold,
  Expand,
  ArrowDown
} from '@element-plus/icons-vue'

// 面包屑数据
const route = useRoute()
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => item.meta.title)
})

// 面包屑组件
const Breadcrumb = {
  setup() {
    return { breadcrumbs }
  },
  template: `
    <el-breadcrumb separator="/">
      <el-breadcrumb-item v-for="(title, index) in breadcrumbs" :key="index">
        {{ title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  `
}

const router = useRouter()
const userStore = useUserStore()
const isCollapse = ref(false)
const username = computed(() => userStore.username || '管理员')

// 判断是否是超级管理员
const isSuperAdmin = computed(() => {
  return localStorage.getItem('adminRole') === '2'
})

// 判断是否有权限
const hasPermission = (permission) => {
  // 这里可以根据实际需求添加权限判断逻辑
  // 目前默认所有用户都有积分管理权限
  return true
}

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 清除用户信息
    userStore.clearUserInfo()
    
    // 跳转到登录页
    router.push('/login')
    
    ElMessage({
      type: 'success',
      message: '已退出登录'
    })
  }).catch(() => {})
}

// 导航到修改密码页面
const navigateToChangePassword = () => {
  router.push('/admin/password')
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.layout-container {
  height: 100%;
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  overflow-x: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #263445;
}

.logo h2 {
  color: #fff;
  margin: 0;
  font-size: 18px;
}

.el-menu-vertical {
  border-right: none;
}

.header {
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  font-size: 20px;
  cursor: pointer;
  margin-right: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.avatar-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 10px;
}

.main {
  padding: 20px;
  background-color: #f5f7fa;
  height: calc(100vh - 60px);
  overflow-y: auto;
}
</style> 
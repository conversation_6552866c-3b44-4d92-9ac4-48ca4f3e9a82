<template>
  <div class="activate-card">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>激活卡密</span>
        </div>
      </template>
      
      <el-form
        ref="activateFormRef"
        :model="activateForm"
        :rules="activateRules"
        label-width="100px"
        class="activate-form"
      >
        <el-form-item label="卡密" prop="keyCode">
          <el-input 
            v-model="activateForm.keyCode" 
            placeholder="请输入卡密" 
            maxlength="16"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="手机号码" prop="phone">
          <el-input 
            v-model="activateForm.phone" 
            placeholder="请输入手机号码" 
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleActivate">激活</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 激活结果 -->
      <div v-if="activateResult.success" class="activate-result">
        <el-divider>激活结果</el-divider>
        
        <el-alert
          :title="activateResult.message"
          type="success"
          :closable="false"
          class="mb-20"
        >
          <template #default>
            <p>手机号码: {{ activateForm.phone }}</p>
            <p>卡密类型: {{ getCardTypeName(activateResult.cardType) }}</p>
            <p>激活时间: {{ activateResult.activateTime }}</p>
            <p>到期时间: {{ activateResult.expireTime }}</p>
          </template>
        </el-alert>
      </div>
    </el-card>
    
    <!-- 卡密说明 -->
    <el-card shadow="hover" class="mt-20">
      <template #header>
        <div class="card-header">
          <span>卡密说明</span>
        </div>
      </template>
      
      <div class="card-types-info">
        <el-table :data="CARD_TYPES" border>
          <el-table-column prop="label" label="卡密类型" width="120" />
          <el-table-column prop="duration" label="有效期" width="120" />
          <el-table-column label="说明">
            <template #default="scope">
              <div class="card-type-desc">
                {{ getCardTypeDescription(scope.row.value) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { activateCard } from '../../api/user'
import { CARD_TYPES, getLabelByValue } from '../../utils/constants'

const activateFormRef = ref(null)
const loading = ref(false)

// 激活表单
const activateForm = reactive({
  keyCode: '',
  phone: ''
})

// 表单验证规则
const activateRules = {
  keyCode: [
    { required: true, message: '请输入卡密', trigger: 'blur' },
    { min: 16, max: 16, message: '卡密长度为16位', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 激活结果
const activateResult = reactive({
  success: false,
  message: '',
  cardType: 0,
  activateTime: '',
  expireTime: ''
})

// 获取卡密类型名称
const getCardTypeName = (type) => {
  return getLabelByValue(CARD_TYPES, type)
}

// 获取卡密类型说明
const getCardTypeDescription = (type) => {
  switch (type) {
    case 1: return '体验卡提供12小时的服务体验，适合新用户试用。'
    case 2: return '天卡提供1天的服务使用权限，适合短期使用。'
    case 3: return '周卡提供7天的服务使用权限，性价比较高。'
    case 4: return '月卡提供30天的服务使用权限，最受欢迎的选择。'
    case 5: return '季卡提供90天的服务使用权限，长期使用更划算。'
    case 6: return '年卡提供365天的服务使用权限，长期用户的理想选择。'
    case 7: return '永久卡提供永久的服务使用权限，一次购买终身使用。'
    default: return ''
  }
}

// 处理激活卡密
const handleActivate = async () => {
  if (!activateFormRef.value) return
  
  await activateFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const res = await activateCard(activateForm)
        
        if (res.code === 200) {
          // 处理激活结果
          activateResult.success = true
          activateResult.message = '卡密激活成功'
          activateResult.cardType = res.data.cardType || 0
          activateResult.activateTime = res.data.activateTime || new Date().toLocaleString()
          activateResult.expireTime = res.data.expireTime || '未知'
          
          ElMessage.success('卡密激活成功')
        } else {
          throw new Error(res.message || '激活失败')
        }
      } catch (error) {
        console.error('激活卡密失败:', error)
        ElMessage.error(error.message || '激活卡密失败，请检查卡密是否正确或已被使用')
        
        // 重置激活结果
        activateResult.success = false
        activateResult.message = ''
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (activateFormRef.value) {
    activateFormRef.value.resetFields()
  }
  
  // 重置激活结果
  activateResult.success = false
  activateResult.message = ''
}
</script>

<style scoped>
.activate-card {
  padding: 20px;
}

.activate-form {
  max-width: 500px;
}

.activate-result {
  margin-top: 30px;
}

.card-types-info {
  margin-bottom: 20px;
}

.card-type-desc {
  line-height: 1.5;
  color: #606266;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}
</style> 
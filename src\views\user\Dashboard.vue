<template>
  <div class="user-dashboard">
    <el-row :gutter="20">
      <!-- 卡密状态卡片 -->
      <el-col :span="16">
        <el-card shadow="hover" class="card-status">
          <template #header>
            <div class="card-header">
              <span>卡密状态</span>
              <el-button v-if="!cardStatus.hasActiveCard" type="primary" @click="goToActivate">
                激活卡密
              </el-button>
            </div>
          </template>
          
          <div v-if="cardStatus.hasActiveCard" class="card-info">
            <div class="card-type">
              <el-tag :type="getCardTypeTag(cardStatus.cardInfo.cardType)" size="large">
                {{ getCardTypeName(cardStatus.cardInfo.cardType) }}
              </el-tag>
            </div>
            
            <div class="card-details">
              <div class="detail-item">
                <span class="detail-label">激活时间：</span>
                <span>{{ cardStatus.cardInfo.activateTime }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">到期时间：</span>
                <span>{{ cardStatus.cardInfo.expireTime }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">剩余天数：</span>
                <span :class="{'text-danger': cardStatus.cardInfo.remainDays < 7}">
                  {{ cardStatus.cardInfo.remainDays }}天
                </span>
              </div>
              
              <el-progress 
                :percentage="getProgressPercentage()" 
                :status="getProgressStatus()"
                :stroke-width="15"
                class="card-progress"
              />
            </div>
          </div>
          
          <div v-else class="no-card">
            <el-empty description="您还没有激活任何卡密">
              <el-button type="primary" @click="goToActivate">立即激活</el-button>
            </el-empty>
          </div>
        </el-card>
      </el-col>
      
      <!-- 用户信息卡片 -->
      <el-col :span="8">
        <el-card shadow="hover" class="user-info">
          <template #header>
            <div class="card-header">
              <span>用户信息</span>
              <el-button type="primary" link @click="goToProfile">编辑资料</el-button>
            </div>
          </template>
          
          <div class="user-info-content">
            <div class="user-avatar">
              <el-avatar :size="80" icon="el-icon-user" />
            </div>
            
            <div class="user-details">
              <div class="detail-item">
                <span class="detail-label">用户名：</span>
                <span>{{ userInfo.username }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">邮箱：</span>
                <span>{{ userInfo.email }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">手机：</span>
                <span>{{ userInfo.phone }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">注册时间：</span>
                <span>{{ userInfo.createTime }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 快速导航 -->
      <el-col :span="24">
        <el-card shadow="hover" class="quick-nav">
          <template #header>
            <div class="card-header">
              <span>快速导航</span>
            </div>
          </template>
          
          <div class="nav-buttons">
            <el-button type="primary" @click="goToActivate">
              <el-icon><el-icon-check /></el-icon>
              激活卡密
            </el-button>
            
            <el-button type="success" @click="goToProfile">
              <el-icon><el-icon-user /></el-icon>
              个人资料
            </el-button>
            
            <el-button type="warning" @click="goToPromotionRank">
              <el-icon><el-icon-data-analysis /></el-icon>
              推广排名
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 使用记录 -->
    <el-card shadow="hover" class="mt-20">
      <template #header>
        <div class="card-header">
          <span>使用记录</span>
        </div>
      </template>
      
      <el-table :data="usageRecords" stripe style="width: 100%">
        <el-table-column prop="cardType" label="卡密类型" width="120">
          <template #default="scope">
            <el-tag :type="getCardTypeTag(scope.row.cardType)">
              {{ getCardTypeName(scope.row.cardType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="cardCode" label="卡密" width="180" />
        <el-table-column prop="activateTime" label="激活时间" width="180" />
        <el-table-column prop="expireTime" label="到期时间" width="180" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '使用中' : '已过期' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserInfo } from '../../api/user'
import { getCardStatus } from '../../api/user'
import { CARD_TYPES } from '../../utils/constants'

const router = useRouter()
const userId = localStorage.getItem('userId')

// 用户信息
const userInfo = reactive({
  username: '',
  email: '',
  phone: '',
  createTime: ''
})

// 卡密状态
const cardStatus = reactive({
  hasActiveCard: false,
  cardInfo: {
    cardType: 0,
    typeName: '',
    activateTime: '',
    expireTime: '',
    remainDays: 0
  }
})

// 模拟使用记录数据
const usageRecords = ref([
  {
    cardType: 4,
    cardCode: 'AB12CD34EF56GH78',
    activateTime: '2023-06-01 12:00:00',
    expireTime: '2023-07-01 12:00:00',
    status: 1
  },
  {
    cardType: 3,
    cardCode: 'IJ90KL12MN34OP56',
    activateTime: '2023-05-01 12:00:00',
    expireTime: '2023-05-08 12:00:00',
    status: 0
  }
])

// 获取卡密类型名称
const getCardTypeName = (type) => {
  const cardType = CARD_TYPES.find(item => item.value === type)
  return cardType ? cardType.label : '未知'
}

// 获取卡密类型标签类型
const getCardTypeTag = (type) => {
  switch (type) {
    case 1: return 'info'
    case 2: return 'warning'
    case 3: return 'success'
    case 4: return 'primary'
    case 5: return ''
    case 6: return 'danger'
    case 7: return 'danger'
    default: return 'info'
  }
}

// 获取进度条百分比
const getProgressPercentage = () => {
  if (!cardStatus.hasActiveCard) return 0
  
  // 永久卡
  if (cardStatus.cardInfo.cardType === 7) return 100
  
  const cardType = CARD_TYPES.find(item => item.value === cardStatus.cardInfo.cardType)
  if (!cardType) return 0
  
  let totalDays = 0
  switch (cardStatus.cardInfo.cardType) {
    case 1: totalDays = 0.5; break // 12小时
    case 2: totalDays = 1; break // 天卡
    case 3: totalDays = 7; break // 周卡
    case 4: totalDays = 30; break // 月卡
    case 5: totalDays = 90; break // 季卡
    case 6: totalDays = 365; break // 年卡
    default: totalDays = 0
  }
  
  if (totalDays === 0) return 0
  
  // 计算剩余百分比
  const percentage = (cardStatus.cardInfo.remainDays / totalDays) * 100
  return Math.min(Math.max(percentage, 0), 100)
}

// 获取进度条状态
const getProgressStatus = () => {
  if (!cardStatus.hasActiveCard) return ''
  
  // 永久卡
  if (cardStatus.cardInfo.cardType === 7) return 'success'
  
  if (cardStatus.cardInfo.remainDays <= 0) return 'exception'
  if (cardStatus.cardInfo.remainDays < 7) return 'warning'
  return 'success'
}

// 页面导航方法
const goToActivate = () => {
  router.push('/user/activate')
}

const goToProfile = () => {
  router.push('/user/profile')
}

const goToPromotionRank = () => {
  router.push('/user/promotion/rank')
}

// 获取用户信息
const fetchUserInfo = async () => {
  if (!userId) return
  
  try {
    const res = await getUserInfo(userId)
    Object.assign(userInfo, res.data)
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 获取卡密状态
const fetchCardStatus = async () => {
  if (!userId) return
  
  try {
    const res = await getCardStatus(userId)
    Object.assign(cardStatus, res.data)
  } catch (error) {
    console.error('获取卡密状态失败:', error)
    ElMessage.error('获取卡密状态失败')
    
    // 使用模拟数据
    cardStatus.hasActiveCard = true
    cardStatus.cardInfo = {
      cardType: 4,
      typeName: '月卡',
      activateTime: '2023-06-01 12:00:00',
      expireTime: '2023-07-01 12:00:00',
      remainDays: 25
    }
  }
}

onMounted(() => {
  fetchUserInfo()
  fetchCardStatus()
})
</script>

<style scoped>
.user-dashboard {
  padding: 20px;
}

.card-status {
  height: 100%;
}

.card-info {
  display: flex;
  align-items: center;
  padding: 20px 0;
}

.card-type {
  margin-right: 30px;
}

.card-details {
  flex: 1;
}

.detail-item {
  margin-bottom: 10px;
  display: flex;
}

.detail-label {
  color: #909399;
  width: 80px;
}

.card-progress {
  margin-top: 20px;
}

.no-card {
  padding: 40px 0;
}

.user-info {
  height: 100%;
}

.user-info-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-avatar {
  margin-bottom: 20px;
}

.user-details {
  width: 100%;
}

.text-danger {
  color: #F56C6C;
}

.quick-nav {
  margin-top: 20px;
}

.nav-buttons {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 15px;
}
</style> 
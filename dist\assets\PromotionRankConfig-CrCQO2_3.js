import{E as j,F as I,G}from"./admin-CZaj3CWx.js";import{_ as L,b as p,d as x,g as S,O as $,c as f,a as t,w as o,r as n,E as _,o as g,e as r,f as m,L as T,N as z}from"./index-DKz5EXvV.js";import"./request-YZgFExiB.js";const A={class:"promotion-rank-config"},J={key:0,class:"loading-container"},K={key:1},Q={key:0,class:"empty-config"},W={key:1,class:"config-info"},X={class:"config-header"},Z={class:"action-buttons"},ee={class:"dialog-footer"},te={__name:"PromotionRankConfig",setup(oe){const k=p(!0),w=p(!1),a=x({id:null,startTime:"",endTime:"",description:""}),M=S(()=>a.id!==null),l=x({id:null,startTime:"",endTime:"",description:""}),h={startTime:[{required:!0,message:"请选择开始时间",trigger:"blur"}],endTime:[{required:!0,message:"请选择结束时间",trigger:"blur"},{validator:(s,e,y)=>{e&&l.startTime&&new Date(e)<=new Date(l.startTime)?y(new Error("结束时间必须晚于开始时间")):y()},trigger:"blur"}],description:[{required:!0,message:"请输入活动说明",trigger:"blur"},{min:5,max:200,message:"活动说明长度应在5到200个字符之间",trigger:"blur"}]},u=p(null),d=p(!1),v=p(!1),V=async()=>{k.value=!0;try{const s=await j();s.data?Object.assign(a,s.data):(a.id=null,a.startTime="",a.endTime="",a.description="")}catch(s){console.error("获取推广排名配置失败:",s),_.error("获取推广排名配置失败")}finally{k.value=!1}},E=()=>{v.value=!1,C(),d.value=!0},H=()=>{v.value=!0,C(),Object.assign(l,a),d.value=!0},R=()=>{z.confirm("确定要删除当前推广排名配置吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await I(a.id),_.success("删除成功"),V()}catch(s){console.error("删除推广排名配置失败:",s),_.error("删除推广排名配置失败")}}).catch(()=>{})},B=async()=>{u.value&&await u.value.validate(async s=>{if(s){w.value=!0;try{const e={...l};await G(e),_.success(v.value?"更新成功":"创建成功"),d.value=!1,V()}catch(e){console.error("保存推广排名配置失败:",e),_.error("保存推广排名配置失败")}finally{w.value=!1}}})},C=()=>{l.id=null,l.startTime="",l.endTime="",l.description="",u.value&&u.value.resetFields()};return $(()=>{V()}),(s,e)=>{const y=n("el-skeleton"),c=n("el-button"),P=n("el-empty"),b=n("el-descriptions-item"),F=n("el-descriptions"),N=n("el-card"),D=n("el-date-picker"),Y=n("el-form-item"),U=n("el-input"),q=n("el-form"),O=n("el-dialog");return g(),f("div",A,[t(N,{shadow:"hover"},{header:o(()=>e[5]||(e[5]=[m("div",{class:"card-header"},[m("span",null,"推广排名配置")],-1)])),default:o(()=>[k.value?(g(),f("div",J,[t(y,{rows:5,animated:""})])):(g(),f("div",K,[M.value?(g(),f("div",W,[m("div",X,[e[9]||(e[9]=m("h3",null,"当前推广排名活动配置",-1)),m("div",Z,[t(c,{type:"primary",onClick:H},{default:o(()=>e[7]||(e[7]=[r("编辑")])),_:1,__:[7]}),t(c,{type:"danger",onClick:R},{default:o(()=>e[8]||(e[8]=[r("删除")])),_:1,__:[8]})])]),t(F,{column:1,border:""},{default:o(()=>[t(b,{label:"活动ID"},{default:o(()=>[r(T(a.id),1)]),_:1}),t(b,{label:"开始时间"},{default:o(()=>[r(T(a.startTime),1)]),_:1}),t(b,{label:"结束时间"},{default:o(()=>[r(T(a.endTime),1)]),_:1}),t(b,{label:"活动说明"},{default:o(()=>[r(T(a.description),1)]),_:1})]),_:1})])):(g(),f("div",Q,[t(P,{description:"暂无推广排名活动配置"},{default:o(()=>[t(c,{type:"primary",onClick:E},{default:o(()=>e[6]||(e[6]=[r("创建配置")])),_:1,__:[6]})]),_:1})]))]))]),_:1}),t(O,{modelValue:d.value,"onUpdate:modelValue":e[4]||(e[4]=i=>d.value=i),title:v.value?"编辑推广排名配置":"创建推广排名配置",width:"500px"},{footer:o(()=>[m("span",ee,[t(c,{onClick:e[3]||(e[3]=i=>d.value=!1)},{default:o(()=>e[10]||(e[10]=[r("取消")])),_:1,__:[10]}),t(c,{type:"primary",onClick:B,loading:w.value},{default:o(()=>e[11]||(e[11]=[r(" 确认 ")])),_:1,__:[11]},8,["loading"])])]),default:o(()=>[t(q,{ref_key:"formRef",ref:u,model:l,rules:h,"label-width":"100px","label-position":"right"},{default:o(()=>[t(Y,{label:"开始时间",prop:"startTime"},{default:o(()=>[t(D,{modelValue:l.startTime,"onUpdate:modelValue":e[0]||(e[0]=i=>l.startTime=i),type:"datetime",placeholder:"选择开始时间",style:{width:"100%"},"value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(Y,{label:"结束时间",prop:"endTime"},{default:o(()=>[t(D,{modelValue:l.endTime,"onUpdate:modelValue":e[1]||(e[1]=i=>l.endTime=i),type:"datetime",placeholder:"选择结束时间",style:{width:"100%"},"value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(Y,{label:"活动说明",prop:"description"},{default:o(()=>[t(U,{modelValue:l.description,"onUpdate:modelValue":e[2]||(e[2]=i=>l.description=i),type:"textarea",rows:3,placeholder:"请输入活动说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},se=L(te,[["__scopeId","data-v-113adb56"]]);export{se as default};
